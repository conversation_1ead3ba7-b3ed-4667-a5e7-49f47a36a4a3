/**
 * @description eat chess effect
 */

import { Component, Node, SpriteAtlas, SpriteFrame, Texture2D, v3, _decorator } from "cc";
import { BasePanel } from "../framework/basePanel";
import { EventMgr } from "../framework/eventMgr";
import { EventName } from "../framework/eventName";
import SuperPanel from "../framework/superPanel";
import ActionManager from "../manager/action";
import { commonUtil } from "../utils/commonUtil";
import { convertWorldCoordinate } from "../utils/utils";

const { ccclass, property } = _decorator;

@ccclass("EatChessManager")
export default class EatChessManager extends BasePanel {

    @property(Texture2D)
    eatChessTexture2D

    onMessageEvent() {
        super.onMessageEvent();
        EventMgr.on(EventName.UPATE_EAT_CHESS_ANIM, this, this.playEatAnimation);
    }

    offMessageEvent() {
        super.offMessageEvent();
        EventMgr.off(EventName.UPATE_EAT_CHESS_ANIM, this, this.playEatAnimation);
    }

    public async playEatAnimation(chessNode: Node) {
        if (chessNode && this.node.isValid) {
            const { x, y } = convertWorldCoordinate(chessNode)
            const newVec = commonUtil.convertToNodeSpaceAR(this.node.parent, v3(x, y));
            this.node.setPosition(v3(newVec.x, newVec.y, 0))
            await ActionManager.playSpriteFrameAnimation(this.node, this.eatChessTexture2D, 250, 1000 / 14, 1)
        }
    }

}
