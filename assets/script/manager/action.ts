/**
 * 动画行为管理器
 */

import { Animation, Node, ProgressBar, Sprite, SpriteFrame, Texture2D, tween, v3, Vec3 } from 'cc'
import { largeScaleRate } from '../gameConfig'
import { FacialType } from '../type'
import { ELayerIndex } from '../type/enum'
import { commonUtil } from '../utils/commonUtil'
import { getTextureFrame } from '../utils/utils'
import { AudioMgr } from './audioMgr'
import ChessManager from "./chess"
import ChessDialogManager from './chessDialog'

export default class ActionManager {

  /**
   * 棋子移动
   * @param node 棋子节点 
   * @param ver3Data 目标position
   * @param isLastMove 是否最后一个移动
   * @returns 
   */
  public static chessMove(node: Node, ver3Data: Vec3, isLastMove: boolean, disableAudio?: boolean) {
    return new Promise((resolve) => {
      if (node) {
        if (isLastMove) {
          tween(node).to(.001, { scale: commonUtil.numToV3(largeScaleRate) })
            .call(() => {
              if (!disableAudio) {
                AudioMgr.instance.play("audio/sound_pieceMove")
              }
            })
            .to(.1, { position: ver3Data })
            .to(.22, { scale: Vec3.ONE }).call(() => {
              resolve('')
            }).start()
        } else {
          tween(node).to(.001, { scale: commonUtil.numToV3(largeScaleRate) }).call(() => {
            if (!disableAudio) {
              AudioMgr.instance.play("audio/sound_pieceMove")
            }
          }).to(.16, { position: ver3Data }).call(() => {
            resolve('')
          }).start()
        }
      } else {
        resolve('')
      }
    })
  }

  // 棋子缩放
  public static eatChessMove(node: Node, ver3Data: Vec3) {
    // cs.log("-------------------eatChessMove", new Date().getTime());
    return new Promise((resolve) => {
      tween(node)
        .call(() => {
          AudioMgr.instance.play("audio/sound_pieceMove");
        })
        .to(.2, { position: ver3Data, scale: commonUtil.numToV3(largeScaleRate) })
        .to(.1, { position: ver3Data, scale: commonUtil.numToV3(largeScaleRate * 1.3) })
        .to(.1, { scale: Vec3.ONE })
        .call(() => {
          resolve('')
        })
        .start()
    })
  }

  // 棋子返回原点
  public static backBirthPlace(beEatNode: Node, eatNode?: Node, isEat?: boolean) {
    return new Promise((resolve) => {
      if (beEatNode?.isValid) {
        const chessVerc = ChessManager.getOriginOrEndLocation(beEatNode)
        if (!chessVerc) resolve('')
        else {
          const { x, y } = chessVerc
          // beEatNode.scale = commonUtil.numToV3(largeScaleRate);
          // beEatNode.setPosition(v3(x, y, 0))
          tween(beEatNode)
            .to(.3, { scale: commonUtil.numToV3(largeScaleRate), position: v3(x, y, 0) })
            .to(.2, {})
            .call(() => {
              if (isEat && eatNode?.isValid) {
                eatNode.getComponent(ChessDialogManager).showFacialContent(FacialType.Happy)
              }
              commonUtil.setIndex(beEatNode, ELayerIndex.Sad)
              beEatNode.getComponent(ChessDialogManager).showFacialContent(FacialType.Sad)
              resolve('')
            })
            .start()
        }
      } else {
        resolve('')
      }
    })
  }
  /**
   * @name 倒计时
   * @param cheesType
   * @param pendingTime
   * @returns
   */
  public static playCountdown(progressComp: ProgressBar, pendingTime: number, callback?: (dt: number) => any) {
    return new Promise((resolve) => {
      try {
        if (!progressComp) resolve('')
        if (!pendingTime) {
          progressComp.progress = 0
          resolve('')
        } else {
          let lastTime = 0
          const workContdown = (dt: number) => {
            if (!lastTime) lastTime = dt
            progressComp.progress -= (dt - lastTime) / pendingTime
            lastTime = dt
            callback && callback(dt)
            if (progressComp.progress > 0) {
              window?.requestAnimationFrame(workContdown)
            } else {
              resolve('')
            }
          }
          progressComp.progress = 1
          window?.requestAnimationFrame(workContdown)
        }
      } catch (e) {
        resolve('')
      }
    })
  }

  /**
   * @zh 播放动画 @en 播放动画
   */
  public static playAnimation(node: Node, animName: string, repeatCount?: number) {
    return new Promise((reslove) => {
      const anim = node?.getComponent(Animation)
      if (!anim) return reslove('')
      const stopAnimation = () => {
        anim.off(Animation.EventType.FINISHED, stopAnimation, this)
        reslove('')
      }
      anim.on(Animation.EventType.FINISHED, stopAnimation, this)
      anim.play(animName)
      const animState = anim.getState(animName);
      animState && (animState.repeatCount = repeatCount || 1)
    })
  }

  /**
   *  @zh 播放长贴图动画 @en play frame animation
   * @param node 当前贴图节点
   * @param texture2D 贴图
   * @param w1  贴图宽度
   * @param fpsRate 帧率
   * @param repeatCount 重复次数
   * @returns 
   */
  public static playSpriteFrameAnimation(node: Node, texture2D: Texture2D, w1: number, fpsRate: number, repeatCount: number, isStopCallback?: () => boolean) {
    return new Promise((reslove) => {
      const targetSf = node?.getComponent(Sprite)
      if (!targetSf || !texture2D) return reslove(null)
      const { width, height } = texture2D
      const endFrameIndex = width / w1
      if (!endFrameIndex) return reslove(null)

      let newFrame: SpriteFrame = null
      let curIndex: number = 0
      let curPlayCount = 0
      let work = function () {
        if (!node.isValid) return
        const resetAllValue = () => {
          newFrame = null
          work = null
          targetSf.spriteFrame = null
        }
        if (isStopCallback?.()) {
          resetAllValue()
          return reslove(null)
        }
        if (curIndex >= endFrameIndex) {
          curIndex = 0
          ++curPlayCount
          if (repeatCount >= 0 && repeatCount <= curPlayCount) {
            resetAllValue()
            return reslove(null)
          }
        }
        newFrame = getTextureFrame(texture2D, curIndex * w1, w1, height)
        targetSf.spriteFrame = newFrame
        curIndex++
        setTimeout(work, fpsRate)
      }
      work()
    })
  }


  /**
   * @zh 播放长贴图动画 @en play frame animation
   */
  public static playSpriteFrameAnimation1(node: Node, texture2D: Texture2D, w1: number, fpsRate: number, repeatCount: number) {
    return new Promise((reslove) => {
      const targetSf = node?.getComponent(Sprite)
      if (!targetSf || !texture2D || repeatCount <= 0) return reslove(null)
      const { width, height } = texture2D
      const endFrameIndex = Math.floor(width / w1);
      if (!endFrameIndex) return reslove(null)
      let newFrame: SpriteFrame = null
      let curIndex: number = 0
      let curPlayCount = 0
      let work = function () {
        if (!node?.isValid) return
        if (curIndex >= endFrameIndex) {
          curIndex = 0
          ++curPlayCount
          if (repeatCount <= curPlayCount) {
            newFrame = null
            work = null
            targetSf.spriteFrame = null
            return reslove(null)
          }
        }
        newFrame = getTextureFrame(texture2D, curIndex * w1, w1, height)
        targetSf.spriteFrame = newFrame
        curIndex++
        setTimeout(work, fpsRate)
      }
      work()
    })
  }



}
