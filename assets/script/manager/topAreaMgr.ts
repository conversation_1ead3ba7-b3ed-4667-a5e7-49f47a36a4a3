import globalData from "../globalData";
import { GAME_MODE } from "../network/constants";

export class TopAreaMgr {
    private static _ins: TopAreaMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new TopAreaMgr();
        return this._ins;
    }

    private _itemFristId: any = null;
    private _itemSecondId: any = null;
    public get itemSecondId(): any {
        return this._itemSecondId;
    }
    public set itemSecondId(value: any) {
        this._itemSecondId = value;
    }

    public get userId() {
        return globalData.myUserId
    }

    public spMap = null;

    public get itemFristId() {
        return this._itemFristId;
    }

    public set itemFristId(data: any) {
        this._itemFristId = data
    }

    // TopAreaMgr.instance.itemFristId 如果存在的话 
    public getUserCoinInfoByIdx(idx: number) {
        const list = this.getCurList();
        return list[idx];
    }

    /**
     * 获取当前list
     * @returns 
     */
    private getCurList() {
        let list = globalData.gameInfo.userCoinInfo;
        if (!list) return [];

        if (globalData?.gameInfo?.gameMode == GAME_MODE.TWO_VS_TWO) {
            const temp = list.filter(item => item.userId == this.userId)
            if (temp && temp.length > 0) {
                list.sort((a, b) => {
                    if (a.userId == this.userId) {
                        return -1;
                    }

                    if (b.userId == this.userId) {
                        return 1;
                    }

                    if (a.teamName == temp[0].teamName) {
                        return -1;
                    }

                    return 1;
                });
            }
        }
        let curList = [];
        let otherList = [];
        let itemData: any = null;
        let itemDataSecond: any = null;
        for (let i = 0; i < list.length; i++) {
            if (String(list[i].userId) != String(this.userId)) {
                if (this.itemFristId) {
                    if (String(this.itemFristId) === String(list[i].userId)) {
                        itemData = list[i];
                    } else if (String(this.itemSecondId) === String(list[i].userId)) {
                        itemDataSecond = list[i];
                    } else {
                        otherList.push(list[i]);
                    }
                } else {
                    curList.push(list[i])
                }
            }
        }
        if (this.itemSecondId) {
            if (itemDataSecond) {
                otherList.unshift(itemDataSecond)
            }
        }
        if (this.itemFristId) {
            if (itemData) {
                otherList.unshift(itemData)
                curList = otherList;
            } else {
                curList = otherList;
            }
        }
        return curList;
    }

    /**
     * 获取自己的数据
     * @returns 
     */
    public getSelfData() {
        let list = globalData.gameInfo.userCoinInfo;
        if (!list) return null;
        for (let i = 0; i < list.length; i++) {
            if (String(list[i].userId) == String(this.userId)) {
                return list[i]
            }
        }
        return null;
    }


    //  经典模式

    /*
    *    获取第一名数据
    */
    public getFirstPlace() {
        let list = globalData.gameInfo.modeData.gameReward
        return list.find(item => item.rank === 1)
    }

    /*
        获取第二名数据
    */
    public getSecondPlace() {
        let list = globalData.gameInfo.modeData.gameReward
        return list.find(item => item.rank === 2)
    }

    /*
     当前游戏模式
    */
    public getGameMode() {
        return globalData.gameInfo.gameMode
    }

}