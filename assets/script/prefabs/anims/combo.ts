import { _decorator, Node, Texture2D, Sprite, tween, v3 } from 'cc';
import { BasePanel } from '../../framework/basePanel';
import { EventMgr } from '../../framework/eventMgr';
import { EventName } from '../../framework/eventName';
import ActionManager from '../../manager/action';
import { AudioMgr } from '../../manager/audioMgr';
import { commonUtil } from '../../utils/commonUtil';
const { ccclass, property } = _decorator;

@ccclass('combo')
export class combo extends BasePanel {
    @property(Texture2D)
    comboTexture2D

    @property(Node)
    iconNode

    @property(Node)
    labelNode

    private comboLabel = 'Combo           × '

    onMessageEvent() {
        super.onMessageEvent();
        EventMgr.on(EventName.EVENT_EAT_CHESS_COMBO_ANIM, this, this.playComboAnimation);
    }

    offMessageEvent() {
        super.offMessageEvent();
        EventMgr.off(EventName.EVENT_EAT_CHESS_COMBO_ANIM, this, this.playComboAnimation);
    }

    public async playComboAnimation(combo: number) {
        if (combo > 1) {
            this.tools.text("label", `${this.comboLabel}${combo}`)
            AudioMgr.instance.play('audio/sound_match')
            this._playLabelAnimation()
            await ActionManager.playSpriteFrameAnimation(this.iconNode, this.comboTexture2D, 500, 1000 / 24, 1)
        }
    }

    private _playLabelAnimation() {
        tween(this.labelNode)
            .to(.3, { scale: commonUtil.getEqualVec3(1) })
            .to(.625, { scale: commonUtil.getEqualVec3(1.2) })
            .call(() => {
                commonUtil.setScale(this.labelNode, 0)
            })
            .start()
    }
}
