import { _decorator, sys } from "cc";
import { cs } from "./log";

const { ccclass, property } = _decorator;

/**
 * @content 缓存
 * <AUTHOR>
 */
@ccclass
export class LocalstorageMgr {
    private static _ins: LocalstorageMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new LocalstorageMgr();
        return this._ins;
    }

    /**
     * 设置缓存数据,长久保存 localStorage
     * @param key 
     * @param value 
     */
    public setItem(key: string, value: any) {
        let val = JSON.stringify(value);
        sys.localStorage.setItem(key, val);
    }

    /**
     * 获取缓存数据
     * @param key 
     * @returns 
     */
    public getItem(key: string) {
        let result = sys.localStorage.getItem(key);
        if (!result && typeof result !== "string") return null;
        return JSON.parse(result);
    }

    /**
     * 删除item
     * @param key 
     * @returns 
     */
    public removeItem(key: string) {
        sys.localStorage.removeItem(key);
    }

    /**
     * 删除所有值
     */
    public clear() {
        cs.log(" localStorage clear !");
        sys.localStorage.clear();
    }

    /**
     * sessionStorage 采用临时存储 
     * @param key 
     * @param value 
     * @returns 
     */
    public sessioSetItem(key: string, value: any) {
        let val = JSON.stringify(value);
        sessionStorage.setItem(key, val);
    }

    /**
     * 通过key获取键值
     * @param key 
     * @returns 
     */
    public sessioGetItem(key: string) {
        let value = sessionStorage.getItem(key);
        if (!value && typeof value !== "string") return null;
        value = JSON.parse(value);
        return value;
    }

    /**
     * 删除item
     * @param key 
     */
    public sessionRemoveItem(key: string) {
        sessionStorage.removeItem(key);
    }

    /**
     * 清除
     */
    public sessionClear() {
        cs.log( "sessionClear !");
        sessionStorage.clear();
    }

}


