# Seal Ludo 项目架构分析

## 目录
- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [架构设计](#架构设计)
- [核心模块分析](#核心模块分析)
- [棋盘格子坐标系统](#棋盘格子坐标系统)
- [数据流和业务流程](#数据流和业务流程)
- [关键代码实现](#关键代码实现)
- [配置和构建](#配置和构建)
- [项目运行指南](#项目运行指南)
- [项目特点和优势](#项目特点和优势)
- [潜在改进点](#潜在改进点)

## 项目概述

**Seal Ludo** 是一个基于 Cocos Creator 3.6.1 开发的多人在线飞行棋游戏。项目采用 TypeScript 开发，支持 iOS 和 Android 双平台，通过 WebSocket 实现实时多人游戏同步，并通过 Seal 桥接层与客户端 APP 进行深度集成。

### 基本信息
- **项目名称**: seal-ludo
- **版本**: v2.4.0(2023030101)
- **开发工具**: Cocos Creator 3.6.1
- **编程语言**: TypeScript
- **网络通信**: WebSocket
- **支持平台**: iOS、Android
- **仓库地址**: https://gitlab.lizhi.fm/lz-game/seal-ludo.git

## 技术栈

### 核心技术
- **游戏引擎**: Cocos Creator 3.6.1
- **编程语言**: TypeScript
- **网络通信**: WebSocket + HTTP (axios)
- **构建工具**: Cocos Creator 内置构建系统
- **版本控制**: Git

### 引擎模块配置
```json
{
  "启用模块": ["2d", "animation", "audio", "base", "gfx-webgl", "gfx-webgl2", 
              "intersection-2d", "particle-2d", "tween", "ui"],
  "禁用模块": ["3d", "physics", "skeletal-animation", "xr", "particle", 
              "terrain", "tiled-map", "spine", "dragon-bones"]
}
```

### 平台支持
- **iOS**: Metal 渲染后端，支持 iOS 12.0+
- **Android**: GLES3 渲染后端，API Level 30，支持 arm64-v8a 和 armeabi-v7a

## 架构设计

### 整体架构图

```mermaid
graph TB
    A[客户端APP] --> B[Seal桥接层]
    B --> C[场景层]
    C --> D[管理器层]
    D --> E[框架层]
    D --> F[网络层]
    
    subgraph "场景层 Scene Layer"
        C1[gameHall.ts - 游戏大厅]
        C2[ludo.ts - 游戏场景]
    end
    
    subgraph "管理器层 Manager Layer"
        D1[gameMgr - 游戏管理]
        D2[view - 视图管理]
        D3[chess - 棋子管理]
        D4[audioMgr - 音频管理]
        D5[wsMgr - WebSocket管理]
        D6[syncCommand - 同步指令]
    end
    
    subgraph "框架层 Framework Layer"
        E1[uiMgr - UI管理]
        E2[loadMgr - 资源加载]
        E3[eventMgr - 事件系统]
        E4[poolMgr - 对象池]
        E5[i18n - 国际化]
    end
    
    subgraph "网络层 Network Layer"
        F1[wsClient - WebSocket客户端]
        F2[wsUtil - 网络工具]
        F3[constants - 网络常量]
    end
    
    subgraph "桥接层 Bridge Layer"
        B1[SealBridge - 主桥接]
        B2[exportAPI - 导出接口]
        B3[bridge - 桥接工具]
    end
    
    C --> C1
    C --> C2
    D --> D1
    D --> D2
    D --> D3
    D --> D4
    D --> D5
    D --> D6
    E --> E1
    E --> E2
    E --> E3
    E --> E4
    E --> E5
    F --> F1
    F --> F2
    F --> F3
    B --> B1
    B --> B2
    B --> B3
```

### 分层架构详解

#### 1. 桥接层 (Bridge Layer)
**位置**: `assets/script/seal/`
**职责**: 与外部客户端 APP 的交互接口

桥接层是 Seal Ludo 项目的核心特色，它实现了游戏与 Seal 客户端 APP 的深度集成。通过这一层，游戏可以调用客户端的原生功能，同时响应客户端的各种事件和指令。

##### 桥接层架构图

```mermaid
graph TB
    subgraph "Seal 客户端 APP"
        APP[原生客户端应用]
        SDK[Seal SDK]
        NATIVE[原生功能模块]

        subgraph "原生功能"
            MIC[麦克风控制]
            SPEAKER[扬声器控制]
            STORAGE[本地存储]
            ANALYTICS[数据分析]
            PAYMENT[支付功能]
            SOCIAL[社交功能]
        end
    end

    subgraph "桥接通信层"
        BRIDGE_COMM[native.bridge 通信接口]
        JSON_PARSER[JSON 消息解析器]
        CALLBACK_MGR[回调管理器]
    end

    subgraph "游戏桥接层 Bridge Layer"
        subgraph "SealBridge 核心"
            SEAL_BRIDGE[SealBridge 单例]
            CALL_NATIVE[_callNative 方法]
            EVENT_HANDLER[事件处理器]
            CALLBACK_STORE[回调存储]
        end

        subgraph "ExportAPI 接口层"
            EXPORT_API[ExportAPI 主类]
            GAME_LOGIC[GameInterFaceManager]
            EVENT_LISTENERS[事件监听器集合]
        end

        subgraph "Bridge 工具层"
            SYSTEM_API[系统信息 API]
            GAME_API[游戏状态 API]
            USER_API[用户交互 API]
            ANALYTICS_API[数据分析 API]
        end
    end

    subgraph "游戏核心层"
        GAME_CORE[游戏核心逻辑]
        SCENE_MGR[场景管理器]
        VIEW_MGR[视图管理器]
        GLOBAL_DATA[全局数据]
    end

    %% 通信流向
    APP --> SDK
    SDK --> BRIDGE_COMM
    BRIDGE_COMM --> JSON_PARSER
    JSON_PARSER --> SEAL_BRIDGE

    %% 桥接层内部连接
    SEAL_BRIDGE --> CALL_NATIVE
    SEAL_BRIDGE --> EVENT_HANDLER
    SEAL_BRIDGE --> CALLBACK_STORE

    EXPORT_API --> SEAL_BRIDGE
    SYSTEM_API --> SEAL_BRIDGE
    GAME_API --> SEAL_BRIDGE
    USER_API --> SEAL_BRIDGE
    ANALYTICS_API --> SEAL_BRIDGE

    %% 与游戏核心的连接
    EXPORT_API --> GAME_CORE
    SYSTEM_API --> GLOBAL_DATA
    GAME_API --> SCENE_MGR
    USER_API --> VIEW_MGR
```

##### 核心组件详解

**1. SealBridge - 核心桥接类**
```typescript
class SealBridge {
  private static instance: SealBridge
  private callbackID = 1000
  private callbacks: { [key: string]: TCallback } = {}
  private registerHandlers: { [key: string]: TCallback[] } = {}
  readonly isSeal: boolean = isSeal
  readonly debug: boolean = Boolean(getUrlParam1('debug'))
}
```

**主要功能**:
- **单例模式**: 确保全局唯一的桥接实例
- **双向通信**: 支持游戏主动调用客户端和被动响应客户端事件
- **回调管理**: 管理异步调用的回调函数
- **事件监听**: 注册和管理客户端事件监听器
- **环境检测**: 自动检测是否在 Seal 客户端环境中运行

**核心方法**:
```typescript
// 主动调用客户端方法
public call(name: TNativeMethod, params: unknown, callback?: TCallback)

// 监听客户端事件
public on(name: sealCallEvents, callback: TCallback)

// 响应客户端回调
public h5Callback(__callback_id, params)

// 解绑事件监听
public off(name: sealCallEvents, callback: TCallback)
```

**2. ExportAPI - 导出接口管理**
```typescript
export default class ExportAPI {
  private gameLogicManager: GameInterFaceManager = new GameInterFaceManager()

  init() {
    // 注册所有客户端事件监听
    sealBridge.on(sealCallEvents.joinGame, this.joinGame)
    sealBridge.on(sealCallEvents.getSeatRect, this.getSeatRect)
    sealBridge.on(sealCallEvents.setMicStatus, this.setMicStatus)
    // ... 更多事件注册
  }
}
```

**主要功能**:
- **接口导出**: 将游戏功能导出给客户端调用
- **事件处理**: 处理客户端发送的各种事件
- **生命周期管理**: 管理事件监听器的注册和注销
- **数据转换**: 处理客户端和游戏之间的数据格式转换

**核心接口**:
- `joinGame`: 处理加入游戏请求
- `getSeatRect`: 获取玩家座位位置信息
- `setMicStatus`: 设置麦克风状态
- `setSeatGiftIcon`: 设置礼物图标
- `closeWebsocket`: 关闭 WebSocket 连接
- `setGameRoomInfo`: 设置游戏房间信息

**3. Bridge - 桥接工具函数**

**系统信息获取**:
```typescript
// 获取客户端系统信息
export function getAppInfo(): Promise<IGetSystemInfoRes>

// 获取用户信息
export function getUserInfo(): Promise<IGetUserInfoRes>

// 获取房间信息
export function getRoomInfo(): Promise<IGetRoomInfoRes>
```

**游戏状态同步**:
```typescript
// 同步游戏状态给客户端
export function NotifyGameStatus(status: GameStatusType, data: object = {})

// 同步玩家状态给客户端
export function NotifyPlayerStatus(status: PlayerType, userId: string, data: object = {})

// 通知游戏预加载完成
export function notifyGamePreloaded(status: number = 1, msg: string = '')
```

**用户交互处理**:
```typescript
// 处理用户点击事件
export const sealClick = (params: { notifyName: string, data?: Record<any, any> })

// 再来一局
export function replayClick()

// 充值点击
export function rechargeClick()
```

**数据分析和追踪**:
```typescript
// 事件追踪上报
export function traceReport(eventName: string, data: Record<any, any>)

// RDS 平台数据上报
export function rdsTraceReport(eventName: string, data: Record<any, any>)
```

##### 桥接层通信时序图

```mermaid
sequenceDiagram
    participant Client as Seal客户端APP
    participant Bridge as native.bridge
    participant SealBridge as SealBridge
    participant ExportAPI as ExportAPI
    participant Game as 游戏逻辑

    Note over Client,Game: 1. 游戏初始化阶段
    Client->>Bridge: 启动游戏WebView
    Bridge->>SealBridge: 初始化桥接
    SealBridge->>SealBridge: 注册事件处理器
    ExportAPI->>SealBridge: 注册游戏接口
    SealBridge-->>Client: 桥接初始化完成

    Note over Client,Game: 2. 游戏主动调用客户端
    Game->>SealBridge: call('getUserInfo', {}, callback)
    SealBridge->>SealBridge: 生成callbackID
    SealBridge->>Bridge: sendToNative('resposneOcMethod', message)
    Bridge->>Client: 转发getUserInfo请求
    Client->>Client: 获取用户信息
    Client->>Bridge: 返回用户数据
    Bridge->>SealBridge: onNative('sealJSBridgeCallback', data)
    SealBridge->>SealBridge: 查找并执行callback
    SealBridge->>Game: 返回用户信息

    Note over Client,Game: 3. 客户端主动调用游戏
    Client->>Bridge: 发送joinGame事件
    Bridge->>SealBridge: onNative('sealJSBridgeListener', event)
    SealBridge->>SealBridge: 解析事件参数
    SealBridge->>ExportAPI: 触发joinGame监听器
    ExportAPI->>Game: 执行加入游戏逻辑
    Game->>Game: 发送WebSocket请求
    ExportAPI->>SealBridge: h5Callback(callbackId, result)
    SealBridge->>Bridge: sendToNative('h5callback', response)
    Bridge->>Client: 返回执行结果

    Note over Client,Game: 4. 游戏状态同步
    Game->>SealBridge: NotifyGameStatus('PLAYING', data)
    SealBridge->>Bridge: sendToNative('notifyGameStatus', params)
    Bridge->>Client: 同步游戏状态
    Client->>Client: 更新UI状态
```

##### 通信协议和数据格式

**1. 通信机制**
```typescript
// 游戏 -> 客户端 (主动调用)
sealBridge.call('methodName', params, callback)

// 客户端 -> 游戏 (事件监听)
sealBridge.on('eventName', handler)

// 游戏 -> 客户端 (回调响应)
sealBridge.h5Callback(callbackId, responseData)
```

**2. 消息格式**
```typescript
// 发送给客户端的消息格式
{
  method: string,           // 方法名
  params: object,          // 参数
  __callback_id: string    // 回调ID
}

// 客户端发送的事件格式
{
  name: string,            // 事件名
  __params: object,        // 参数
  __callback_id?: string   // 可选的回调ID
}
```

**3. 支持的原生方法 (TNativeMethod)**
```typescript
type TNativeMethod =
  | 'getGameConfig'        // 获取游戏配置
  | 'login'               // 登录获取token
  | 'getUserInfo'         // 获取用户信息
  | 'getSystemInfo'       // 获取系统信息
  | 'getGameRoomInfo'     // 获取房间信息
  | 'notifyGameStatus'    // 通知游戏状态
  | 'notifyPlayerStatus'  // 通知玩家状态
  | 'notifyClickEvent'    // 通知点击事件
  | 'setSpeakerMode'      // 设置扬声器模式
  | 'setMicMode'          // 设置麦克风模式
  | 'traceReport'         // 事件追踪
  | 'rdsTraceReport'      // RDS数据上报
  // ... 更多方法
```

**4. 支持的客户端事件 (sealCallEvents)**
```typescript
enum sealCallEvents {
  joinGame = 'joinGame',                    // 加入游戏
  cancelJoinGame = 'cancelJoinGame',        // 取消加入游戏
  getSeatRect = 'getSeatRect',              // 获取座位矩形
  setMicStatus = 'setMicStatus',            // 设置麦克风状态
  setSeatGiftIcon = 'setSeatGiftIcon',      // 设置座位礼物图标
  closeWebsocket = 'closeWebsocket',        // 关闭WebSocket
  setGameRoomInfo = 'setGameRoomInfo',      // 设置游戏房间信息
  gameHall_changeGame = 'gameHall_changeGame', // 大厅切换游戏
  // ... 更多事件
}
```

##### 环境适配和调试支持

**1. 环境检测**
```typescript
// 检测是否在Seal客户端环境
const ua = decodeURIComponent(window.navigator.userAgent.toLocaleLowerCase())
const isSeal = !!(/sealgame/gi.test(ua) || NATIVE)

// 调试模式检测
const debug = Boolean(getUrlParam1('debug'))
```

**2. 降级处理**
```typescript
// 非Seal环境下的降级处理
if (sealBridge.isSeal) {
  // 调用真实的客户端接口
  sealBridge.call('getUserInfo', {}, callback)
} else {
  // 使用模拟数据
  callback(MOCK_USER_INFO)
}
```

**3. 调试功能**
- **日志输出**: 详细的调用日志和参数记录
- **模拟数据**: 非客户端环境下的模拟数据支持
- **URL参数**: 通过URL参数控制调试行为
- **错误处理**: 完善的异常捕获和处理机制

##### 桥接层的核心价值

**1. 深度集成能力**
桥接层使得游戏能够深度集成到 Seal 客户端生态中，实现：
- **原生功能调用**: 直接调用客户端的麦克风、扬声器、存储等原生功能
- **用户体验一致性**: 与客户端 UI 风格和交互模式保持一致
- **数据共享**: 与客户端共享用户信息、设置和状态
- **生命周期同步**: 游戏生命周期与客户端应用生命周期同步

**2. 业务功能扩展**
通过桥接层，游戏获得了丰富的业务功能：
- **社交功能**: 好友系统、聊天、语音通话
- **支付功能**: 内购、充值、虚拟货币管理
- **分析功能**: 用户行为分析、性能监控、错误追踪
- **运营功能**: A/B测试、配置下发、热更新

**3. 技术架构优势**
- **解耦设计**: 游戏逻辑与客户端功能解耦，便于独立开发和测试
- **异步通信**: 基于回调的异步通信机制，不阻塞游戏主线程
- **类型安全**: 完整的 TypeScript 类型定义，减少运行时错误
- **环境适配**: 自动检测运行环境，支持开发、测试和生产环境

##### 实际应用场景

**1. 游戏房间管理**
```typescript
// 获取房间信息并初始化游戏
const roomInfo = await getRoomInfo()
globalData.roomId = roomInfo.roomId
globalData.ownerId = roomInfo.ownerId

// 通知客户端游戏状态变化
NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_PLAYING, {
  playerCount: 4,
  gameMode: 'classic'
})
```

**2. 用户交互处理**
```typescript
// 获取玩家座位位置信息用于显示麦克风状态
const seatRects = gameLogicManager.getSeatRect(['user1', 'user2'])
// 返回格式：[{ userId: 'user1', rect: { x, y, width, height }}]

// 处理麦克风状态变化
sealBridge.on(sealCallEvents.setMicStatus, (data) => {
  const { userId, status } = data
  ViewManager.updateMicStatus(userId, status)
})
```

**3. 数据分析和追踪**
```typescript
// 游戏事件追踪
traceReport('game_start', {
  gameMode: globalData.gameInfo.gameMode,
  playerCount: globalData.gameInfo.playerInfo.length,
  timestamp: Date.now()
})

// RDS 平台数据上报
rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
  link: 'Cocos_LoadLudoGame',
  roomId: globalData.roomId,
  costTime: loadTime,
  result: 'Success'
})
```

**4. 游戏生命周期管理**
```typescript
// 游戏预加载完成通知
export function notifyGamePreloaded(status: number = 1, msg: string = '') {
  sealBridge.call('notifyGamePreloaded', { status, msg }, (ret) => {
    console.info(`notifyGamePreloaded ret:${ret}`)
  })
}

// 游戏结束处理
export function notifyBustPlayerResult(coinInfo?: any) {
  sealBridge.call('notifyBustStatus', {
    resultUrl: "",
    userId: globalData.myUserId,
    gameRoundId: globalData.gameRoundId,
    rankInfo: JSON.stringify(coinInfo ?? [])
  })
}
```

##### 桥接层设计模式

**1. 单例模式 (Singleton)**
```typescript
class SealBridge {
  private static instance: SealBridge

  public static getInstance() {
    if (!SealBridge.instance) {
      SealBridge.instance = new SealBridge()
    }
    return SealBridge.instance
  }
}
```

**2. 观察者模式 (Observer)**
```typescript
// 事件注册和监听
private registerHandlers: { [key: string]: TCallback[] } = {}

public on(name: sealCallEvents, callback: TCallback) {
  if (!this.registerHandlers[name]) this.registerHandlers[name] = []
  this.registerHandlers[name].push(callback)
}
```

**3. 策略模式 (Strategy)**
```typescript
// 环境适配策略
if (sealBridge.isSeal) {
  // 真实客户端环境策略
  sealBridge.call('getUserInfo', {}, callback)
} else {
  // 开发环境模拟策略
  callback(MOCK_USER_INFO)
}
```

**4. 代理模式 (Proxy)**
```typescript
// 桥接层作为游戏和客户端之间的代理
export const sealClick = (params, callback) => {
  sealBridge.call("notifyClickEvent", params, callback)
}
```

##### 错误处理和容错机制

**1. 异常捕获**
```typescript
private _callNative(method, params, __callback_id) {
  try {
    native.bridge.sendToNative('resposneOcMethod', JSON.stringify({
      method, params, __callback_id
    }))
  } catch (err) {
    console.error('Bridge call failed:', err)
  }
}
```

**2. 超时处理**
```typescript
// 在 wsUtil 中实现的超时机制
this.timerMap[key] = setTimeout(() => {
  if (this.requestMap[key]) {
    console.warn('接口超时: ', params.type, params)
    sealBridge.h5Callback(h5callId, { "status": "fail" })
  }
}, this.TIMEOUT_DURATION)
```

**3. 降级处理**
```typescript
// 获取音效状态的降级处理
export function getEffectSoundState(userid) {
  return new Promise((resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call('getEffectSoundState', { userId: userid }, (ret) => {
        if (ret.status === 'success') {
          resolve(ret)
        } else {
          resolve({ music_switch_on: true, sound_effect_switch_on: true })
        }
      })
    } else {
      // 非客户端环境的默认值
      resolve({ music_switch_on: true, sound_effect_switch_on: true })
    }
  })
}
```

##### 性能优化策略

**1. 回调管理优化**
- 使用递增的 callbackID 避免冲突
- 及时清理已执行的回调函数
- 避免内存泄漏

**2. 消息序列化优化**
- 使用 JSON.stringify 进行消息序列化
- 避免循环引用
- 控制消息大小

**3. 事件监听优化**
- 支持事件的注册和注销
- 避免重复注册相同事件
- 及时清理不需要的监听器

桥接层是 Seal Ludo 项目的核心创新点，它不仅实现了游戏与客户端的深度集成，还为游戏提供了丰富的原生功能和业务能力。通过精心设计的架构和完善的错误处理机制，桥接层确保了游戏在各种环境下的稳定运行和良好的用户体验。

## 棋盘格子坐标系统

### 核心算法概述

Seal Ludo 项目采用了一套精密的坐标系统来管理棋盘上的格子位置和棋子移动。该系统基于 **15x15 网格布局**，通过多层坐标转换实现了从逻辑格子索引到屏幕像素坐标的精确映射。

### 核心类和组件

#### 1. **ChessManager** - 棋子管理核心类
**位置**: `assets/script/manager/chess.ts`
**职责**: 棋子位置计算、移动控制、坐标转换的核心实现

#### 2. **gameConfig.ts** - 坐标配置中心
**位置**: `assets/script/gameConfig.ts`
**职责**: 定义所有格子的坐标映射、起始位置、终点位置等配置数据

#### 3. **ViewManager** - 视图管理器
**位置**: `assets/script/manager/view.ts`
**职责**: 棋盘视图初始化、玩家位置排序、坐标系统管理

#### 4. **Ludo** - 主场景类
**位置**: `assets/script/scene/ludo.ts`
**职责**: 棋盘容器节点管理、网格容器初始化

### 坐标系统架构

#### 1. **棋盘坐标系统全景图**

```mermaid
graph TB
    subgraph "15x15 棋盘网格布局"
        subgraph "玩家1区域 (右上)"
            P1_START[起始区域<br/>164,268 | 164,164<br/>268,268 | 268,164]
            P1_END[终点区域 105<br/>0,30 | -28,54<br/>0,54 | 28,54]
        end

        subgraph "玩家2区域 (右下)"
            P2_START[起始区域<br/>164,-266 | 164,-162<br/>268,-266 | 268,-162]
            P2_END[终点区域 115<br/>24,0 | 54,-28<br/>54,0 | 54,28]
        end

        subgraph "玩家3区域 (左下)"
            P3_START[起始区域<br/>-164,-266 | -164,-162<br/>-268,-266 | -268,-162]
            P3_END[终点区域 125<br/>0,-26 | -28,-54<br/>0,-54 | 28,-54]
        end

        subgraph "玩家4区域 (左上)"
            P4_START[起始区域<br/>-164,268 | -164,164<br/>-268,268 | -268,164]
            P4_END[终点区域 135<br/>-24,0 | -54,28<br/>-54,0 | -54,-28]
        end

        subgraph "主要移动路径"
            PATH1[格子1-13<br/>右侧路径<br/>48,288 → 336,-48]
            PATH2[格子14-26<br/>下方路径<br/>288,-48 → -48,-336]
            PATH3[格子27-39<br/>左侧路径<br/>-48,-288 → -336,48]
            PATH4[格子40-52<br/>上方路径<br/>-288,48 → 48,336]
        end

        subgraph "特殊格子"
            CORNERS[转角格子<br/>6: 48,48<br/>19: 48,-48<br/>32: -48,-48<br/>45: -48,48]
            OBSTACLES[障碍物格子<br/>100,110,120,130]
            SAFE_ZONES[安全区域<br/>101-104: 玩家1<br/>111-114: 玩家2<br/>121-124: 玩家3<br/>131-134: 玩家4]
        end
    end

    subgraph "坐标转换流程"
        LOGIC_INDEX[逻辑格子索引<br/>boxIndex: 0-135]
        GRID_COORD[网格相对坐标<br/>gridCoordinateMap]
        WORLD_COORD[世界坐标<br/>convertToWorldSpaceAR]
        NODE_COORD[节点本地坐标<br/>convertToNodeSpaceAR]
        SCREEN_COORD[屏幕坐标<br/>最终渲染位置]

        LOGIC_INDEX --> GRID_COORD
        GRID_COORD --> WORLD_COORD
        WORLD_COORD --> NODE_COORD
        NODE_COORD --> SCREEN_COORD
    end

    subgraph "节点层次结构"
        CANVAS[Canvas 画布]
        GAME[Game 游戏容器]
        CHESSBOARD[ChessBoard 棋盘节点]
        GRID_CONTAINER[GridContainer 网格容器]
        CHESS_GROUP[ChessGroup 棋子组]
        CHESS_NODE[Chess 棋子节点]

        CANVAS --> GAME
        GAME --> CHESSBOARD
        CHESSBOARD --> GRID_CONTAINER
        GAME --> CHESS_GROUP
        CHESS_GROUP --> CHESS_NODE
    end
```

#### 2. **15x15 网格布局系统**

```typescript
// 网格基础配置
export const gridData = { rows: 15, cols: 15 }

// 网格坐标映射 - 核心数据结构
export const gridCoordinateMap = new Map([
  [1, v3(48, 288, 0)],    // 格子1的相对坐标
  [2, v3(48, 240, 0)],    // 格子2的相对坐标
  [3, v3(48, 192, 0)],    // 格子3的相对坐标
  // ... 共52个主要格子
  [52, v3(48, 336, 0)],   // 格子52的相对坐标

  // 特殊区域坐标
  [100, v3(0, 288, 0)],   // 玩家1终点区域
  [110, v3(288, 0, 0)],   // 玩家2终点区域
  [120, v3(0, -288, 0)],  // 玩家3终点区域
  [130, v3(-288, 0, 0)]   // 玩家4终点区域
])
```

### 核心算法实现

#### 1. **格子坐标获取算法**

```typescript
/**
 * 根据棋盘格子序号获取该格子的position位置
 * @param boxIndex 格子序号 (0-135)
 * @param node 棋子节点
 * @returns Vec3 世界坐标位置
 */
public static getPosLocation(boxIndex: number, node: Node): Vec3 {
  if (boxIndex === 0) {
    // 起始位置 - 从预定义的起始位置配置获取
    return ChessManager.getOriginOrEndLocation(node)
  } else if (finalChessIndexs.includes(boxIndex)) {
    // 终点位置 - 从预定义的终点位置配置获取
    let pos = ChessManager.getOriginOrEndLocation(node, true)
    return v3(pos.x, pos.y, 0)
  } else {
    // 普通格子 - 从网格坐标映射获取
    const chssCoor = gridCoordinateMap.get(boxIndex)
    return this.getIndexPosition(chssCoor)
  }
}
```

#### 2. **坐标转换核心算法**

```typescript
/**
 * 将网格相对坐标转换为棋子组节点的本地坐标
 * @param chssCoor 网格相对坐标
 * @returns Vec3 棋子组本地坐标
 */
public static getIndexPosition(chssCoor: Vec3 | Vec2): Vec3 {
  // 第一步：将网格容器的相对坐标转换为世界坐标
  const gPos = commonUtil.convertToWorldSpaceAR(
    ViewManager.gameInstance.gridContainer,
    v3(chssCoor.x, chssCoor.y)
  )

  // 第二步：将世界坐标转换为棋子组节点的本地坐标
  return commonUtil.convertToNodeSpaceAR(
    ViewManager.gameInstance.chessGroupNode,
    gPos
  )
}
```

#### 3. **起始和终点位置算法**

```typescript
/**
 * 获取棋子的起始位置或终点位置
 * @param node 棋子节点 (名称格式: "1-1", "2-3" 等)
 * @param isFinal 是否为终点位置
 * @returns Vec3 位置坐标
 */
public static getOriginOrEndLocation(node: Node, isFinal?: boolean) {
  let chessType: any = node?.name?.split('-')
  const chessIndex = Number(chessType?.[1] || 0)  // 棋子序号 (1-4)
  chessType = Number(chessType?.[0] || 3)         // 玩家类型 (1-4)

  // 根据当前玩家视角获取位置索引
  const sortIndex = ViewManager.getCurrentPlayerPosIndex(chessType)

  // 从配置中获取对应位置
  return (isFinal ? chessEndPositions : chessOriginalPositions)
    [convertPlayerPos(sortIndex)][chessIndex]
}
```

### 坐标配置数据

#### 1. **棋子起始位置配置**

```typescript
export const chessOriginalPositions = {
  RightTop: [
    undefined,
    v3(164, 268, 0),   // 玩家1棋子1起始位置
    v3(164, 164, 0),   // 玩家1棋子2起始位置
    v3(268, 268, 0),   // 玩家1棋子3起始位置
    v3(268, 164, 0)    // 玩家1棋子4起始位置
  ],
  RightBottom: [/* 玩家2起始位置 */],
  LeftBottom: [/* 玩家3起始位置 */],
  LeftTop: [/* 玩家4起始位置 */]
}
```

#### 2. **棋子终点位置配置**

```typescript
export const chessEndPositions = {
  RightTop: [
    undefined,
    v3(0, 30, 0),      // 玩家1棋子1终点位置
    v3(-28, 54, 0),    // 玩家1棋子2终点位置
    v3(0, 54, 0),      // 玩家1棋子3终点位置
    v3(28, 54, 0)      // 玩家1棋子4终点位置
  ],
  // ... 其他玩家终点位置
}
```

#### 3. **特殊格子索引定义**

```typescript
// 终点格子索引
export const finalChessIndexs = [105, 115, 125, 135]

// 障碍物格子索引
export const gameObstacleIndexs = [100, 110, 120, 130]

// 转角坐标 - 用于棋子移动时的平滑转弯
export const cornerCoordinateMap = new Map([
  [6, v3(48, 48, 0)],    // 右上转角
  [19, v3(48, -48, 0)],  // 右下转角
  [32, v3(-48, -48, 0)], // 左下转角
  [45, v3(-48, 48, 0)]   // 左上转角
])
```

### 棋盘旋转和视角适配

#### 1. **多玩家视角旋转算法**

```typescript
/**
 * 根据当前玩家初始化棋盘视角
 * @param userIndex 当前玩家序号 (1-4)
 */
public static async initUiInfo(userIndex: ChessType, ...) {
  let boardAngle = 0, boxAngle = 0

  switch (Number(userIndex)) {
    case 1: {
      boardAngle = -180
      this.playerSortPosIndexs = [3, 4, 1, 2]  // 重新排序玩家位置
      break
    }
    case 2: {
      boardAngle = 90
      this.playerSortPosIndexs = [4, 1, 2, 3]
      break
    }
    case 4: {
      boardAngle = -90
      this.playerSortPosIndexs = [2, 3, 4, 1]
      break
    }
    default: {
      this.playerSortPosIndexs = [1, 2, 3, 4]  // 默认排序
      break
    }
  }

  // 应用棋盘旋转
  this.gameInstance.chessBoardNode.angle = -boardAngle
}
```

#### 2. **玩家位置索引转换**

```typescript
/**
 * 获取当前玩家在视角中的位置索引
 * @param userIndex 玩家序号
 * @returns 位置索引 (1-4)
 */
public static getCurrentPlayerPosIndex(userIndex) {
  return this.playerSortPosIndexs.findIndex(i => i === userIndex) + 1
}
```

### 棋子位置管理

#### 1. **棋子位置矫正算法**

```typescript
/**
 * 棋子位置矫正 - 根据格子索引设置棋子的正确位置和缩放
 * @param chessInfo 棋子信息 {chessId, chessIndex}
 */
public static fixChessPosition(chessInfo: ChessInfo) {
  const { chessId, chessIndex } = chessInfo
  const chessNode = this.chessMap.get(chessId)

  // 记录棋子当前位置索引
  this.chessPosIndexMap.set(chessId, chessIndex)

  if (!chessNode?.isValid) return

  // 根据位置类型设置缩放比例
  chessNode.setScale(
    commonUtil.getEqualVec3(
      finalChessIndexs.includes(chessIndex)
        ? smallScaleRate      // 终点区域：0.5倍缩放
        : chessIndex === 0
          ? largeScaleRate    // 起始位置：1.32倍缩放
          : 1                 // 普通格子：正常缩放
    )
  )

  // 设置棋子位置
  const targetPos = this.getPosLocation(chessIndex, chessNode)
  if (targetPos) {
    chessNode.setPosition(targetPos)
  }
}
```

#### 2. **多棋子重叠处理算法**

```typescript
/**
 * 处理同一格子上多个棋子的排列
 * @param chessInfos 棋子信息数组
 */
public static scaleMulChess(chessInfos: Array<ChessInfo>) {
  const chessPosInfo = {}

  // 按格子索引分组棋子
  chessInfos.forEach(({ chessId, chessIndex }) => {
    if (!chessPosInfo[chessIndex]) chessPosInfo[chessIndex] = []
    chessPosInfo[chessIndex].push(chessId)
  })

  // 处理每个格子上的棋子排列
  Object.entries(chessPosInfo).forEach((infos: any, colIndex: number, self) => {
    const boxSize = 48              // 格子大小
    const rowLen = infos[1].length  // 该格子上的棋子数量
    const colLen = self.length      // 总格子数量

    // 计算缩放比例和间距
    const scaleRate = (colLen > 1 || rowLen > 1) ? 2 / 3 : 1
    const xDistance = boxSize * scaleRate / (rowLen > 2 ? rowLen + 1 : rowLen)
    const yDistance = boxSize * scaleRate / (colLen > 2 ? colLen + 1 : colLen)

    // 排列每个棋子
    infos[1].forEach((chessId: string, index: number) => {
      const chessNode = ChessManager.chessMap.get(chessId)
      if (!chessNode?.isValid) return

      const { x, y } = ChessManager.getPosLocation(+chessIndex, chessNode)
      chessNode.setScale(commonUtil.getEqualVec3(scaleRate))
      chessNode.setPosition(v3(
        x + xDistance * (infos[1].length - index - 1) - (rowLen > 1 ? 8 : 0),
        y + yDistance * (colIndex - (colLen > 1 ? colLen / 2 : 0)),
        0
      ))
    })
  })
}
```

### 棋子移动路径算法

#### 1. **移动路径计算**

```typescript
/**
 * 棋子移动动画
 * @param node 棋子节点
 * @param movePath 移动路径数组 [起始格子, 中间格子..., 目标格子]
 * @param isEatChess 是否吃子
 */
public static async chessPosMove(
  node: Node,
  movePath: number[],
  isEatChess: boolean,
  ...
) {
  return new Promise(async (resolve) => {
    node.setScale(Vec3.ONE)
    commonUtil.setIndex(node, ELayerIndex.Moving)

    // 逐步移动到每个格子
    for (let i = 0; i < movePath.length; i++) {
      let targetIndex = movePath[i]
      let targetPos: Vec3 = ChessManager.getPosLocation(targetIndex, node)
      let isLastMove: boolean = i === (movePath.length - 1)

      // 处理转角位置的平滑移动
      const targetCornerPos = cornerCoordinateMap.get(targetIndex)
      if (targetCornerPos) {
        await ActionManager.chessMove(
          node,
          this.getIndexPosition(targetCornerPos),
          false,
          true
        )
      }

      // 移动到目标位置
      if (isEatChess && isLastMove) {
        await ActionManager.eatChessMove(node, targetPos)
      } else {
        await ActionManager.chessMove(node, targetPos, isLastMove, false)
      }

      // 处理到达终点的特殊效果
      if (finalChessIndexs.indexOf(targetIndex) >= 0) {
        node.setScale(commonUtil.getEqualVec3(smallScaleRate))
        AudioMgr.instance.play('audio/sound_chessEnter')
        ViewManager.toggleVisiableResultParticle(true, targetPos)
      }
    }

    resolve(null)
  })
}
```

### 坐标系统的技术特点

#### 1. **分层设计**
- **逻辑层**: 使用简单的数字索引 (0-135) 表示格子
- **配置层**: 通过 Map 数据结构映射索引到相对坐标
- **转换层**: 多级坐标空间转换确保精确定位
- **渲染层**: 最终转换为屏幕像素坐标

#### 2. **灵活适配**
- **多玩家视角**: 支持4个玩家不同的视角旋转
- **动态缩放**: 根据棋子状态自动调整缩放比例
- **重叠处理**: 智能处理同一格子多个棋子的排列

#### 3. **性能优化**
- **预计算坐标**: 所有格子坐标预先计算并缓存
- **增量更新**: 只更新发生变化的棋子位置
- **对象池**: 复用棋子节点减少内存分配

#### 4. **扩展性**
- **配置驱动**: 通过修改配置文件即可调整棋盘布局
- **模块化**: 坐标计算逻辑独立，便于维护和扩展
- **类型安全**: 完整的 TypeScript 类型定义

这套坐标系统是 Seal Ludo 项目的技术核心之一，它不仅保证了棋子位置的精确计算，还为游戏的视觉效果和用户体验提供了坚实的技术基础。通过多层抽象和精心设计的算法，系统能够处理复杂的多玩家视角、棋子重叠、移动动画等各种场景，展现了优秀的工程设计水平。

#### 2. 场景层 (Scene Layer)
**位置**: `assets/script/scene/`
**职责**: 场景管理和生命周期控制

- **gameHall.ts**: 游戏大厅场景，处理房间管理和场景切换
- **ludo.ts**: 主游戏场景，管理游戏界面和组件初始化

#### 3. 管理器层 (Manager Layer)
**位置**: `assets/script/manager/`
**职责**: 游戏逻辑管理和业务处理

- **gameMgr**: 游戏生命周期和状态管理
- **view**: 统一视图管理和UI协调
- **chess**: 棋子逻辑和移动管理
- **audioMgr**: 音频播放和管理
- **wsMgr**: WebSocket 连接管理
- **syncCommand**: 服务器指令同步处理

#### 4. 框架层 (Framework Layer)
**位置**: `assets/script/framework/`
**职责**: 基础设施和通用功能

- **uiMgr**: UI 界面管理和生命周期
- **loadMgr**: 资源加载和缓存管理
- **eventMgr**: 事件系统和消息分发
- **poolMgr**: 对象池管理和内存优化
- **i18n**: 国际化和多语言支持

#### 5. 网络层 (Network Layer)
**位置**: `assets/script/network/`
**职责**: 网络通信和数据传输

- **wsClient**: WebSocket 客户端实现
- **wsUtil**: 网络工具和消息处理
- **constants**: 网络常量和枚举定义

## 核心模块分析

### 1. 游戏管理器 (GameMgr)

```typescript
export default class GameMgr {
  private _commandMap: Map<ACTIONS, SyncCommand>
  
  public startGame() {
    this._commandMap = getSysncCommandMap()
    director.on('initWebSocket', this.handleInitEvents, this);
    director.on('handleGameStage', this.handleGameStage, this);
  }
  
  public stopGame() {
    // 清理事件监听和资源
  }
}
```

**核心功能**:
- 游戏生命周期管理
- 指令映射和分发
- 游戏状态转换处理
- WebSocket 事件监听

### 2. 棋子管理器 (ChessManager)

```typescript
export default class ChessManager extends Component {
  public static chessMap: Map<string, Node> = new Map()
  
  public onClickChessEvent(event) {
    if (!globalData.isMyRound || !globalData.canMoveChessMap) return
    // 处理棋子点击逻辑
    globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand })
  }
  
  public static async chessPosMove(node: Node, movePath: number[]) {
    // 棋子移动动画实现
  }
}
```

**核心功能**:
- 棋子创建和初始化
- 棋子移动逻辑和动画
- 点击事件处理
- 多种游戏模式支持

### 3. 网络通信模块 (wsClient)

```typescript
export default class wsClient {
  private _socket: WebSocket | null = null
  private _connected: boolean = false
  
  private async init() {
    this._socket = new WebSocket(
      `${gameConfig.WEBSOCKET[gameConfig.ENV]}/ws/game`, 
      [globalData.token]
    )
    this.setupEventHandlers()
  }
  
  public postMessage(message: any) {
    if (!this._connected) return false
    this._socket?.send(JSON.stringify(message))
    return true
  }
}
```

**核心功能**:
- WebSocket 连接管理
- 自动重连机制
- 消息发送和接收
- 连接状态监控

### 4. 同步指令系统 (SyncCommand)

```typescript
export default class SyncCommand {
  public async execute(message: any) {
    const { action, gameData } = message
    switch (action) {
      case ACTIONS.NOTIFY_DICE_RESULT:
        await this._handleDiceResultCommand(gameData)
        break
      case ACTIONS.NOTIFY_CHESS_MOVE:
        await this._handleChessMoveCommand(gameData, lastMessage?.gameData, message)
        break
      case ACTIONS.NOTIFY_PLAYERSTATUS_UPDATE:
        await this._handlePlayerViewCommand(gameData)
        break
    }
  }
}
```

**核心功能**:
- 服务器指令解析和执行
- 游戏状态同步
- 动画和特效协调
- 多玩家操作序列化

### 5. UI管理器 (UIMgr)

```typescript
export class UIMgr {
  private _dictSharedPanel: any = {}
  private _dictLoading: any = {}

  public async showDialog(panelPath: string, args?: any, parent?: Node) {
    const panel = this._dictSharedPanel[panelPath]
    if (isValid(panel)) {
      panel.active = true
      const script = panel.getComponent(scriptName)
      if (script && script.init) script.init(args)
    } else {
      await this.createUi(panelPath, scriptName, parent, zIndex, args)
    }
  }
}
```

**核心功能**:
- UI 界面生命周期管理
- 单例界面缓存机制
- 动态加载和创建
- 层级管理和显示控制

## 数据流和业务流程

### 游戏启动流程

```mermaid
sequenceDiagram
    participant App as 客户端APP
    participant Bridge as Seal桥接层
    participant Hall as 游戏大厅
    participant Game as 游戏场景
    participant Server as 游戏服务器

    App->>Bridge: 启动游戏
    Bridge->>Hall: 初始化大厅场景
    Hall->>Server: 建立WebSocket连接
    Server-->>Hall: 连接成功
    App->>Bridge: changeGame指令
    Bridge->>Hall: 切换到游戏场景
    Hall->>Game: 加载ludo场景
    Game->>Server: 绑定房间
    Server-->>Game: 房间信息
    Game->>Game: 初始化游戏组件
    Game-->>App: 游戏准备完成
```

### 游戏核心循环

```mermaid
sequenceDiagram
    participant Player as 玩家
    participant UI as 游戏界面
    participant Chess as 棋子管理
    participant Server as 服务器
    participant Other as 其他玩家

    Player->>UI: 点击骰子
    UI->>Server: 发送摇骰子请求
    Server-->>UI: 返回骰子结果
    UI->>UI: 播放骰子动画
    UI->>Chess: 显示可移动棋子
    Player->>Chess: 选择棋子
    Chess->>Server: 发送移动请求
    Server-->>Chess: 确认移动
    Chess->>Chess: 执行移动动画
    Chess->>UI: 更新游戏状态
    Server-->>Other: 同步游戏状态
```

### 消息处理流程

```mermaid
graph LR
    A[WebSocket消息] --> B[wsUtil解析]
    B --> C[消息类型判断]
    C --> D[SyncCommand处理]
    D --> E[更新游戏状态]
    E --> F[更新UI显示]
    F --> G[发送确认消息]

    C --> H[错误处理]
    H --> I[重试机制]
    I --> J[用户提示]
```

## 关键代码实现

### 1. 全局数据管理

```typescript
class GlobalData {
  public WS: wsClient | null = null
  public gameRoundId: string = ''
  public myUserId: string = ''
  public currentOpUserId: string = ''
  public roomId: string = ''
  public gameInfo: any = {
    playerInfo: [],
    chessInfo: [],
    gameStatus: GAME_STAGE.INIT,
    gameMode: GAME_MODE.ONE_VS_ONE,
    endMode: END_MODE.CLASSIC
  }

  public socketSend(type: SOCKET_TYPE, message: object = {}, h5Callback?: string) {
    if (!this.WS?.connected) {
      this.unsentMessage.push({ type, message })
      return
    }
    WsUtil.send(this.WS, { type, message }, h5Callback)
  }
}
```

### 2. 事件系统实现

```typescript
export class EventMgr {
  private static _handlers: { [key: string]: any[] } = {}

  public static on(eventName: string, target: any, handler: Function) {
    let handlerList: Array<any> = EventMgr._handlers[eventName]
    if (!handlerList) {
      handlerList = []
      EventMgr._handlers[eventName] = handlerList
    }
    handlerList.push({ handler, target })
  }

  public static dispatchEvent(eventName: string, ...args: any) {
    let handlerList = this._handlers[eventName]
    if (!handlerList) return

    for (let i = 0; i < handlerList.length; i++) {
      let curHandler = handlerList[i]
      if (curHandler) {
        curHandler.handler.apply(curHandler.target, args)
      }
    }
  }
}
```

### 3. 资源加载管理

```typescript
export class LoadMgr {
  public async loadRes(url: string, type: any) {
    return new Promise((resolve, reject) => {
      resources.load(url, type, (err: any, res: any) => {
        if (err) {
          reject([err, res])
          return
        }
        resolve([null, res])
      })
    })
  }

  public async createPrefab(path: string) {
    let cacheNode = CachePrefabs.instance.prefabsCacheMap.get(path)
    if (cacheNode) {
      return instantiate(cacheNode)
    }

    const info = await this.loadRes(path, Prefab)
    const prefab = info?.[1]
    CachePrefabs.instance.prefabsCacheMap.set(path, prefab)
    return instantiate(prefab)
  }
}
```

### 4. 桥接层实现

```typescript
class SealBridge {
  private callbacks: { [key: string]: TCallback } = {}
  private registerHandlers: { [key: string]: TCallback[] } = {}

  public call(name: TNativeMethod, params: unknown, callback?: TCallback) {
    const id = (this.callbackID++).toString()
    this.callbacks[id] = callback
    this._callNative(name, params, id)
  }

  public on(name: sealCallEvents, callback: TCallback) {
    if (!this.registerHandlers[name]) this.registerHandlers[name] = []
    this.registerHandlers[name].push(callback)
  }

  public h5Callback(__callback_id, params) {
    if (!__callback_id) return
    const newParams = JSON.stringify(params)
    this._callNative('h5callback', newParams, __callback_id)
  }
}
```

## 配置和构建

### 项目配置文件

#### 1. package.json
```json
{
  "name": "seal-ludo",
  "version": "3.6.1",
  "dependencies": {
    "axios": "^0.27.2"
  },
  "repository": {
    "type": "git",
    "url": "https://gitlab.lizhi.fm/lz-game/seal-ludo.git"
  }
}
```

#### 2. tsconfig.json
```json
{
  "extends": "./temp/tsconfig.cocos.json",
  "compilerOptions": {
    "strict": false,
    "module": "commonjs",
    "lib": ["es2015", "es2017", "dom"],
    "target": "es5"
  }
}
```

#### 3. 游戏配置 (gameConfig.ts)
```typescript
export default class GameConfig {
  public static version: string = 'v2.4.0(2023030101)'
  public static ENV: string = Env.dev

  public static SERVER: object = {
    prod: 'https://ludo.pongpongchat.com',
    pre: 'https://ludopre.pongpongchat.com',
    dev: 'http://ludo.yfxn.lizhi.fm'
  }

  public static WEBSOCKET: object = {
    prod: 'wss://ludo.pongpongchat.com',
    pre: 'wss://ludopre.pongpongchat.com',
    dev: 'ws://ludo.yfxn.lizhi.fm'
  }
}
```

### 构建配置

#### Android 构建配置
```json
{
  "platform": "android",
  "packageName": "game.ludo.test",
  "orientation": { "portrait": true },
  "apiLevel": 30,
  "appABIs": ["arm64-v8a", "armeabi-v7a"],
  "renderBackEnd": { "gles3": true }
}
```

#### iOS 构建配置
```json
{
  "platform": "ios",
  "packageName": "ios.game.test",
  "orientation": { "portrait": true },
  "targetVersion": "12.0",
  "renderBackEnd": { "metal": true }
}
```

### 目录结构

```
seal-ludo/
├── assets/                    # 游戏资源
│   ├── scene/                # 场景文件
│   │   ├── gameHall.scene    # 游戏大厅场景
│   │   └── ludo.scene        # 主游戏场景
│   ├── script/               # TypeScript 代码
│   │   ├── framework/        # 框架层
│   │   ├── manager/          # 管理器层
│   │   ├── network/          # 网络层
│   │   ├── seal/             # 桥接层
│   │   ├── scene/            # 场景脚本
│   │   ├── prefabs/          # 预制体脚本
│   │   ├── components/       # UI组件
│   │   ├── utils/            # 工具类
│   │   └── type/             # 类型定义
│   ├── texture/              # 贴图资源
│   │   ├── ui/               # UI贴图
│   │   └── fnt/              # 字体文件
│   ├── materials/            # 材质资源
│   ├── effects/              # 特效资源
│   └── resources/            # 动态加载资源
├── buildConfig/              # 构建配置
│   ├── buildConfig_android.json
│   └── buildConfig_ios.json
├── native/                   # 原生代码
│   └── engine/               # 引擎相关
├── settings/                 # 项目设置
├── profiles/                 # 配置文件
├── package.json              # 项目依赖
├── tsconfig.json             # TypeScript配置
└── project.json              # 项目配置
```

## 项目特点和优势

### 1. 架构优势
- **清晰的分层架构**: 职责分离明确，便于维护和扩展
- **高度模块化**: 每个模块功能单一，耦合度低
- **完善的错误处理**: 统一的错误处理和日志系统
- **灵活的配置管理**: 支持多环境配置和动态切换

### 2. 技术亮点
- **实时多人同步**: 基于WebSocket的高效实时通信
- **流畅的动画系统**: 优化的动画和特效处理
- **完善的音频管理**: 支持音效和背景音乐管理
- **跨平台支持**: 统一代码库支持iOS和Android

### 3. 用户体验
- **多语言支持**: 完整的国际化解决方案
- **丰富的游戏模式**: 支持1v1、4人对战、2v2等多种模式
- **流畅的操作体验**: 优化的触控响应和视觉反馈
- **完善的游戏指引**: 新手引导和帮助系统

### 4. 性能优化
- **对象池管理**: 减少内存分配和垃圾回收
- **资源预加载**: 提升游戏启动和切换速度
- **动画优化**: 高效的动画播放和管理
- **网络优化**: 智能重连和消息队列管理

## 项目运行指南

### 环境要求

#### 必需工具
- **Cocos Creator 3.6.1** - 主要开发工具和运行环境
- **Node.js** (推荐 v16+) - JavaScript 运行时环境
- **现代浏览器** - Chrome、Firefox、Safari 等支持 WebGL 的浏览器

#### 可选工具
- **VSCode** - 推荐的代码编辑器
- **Android Studio** - Android 平台开发和调试
- **Xcode** - iOS 平台开发和调试（仅 macOS）

### 运行方式

#### 1. Cocos Creator 编辑器运行（推荐开发调试）

**步骤说明**：
```bash
# 1. 安装项目依赖
npm install

# 2. 启动 Cocos Creator 3.6.1
# 3. 打开项目：选择 seal-ludo 项目文件夹
# 4. 等待项目加载和 TypeScript 编译完成
# 5. 点击编辑器顶部的"预览"按钮
# 6. 游戏将在默认浏览器中启动
```

**本地访问地址**：
```
http://localhost:7456/?debug=true
```

**调试参数说明**：
- `debug=true` - 开启调试模式，显示详细日志
- `userId=123` - 指定用户ID，用于多人游戏测试
- `roomId=456` - 指定房间ID，相同房间的玩家可以一起游戏

#### 2. 在线测试环境（快速体验）

**测试地址**：
```bash
# 玩家1测试地址
http://tiya.yfxn.lizhi.fm/static/seal_ludo/index.html?roomId=0090&userId=3465912345&debug=true

# 玩家2测试地址
http://tiya.yfxn.lizhi.fm/static/seal_ludo/index.html?roomId=0090&userId=3465912341115&debug=true
```

**多人游戏测试**：
- 同时打开两个不同的浏览器窗口或标签页
- 使用不同的 `userId` 参数模拟不同玩家
- 使用相同的 `roomId` 参数让玩家进入同一房间

#### 3. 原生应用构建

**Android 构建流程**：
```bash
# 1. 在 Cocos Creator 中选择"项目" -> "构建发布"
# 2. 选择目标平台：Android
# 3. 配置构建参数：
{
  "packageName": "game.ludo.test",
  "orientation": "portrait",
  "apiLevel": 30,
  "appABIs": ["arm64-v8a", "armeabi-v7a"],
  "renderBackEnd": "gles3"
}

# 4. 点击"构建"按钮，等待构建完成
# 5. 使用 Android Studio 打开生成的项目
# 6. 连接 Android 设备或启动模拟器
# 7. 运行应用进行测试
```

**iOS 构建流程**：
```bash
# 1. 在 Cocos Creator 中选择"项目" -> "构建发布"
# 2. 选择目标平台：iOS
# 3. 配置构建参数：
{
  "packageName": "ios.game.test",
  "targetVersion": "12.0",
  "renderBackEnd": "metal",
  "orientation": "portrait"
}

# 4. 点击"构建"按钮，等待构建完成
# 5. 使用 Xcode 打开生成的 .xcodeproj 文件
# 6. 配置开发者证书和设备
# 7. 连接 iOS 设备或启动模拟器
# 8. 运行应用进行测试
```

### 游戏启动流程

```mermaid
graph LR
    A[启动游戏] --> B[加载 Cocos Creator 引擎]
    B --> C[初始化桥接层 SealBridge]
    C --> D[建立 WebSocket 连接]
    D --> E[加载游戏场景和资源]
    E --> F[显示游戏界面]
    F --> G[等待玩家操作]

    style A fill:#e3f2fd
    style F fill:#e8f5e8
    style G fill:#fff3e0
```

### 网络配置

#### 服务器环境配置
```typescript
// gameConfig.ts 中的服务器配置
export default class GameConfig {
  public static SERVER = {
    prod: 'https://ludo.pongpongchat.com',      // 生产环境
    pre: 'https://ludopre.pongpongchat.com',    // 预发环境
    dev: 'http://ludo.yfxn.lizhi.fm'            // 开发环境
  }

  public static WEBSOCKET = {
    prod: 'wss://ludo.pongpongchat.com',        // 生产环境 WebSocket
    pre: 'wss://ludopre.pongpongchat.com',      // 预发环境 WebSocket
    dev: 'ws://ludo.yfxn.lizhi.fm'              // 开发环境 WebSocket
  }
}
```

#### 环境切换
```typescript
// 通过修改 ENV 变量切换环境
public static ENV: string = Env.dev  // dev | pre | prod
```

### 调试和开发

#### 1. 调试模式功能
- **详细日志输出**：显示游戏状态、网络通信、用户操作等信息
- **桥接层调试**：显示与客户端的通信详情
- **网络监控**：WebSocket 连接状态和消息内容
- **性能监控**：帧率、内存使用等性能指标

#### 2. 开发者工具使用
```bash
# 在浏览器中按 F12 打开开发者工具

# Console 标签页：查看游戏日志和错误信息
# Network 标签页：监控 WebSocket 连接和 HTTP 请求
# Performance 标签页：分析游戏性能和帧率
# Application 标签页：查看本地存储和缓存
```

#### 3. 常用调试技巧
```typescript
// 在代码中添加调试日志
console.log('游戏状态:', globalData.gameInfo)
console.log('玩家信息:', globalData.playerInfo)
console.log('WebSocket状态:', globalData.ws?.readyState)

// 使用 cs.log 进行条件日志输出
cs.log('棋子移动:', chessId, movePath)
```

### 常见问题解决

#### 1. 项目无法打开
**问题现象**：Cocos Creator 无法正常加载项目

**解决方案**：
```bash
# 检查 Cocos Creator 版本是否为 3.6.1
# 确保项目路径不包含中文字符或特殊符号

# 清理项目缓存
rm -rf temp/
rm -rf library/

# 重新打开项目让编辑器重新编译
```

#### 2. TypeScript 编译错误
**问题现象**：代码编译失败，出现类型错误

**解决方案**：
```bash
# 检查 tsconfig.json 配置
# 确保 TypeScript 版本兼容

# 清理编译缓存
rm -rf temp/declarations/
rm -rf temp/programming/

# 重新编译项目
```

#### 3. 网络连接失败
**问题现象**：无法连接到游戏服务器

**解决方案**：
```typescript
// 检查网络连接状态
// 确认服务器地址配置正确
// 查看浏览器控制台的网络错误信息

// 在调试模式下检查 WebSocket 连接
console.log('WebSocket URL:', wsUrl)
console.log('连接状态:', ws.readyState)
```

#### 4. 依赖安装失败
**问题现象**：npm install 执行失败

**解决方案**：
```bash
# 清理依赖和缓存
rm -rf node_modules/
rm package-lock.json
rm yarn.lock

# 清理 npm 缓存
npm cache clean --force

# 重新安装依赖
npm install

# 或使用 yarn
yarn install
```

#### 5. 移动端构建失败
**问题现象**：Android 或 iOS 构建过程中出错

**解决方案**：
```bash
# Android 构建问题
# 1. 检查 Android SDK 和 NDK 路径配置
# 2. 确保 Java 版本为 JDK 8 或 11
# 3. 检查 Gradle 版本兼容性

# iOS 构建问题
# 1. 检查 Xcode 版本是否支持目标 iOS 版本
# 2. 确保开发者证书配置正确
# 3. 检查设备或模拟器兼容性
```

### 性能优化建议

#### 开发阶段
- 使用 `debug=true` 参数获取详细的性能数据
- 在 Chrome DevTools 中监控内存使用情况
- 关注 WebSocket 消息的频率和大小
- 定期检查资源加载时间和缓存效果

#### 生产环境
- 关闭调试模式以提升性能
- 启用资源压缩和代码混淆
- 使用 CDN 加速静态资源加载
- 优化图片和音频资源的大小和格式

### 快速启动指南

#### 最简单的体验方式
```bash
# 直接访问在线测试版本（无需安装任何工具）
http://tiya.yfxn.lizhi.fm/static/seal_ludo/index.html?debug=true
```

#### 本地开发启动
```bash
# 1. 下载并安装 Cocos Creator 3.6.1
# 2. 克隆或下载项目代码
# 3. 使用 Cocos Creator 打开项目
# 4. 点击预览按钮
# 5. 在浏览器中访问 http://localhost:7456/?debug=true
```

#### 多人游戏测试
```bash
# 1. 打开两个浏览器窗口
# 2. 分别访问不同 userId 的测试地址
# 3. 确保使用相同的 roomId 参数
# 4. 开始多人游戏测试
```

这个运行指南涵盖了从环境搭建到问题解决的完整流程，无论是开发者进行代码调试，还是测试人员进行功能验证，都能找到合适的运行方式。项目的多种运行模式设计得很灵活，既支持快速在线体验，也支持完整的本地开发环境。

## 潜在改进点

### 1. 代码质量
- **类型安全**: 部分代码可以增加更严格的类型定义
- **单元测试**: 缺少完整的测试覆盖，建议增加单元测试
- **代码规范**: 可以统一代码风格和命名规范
- **文档完善**: 增加更详细的API文档和注释

### 2. 性能优化
- **状态管理**: 可以考虑使用更高效的状态管理方案
- **网络优化**: 请求缓存和重试机制可以进一步优化
- **资源管理**: 资源加载的优先级和策略可以改进
- **内存管理**: 增加更精细的内存监控和优化

### 3. 可维护性
- **模块解耦**: 某些模块的耦合度可以进一步降低
- **配置管理**: 配置系统可以更加灵活和可扩展
- **错误处理**: 统一错误处理机制和用户提示
- **日志系统**: 增加更详细的日志分级和过滤

### 4. 功能扩展
- **离线模式**: 支持单机游戏模式
- **回放系统**: 游戏录像和回放功能
- **社交功能**: 好友系统和聊天功能
- **数据分析**: 游戏数据统计和分析

## 总结

Seal Ludo 是一个架构设计良好的多人在线游戏项目，采用了清晰的分层架构和模块化设计。项目在技术实现、用户体验和性能优化方面都有不错的表现，特别是在实时多人同步、跨平台支持和客户端集成方面具有明显优势。

项目的核心优势在于：
1. **完善的架构设计**：分层清晰，职责明确
2. **高效的实时通信**：基于WebSocket的稳定连接
3. **优秀的用户体验**：流畅的动画和丰富的交互
4. **强大的扩展性**：支持多种游戏模式和配置

通过持续的代码优化、性能提升和功能扩展，这个项目可以为用户提供更好的游戏体验，并为后续的开发和维护奠定坚实的基础。
