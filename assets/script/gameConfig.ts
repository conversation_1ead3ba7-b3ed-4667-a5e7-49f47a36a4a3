import { Env } from './type/index'
import { _decorator, v3 } from 'cc';

export default class GameConfig {
  public static version: string = 'v2.4.0(2023030101)'; // 游戏版本号
  public static ENV: string = Env.dev;// dev pre prod

  // ========== 本地游戏模式配置 ==========
  public static FORCE_LOCAL_MODE: boolean = true; // 强制本地模式
  public static DISABLE_NETWORK: boolean = true;  // 禁用网络连接

  //游戏后端http域名（本地模式下不使用）
  public static SERVER: object = {
    prod: 'https://ludo.pongpongchat.com',
    pre: 'https://ludopre.pongpongchat.com',
    dev: 'http://ludo.yfxn.lizhi.fm'
  }
  //游戏后端websocket域名（本地模式下不使用）
  public static WEBSOCKET: object = {
    prod: `wss://ludo.pongpongchat.com`,
    pre: `wss://ludopre.pongpongchat.com`,
    dev: `ws://ludo.yfxn.lizhi.fm`, // tiya-ludo灯塔环境地址：ws://liveact.yfxn.lizhi.fm
  }

  // ========== 固定本地游戏配置 ==========
  public static LOCAL_GAME_CONFIG = {
    gameMode: 7,        // GAME_MODE.FOUR_BATTLE (4人对战)
    endMode: 1,         // END_MODE.CLASSIC (经典模式)
    playerCount: 4,     // 玩家数量
    enableAI: true,     // 启用AI玩家
    autoStart: true,    // 自动开始游戏
    skipNetworkCheck: true, // 跳过网络检查
  }
}

export const gameMaxFps = 60

export const gameMinFps = 60

// 棋子最大缩放比例
export const largeScaleRate = 1.32

export const smallScaleRate = 0.5

// 所有棋子id
export let allChesses = ['1-1', '1-2', '1-3', '1-4', '2-1', '2-2', '2-3', '2-4', '3-1', '3-2', '3-3', '3-4', '4-1', '4-2', '4-3', '4-4']

// 棋子终点坐标
export const finalChessIndexs = [105, 115, 125, 135]

// 网格数据
export const gridData = { rows: 15, cols: 15 }

// 游戏障碍物位置
export const gameObstacleIndexs = [100, 110, 120, 130]

// 网格战旅点位Map
export const chessPosMap = new Map(
  [
    ['9-2', '1'],
    ['9-3', '2'],
    ['9-4', '3'],
    ['9-5', '4'],
    ['9-6', '5'],
    ['10-7', '6'],
    ['11-7', '7'],
    ['12-7', '8'],
    ['13-7', '9'],
    ['14-7', '10'],
    ['15-7', '11'],
    ['15-8', '12'],
    ['15-9', '13'],
    ['14-9', '14'],
    ['13-9', '15'],
    ['12-9', '16'],
    ['11-9', '17'],
    ['10-9', '18'],
    ['9-10', '19'],
    ['9-11', '20'],
    ['9-12', '21'],
    ['9-13', '22'],
    ['9-14', '23'],
    ['9-15', '24'],
    ['8-15', '25'],
    ['7-15', '26'],
    ['7-14', '27'],
    ['7-13', '28'],
    ['7-12', '29'],
    ['7-11', '30'],
    ['7-10', '31'],
    ['6-9', '32'],
    ['5-9', '33'],
    ['4-9', '34'],
    ['3-9', '35'],
    ['2-9', '36'],
    ['1-9', '37'],
    ['1-8', '38'],
    ['1-7', '39'],
    ['2-7', '40'],
    ['3-7', '41'],
    ['4-7', '42'],
    ['5-7', '43'],
    ['6-7', '44'],
    ['7-6', '45'],
    ['7-5', '46'],
    ['7-4', '47'],
    ['7-3', '48'],
    ['7-2', '49'],
    ['7-1', '50'],
    ['8-1', '51'],
    ['9-1', '52'],
    ['8-2', '100'],
    ['8-3', '101'],
    ['8-4', '102'],
    ['8-5', '103'],
    ['8-6', '104'],
    ['14-8', '110'],
    ['13-8', '111'],
    ['12-8', '112'],
    ['11-8', '113'],
    ['10-8', '114'],
    ['8-14', '120'],
    ['8-13', '121'],
    ['8-12', '122'],
    ['8-11', '123'],
    ['8-10', '124'],
    ['2-8', '130'],
    ['3-8', '131'],
    ['4-8', '132'],
    ['5-8', '133'],
    ['6-8', '134'],
    // ['8-9', '125'],
    // ['8-7', '105'],
    // ['7-8', '135'],
    // ['9-8', '115'],
  ]
);

// 转角坐标
export const cornerCoordinateMap = new Map([
  [6, v3(48, 48, 0)],
  [19, v3(48, -48, 0)],
  [32, v3(-48, -48, 0)],
  [45, v3(-48, 48, 0)],
])

// 网格坐标
export const gridCoordinateMap = new Map(
  [
    [1, v3(48, 288, 0)],
    [2, v3(48, 240, 0)],
    [3, v3(48, 192, 0)],
    [4, v3(48, 144, 0)],
    [5, v3(48, 96, 0)],
    [6, v3(96, 48, 0)],
    [7, v3(144, 48, 0)],
    [8, v3(192, 48, 0)],
    [9, v3(240, 48, 0)],
    [10, v3(288, 48, 0)],
    [11, v3(336, 48, 0)],
    [12, v3(336, 0, 0)],
    [13, v3(336, -48, 0)],
    [14, v3(288, -48, 0)],
    [15, v3(240, -48, 0)],
    [16, v3(192, -48, 0)],
    [17, v3(144, -48, 0)],
    [18, v3(96, -48, 0)],
    [19, v3(48, -96, 0)],
    [20, v3(48, -144, 0)],
    [21, v3(48, -192, 0)],
    [22, v3(48, -240, 0)],
    [23, v3(48, -288, 0)],
    [24, v3(48, -336, 0)],
    [25, v3(0, -336, 0)],
    [26, v3(-48, -336, 0)],
    [27, v3(-48, -288, 0)],
    [28, v3(-48, -240, 0)],
    [29, v3(-48, -192, 0)],
    [30, v3(-48, -144, 0)],
    [31, v3(-48, -96, 0)],
    [32, v3(-96, -48, 0)],
    [33, v3(-144, -48, 0)],
    [34, v3(-192, -48, 0)],
    [35, v3(-240, -48, 0)],
    [36, v3(-288, -48, 0)],
    [37, v3(-336, -48, 0)],
    [38, v3(-336, 0, 0)],
    [39, v3(-336, 48, 0)],
    [40, v3(-288, 48, 0)],
    [41, v3(-240, 48, 0)],
    [42, v3(-192, 48, 0)],
    [43, v3(-144, 48, 0)],
    [44, v3(-96, 48, 0)],
    [45, v3(-48, 96, 0)],
    [46, v3(-48, 144, 0)],
    [47, v3(-48, 192, 0)],
    [48, v3(-48, 240, 0)],
    [49, v3(-48, 288, 0)],
    [50, v3(-48, 336, 0)],
    [51, v3(0, 336, 0)],
    [52, v3(48, 336, 0)],
    [100, v3(0, 288, 0)],
    [101, v3(0, 240, 0)],
    [102, v3(0, 192, 0)],
    [103, v3(0, 144, 0)],
    [104, v3(0, 96, 0)],
    [110, v3(288, 0, 0)],
    [111, v3(240, 0, 0)],
    [112, v3(192, 0, 0)],
    [113, v3(144, 0, 0)],
    [114, v3(96, 0, 0)],
    [120, v3(0, -288, 0)],
    [121, v3(0, -240, 0)],
    [122, v3(0, -192, 0)],
    [123, v3(0, -144, 0)],
    [124, v3(0, -96, 0)],
    [130, v3(-288, 0, 0)],
    [131, v3(-240, 0, 0)],
    [132, v3(-192, 0, 0)],
    [133, v3(-144, 0, 0)],
    [134, v3(-96, 0, 0)]
  ]
)

// 障碍物移动终点map
export const obtacleFinalIndexMap = new Map([
  [100, 103],
  [110, 113],
  [120, 123],
  [130, 133]
])


/**
 * 棋子初始位置
 */
export const chessOriginalPositions = {
  RightTop: [undefined, v3(164, 268, 0), v3(164, 164, 0), v3(268, 268, 0), v3(268, 164, 0)],
  RightBottom: [undefined, v3(164, -266, 0), v3(164, -162, 0), v3(268, -266, 0), v3(268, -162, 0)],
  LeftBottom: [undefined, v3(-164, -266, 0), v3(-164, -162), v3(-268, -266), v3(-268, -162, 0)],
  LeftTop: [undefined, v3(-164, 268, 0), v3(-164, 164, 0), v3(-268, 268, 0), v3(-268, 164, 0)]
}

/**
 * 棋子终点位置
 */
export const chessEndPositions = {
  RightTop: [undefined, v3(0, 30, 0), v3(-28, 54, 0), v3(0, 54, 0), v3(28, 54, 0)], // 105
  RightBottom: [undefined, v3(24, 0, 0), v3(54, -28, 0), v3(54, 0, 0), v3(54, 28, 0)], // 115
  LeftBottom: [undefined, v3(0, -26, 0), v3(-28, -54, 0), v3(0, -54, 0), v3(28, -54, 0)], // 125
  LeftTop: [undefined, v3(-24, 0, 0), v3(-54, 28, 0), v3(-54, 0, 0), v3(-54, -28, 0)], // 135
}
