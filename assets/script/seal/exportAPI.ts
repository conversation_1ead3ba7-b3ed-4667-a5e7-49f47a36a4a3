import GiftManager from "../manager/gift"
import ViewManager from "../manager/view"
import globalData from './../globalData'
import { END_MODE, GAME_MODE, SOCKET_TYPE } from '../network/constants'
import { convertWorldCoordinate } from "../utils/utils"
import { sealBridge } from './SealBridge'
import { getEffectSoundState, getGameConfigId, getGameSubType, getToken, NotifyPlayerGiftStatus, NotifyUpdateGameSkin } from "./bridge";
import { EventType } from "../type/events";
import { Utils } from "../framework/frameworkUtils"
import { _decorator, director, find } from "cc";
import { commonUtil } from "../utils/commonUtil"
import { sealCallEvents } from "./sealType"
import { PlayerStatus } from "../type"
import { rdsTrack } from "../track/rdsTrack"
import { cs } from "../framework/log"

/**
 * @name 游戏对外接口管理器
 * @description game interface for supporting app
 */
class GameInterFaceManager {

  /**
   * @param userId 
   * @returns 
   * @description get player's management 
   */
  private getPlayeManager(userId) {
    return ViewManager.getPlayerMgr(globalData.getUserInfoByUserId(userId)?.userIndex)
  }

  /**
   * @param userIds userId collections
   * @returns format: [{ userId: '', rect: { x, y, width, height }}]
   * @description get locational information about the mic
   */
  public getSeatRect(userIds: (string | number)[]) {
    const result = []
    let rootNode = commonUtil.getUITransform(find('Canvas'));
    for (let i = 0; i < userIds.length; i++) {
      const playerManager = this.getPlayeManager(userIds[i])
      if (!playerManager) continue
      const { width, height } = commonUtil.getUITransform(playerManager._avatorNode)
      const avatorPosition = convertWorldCoordinate(playerManager._avatorNode)
      const giftNode = commonUtil.getUITransform(playerManager._giftNode.getComponent(GiftManager).giftNode)
      if (!avatorPosition || !giftNode) return
      const giftPosition = convertWorldCoordinate(playerManager._giftNode.getComponent(GiftManager).giftNode)
      let obj = {
        userId: userIds[i],
        rect: { width: rootNode.width / 2, height: rootNode.height / 2 }, // 画布宽高
        userRect: {
          x: avatorPosition.x / 2,
          y: avatorPosition.y / 2,
          width: width * (globalData.gameScaleRate || 1) / 2,
          height: height * (globalData.gameScaleRate || 1) / 2
        },
        giftRect: {
          x: giftPosition.x / 2,
          y: giftPosition.y / 2,
          width: giftNode.width * (globalData.gameScaleRate || 1) / 2,
          height: giftNode.height * (globalData.gameScaleRate || 1) / 2
        }
      };

      cs.log("getSealRect:" + globalData?.gameInfo?.gameMode);
      if (globalData?.gameInfo?.gameMode == GAME_MODE.TWO_VS_TWO) {
        const teamNode = playerManager?._avatorMgr?.teamNode;
        if (!teamNode) {
          cs.log("teamNode is null");

          return;
        }
        const uitransform = commonUtil.getUITransform(teamNode);
        const teamPosition = convertWorldCoordinate(teamNode);
        const teamName = playerManager?._avatorMgr?._teamName;
        obj["teamRect"] = {
          teamName: teamName,
          x: teamPosition.x / 2,
          y: teamPosition.y / 2,
          width: uitransform.width * (globalData.gameScaleRate || 1) / 2,
          height: uitransform.height * (globalData.gameScaleRate || 1) / 2
        }
        cs.log("teamRect" + JSON.stringify(obj["teamRect"]));
      }

      result.push(obj);
    }
    return result
  }

  /**
   * @name 设置礼物图片 
   * @description set gift icon view
   */
  public setSeatGiftIcon(userId: string | number, url: string) {
    try {
      // if (!userId || !url) return
      // this.getPlayeManager(userId)
      //   ?.giftNode
      //   ?.getComponent(GiftManager)
      //   ?.setSeatGiftIcon(url)
    } catch (e) { }
  }

  /**
   *  @name 设置我的麦克风
   *  @description set my mic view 
   */
  public setMicStatus(userId: string, status: boolean) {
    this.getPlayeManager(userId)?.setMicViewStatus(status)
  }

  /**
   * @name 设置玩家声音波纹状态
   */
  private isPlayingVoice = false
  public setSpeakerStatus(userId: string | number) {
    try {
      if (this.isPlayingVoice) return
      this.isPlayingVoice = true
      this.getPlayeManager(userId)?.playMicWave()
    } catch (e) { } finally {
      setTimeout(() => {
        this.isPlayingVoice = false
      }, 3000)
    }
  }
}


const { ccclass } = _decorator;
@ccclass
export default class ExportAPI {
  private gameLogicManager: GameInterFaceManager = new GameInterFaceManager()
  init() {
    sealBridge.on(sealCallEvents.joinGame, this.joinGame)
    sealBridge.on(sealCallEvents.cancelJoinGame, this.exit)
    sealBridge.on(sealCallEvents.getSeatRect, this.getSeatRect)
    sealBridge.on(sealCallEvents.setSeatGiftIcon, this.setSeatGiftIcon)
    sealBridge.on(sealCallEvents.setMicStatus, this.setMicStatus)
    sealBridge.on(sealCallEvents.setSpeak, this.setSpeakerStatus)
    sealBridge.on(sealCallEvents.closeWebsocket, this.closeWebsocket)
    sealBridge.on(sealCallEvents.setGameRoomInfo, this.setGameRoomInfo)
    sealBridge.on(sealCallEvents.toggleAvatorBorderVisiable, this.toggleAvatorBorderVisiable)
    sealBridge.on(sealCallEvents.getUserCoinInfo, this.getUserCoinInfo)
    sealBridge.on(sealCallEvents.setGameMode, this.setGameMode)
    director.on(sealCallEvents.notifyGitfStatus, this.notifyGitfStatus, this)
  }

  initGameHall() {
    sealBridge.on(sealCallEvents.preload, this.preload)
  }
  setGameRoomInfo = (ret) => {
    console.error("setGameModePageInfo====", ret)
    globalData.roomInfo = ret
    director.emit(EventType.CHANGE_VIEW)
  }
  notifyGitfStatus = () => {
    const userIds = globalData.gameInfo.playerInfo.map(i => i.userId)
    const result = this.gameLogicManager.getSeatRect(userIds)
    NotifyPlayerGiftStatus(JSON.stringify(result))
    NotifyUpdateGameSkin()
    getEffectSoundState(globalData.myUserId)
  }
  setSeatGiftIcon = (ret, __h5callback_id) => {
    let { userId, url } = ret;
    this.gameLogicManager.setSeatGiftIcon(userId, url)
  }
  getSeatRect = (ret, __h5callback_id) => {
    const { userIds } = ret
    const result = this.gameLogicManager.getSeatRect(userIds)
    __h5callback_id && sealBridge.h5Callback(__h5callback_id, result)
  }
  getUserCoinInfo = (ret, __h5callback_id) => {
    const { userId } = ret
    let coinInfo
    let maxEatChess = 0
    const userCoinInfo = globalData.gameInfo?.userCoinInfo?.filter((infos) => {
      if (infos.userId === userId) {
        coinInfo = infos
      }
      return [PlayerStatus.Gaming, PlayerStatus.Hosting].includes(globalData.getUserInfoByUserId(infos?.userId)?.status)
    })
    userCoinInfo?.forEach((infos) => {
      if (+infos.eatChessNum > maxEatChess) {
        maxEatChess = (+infos.eatChessNum)
      }
    })
    console.log('getUserCoinInfo', JSON.stringify(userCoinInfo))
    console.log('getUserCoinInfo', JSON.stringify(globalData.gameInfo?.userCoinInfo))
    console.log('getUserCoinInfo', coinInfo?.antes, maxEatChess, coinInfo?.coin)
    sealBridge.h5Callback(__h5callback_id, {
      userId,
      coinNum: coinInfo?.coin || 0,
      antes: coinInfo?.antes ?? 0,
      maxEatChess
    })
  }
  setMicStatus = (ret, __h5callback_id) => {
    const { userId, status } = ret
    this.gameLogicManager.setMicStatus(userId, status)
  }
  setSpeakerStatus = (ret, __h5callback_id) => {
    const { userId } = ret
    this.gameLogicManager?.setSpeakerStatus(userId)
  }
  joinGame = () => {
    globalData.socketSend(SOCKET_TYPE.JOIN_GAME, {})
  }
  exit = () => {
    globalData.socketSend(SOCKET_TYPE.LEAVE, {})
  }
  setGameMode = () => {
    console.log("setGameMode success")
    globalData.socketSend(SOCKET_TYPE.OPERATION, {
      subCommand: {
        commandKey: 'setGameMode',
        gameMode: GAME_MODE.ONE_VS_ONE,
        endMode: END_MODE.CLASSIC
      }
    })
  }
  preload = async (data, __h5callback_id) => {
    console.error('sealSdk 调用游戏initWebsocket，params:', data, '__h5callback_id:', __h5callback_id)
    // ViewManager.resetView()
    globalData.dateTime["preload"] = new Date().getTime();
    const { userId } = data
    director.emit('INIT-PRELOAD', userId, __h5callback_id)
  }
  closeWebsocket = (ret, __h5callback_id) => {
    console.error(`sealSdk 调用游戏closeWebsocket，params:${ret},__h5callback_id:${__h5callback_id} `)
    ViewManager.resetView()
    globalData.resetGlobalData()
    commonUtil.setActive(ViewManager.gameInstance?.loadingLayer, true)
    director.emit('CLOSE-WEBSOCKET', __h5callback_id)
  }
  toggleAvatorBorderVisiable(ret) {
    const { userId, isvisiable } = ret
    ViewManager.toggleAvatorBorderVisiable(userId, isvisiable)
  }
  onDestroy() {
    sealBridge.off(sealCallEvents.joinGame, this.joinGame)
    sealBridge.off(sealCallEvents.cancelJoinGame, this.exit)
    sealBridge.off(sealCallEvents.getSeatRect, this.getSeatRect)
    sealBridge.off(sealCallEvents.setSeatGiftIcon, this.setSeatGiftIcon)
    sealBridge.off(sealCallEvents.setMicStatus, this.setMicStatus)
    sealBridge.off(sealCallEvents.setSpeak, this.setSpeakerStatus)
    sealBridge.off(sealCallEvents.closeWebsocket, this.closeWebsocket)
    sealBridge.off(sealCallEvents.setGameRoomInfo, this.setGameRoomInfo)
    sealBridge.off(sealCallEvents.toggleAvatorBorderVisiable, this.toggleAvatorBorderVisiable)
    director.off(sealCallEvents.notifyGitfStatus, this.notifyGitfStatus, this)
  }

  onGameHallDestroy() {
    sealBridge.off(sealCallEvents.preload, this.preload)
  }
}
