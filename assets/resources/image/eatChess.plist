<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{0,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{2250,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{2500,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{2750,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{3000,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>14.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{3250,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{250,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{500,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{750,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{1000,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{1250,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{1500,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{1750,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{250,250}</string>
                <key>spriteSourceSize</key>
                <string>{250,250}</string>
                <key>textureRect</key>
                <string>{{2000,0},{250,250}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>eatChess.png</string>
            <key>size</key>
            <string>{3500,250}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:27800d78847d2ca53bd52d9e7fdbcc68:b76f10e99ca426217d8d28d087f8f67e:01a534d95c720509323fbdc6a9bb5dc3$</string>
            <key>textureFileName</key>
            <string>eatChess.png</string>
        </dict>
    </dict>
</plist>
