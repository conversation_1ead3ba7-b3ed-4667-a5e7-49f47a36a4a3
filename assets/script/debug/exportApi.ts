import globalData from "../globalData"
import ExportAPI from "../seal/exportAPI"

/**
 * @zh 测试获取用户信息
 */
export const testGetUserCoinInfo = () => {
  globalData.gameInfo.userCoinInfo = [
    { antes: 500, multiple: 1, userIndex: 1, userId: '111', eatChessNum: 5, coin: 2222, balance: 500 },
    { antes: 500, multiple: 1, userIndex: 2, userId: '222', eatChessNum: 10, coin: 2222, balance: 0 },    
    { antes: 500, multiple: 1, userIndex: 3, userId: '333', eatChessNum: 2, coin: 2222, balance: -500 },
    { antes: 500, multiple: 1, userIndex: 4, userId: '444', eatChessNum: 2, coin: 2222, balance: 0 },
  ]
  
  globalData.gameInfo.playerInfo = [
    { userId: "111", name: "ال مقدار اكل الفائز", portrait: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRDlQ3rdTWjj1WXTmnqn_pphh8AbuJDLsyMQQ&usqp=CAU', userIndex: 1, isAuto: false, portraitMask: "", status: 1 },
    { userId: "222", name: 'رشاد ابو فارس', portrait: " https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQQtVs1FqmZhWnLMNrdyKTTS2KB2UYIDygwEA&usqp=CAU", userIndex: 2, isAuto: false, portraitMask: "", status: 1 },
    { userId: "333", name: "ال مقدار اكل الفائز", portrait: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSOdn9kgri-0dtvdcyU1F0vnSj1sUhCMtg75A&usqp=CAU", userIndex: 3, isAuto: false, portraitMask: "", status: 1 },
    { userId: "444", name: "ال مقدار اكل الفائز", portrait: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSsAaUQqpRQ1hYDzZiceDJt3QiLHFucgzYwCQ&usqp=CAU", userIndex: 4, isAuto: false, portraitMask: "", status: 1 },
  ]
  const exportAPI = new ExportAPI()
  setTimeout(() => {
    console.log("执行")
    console.log(exportAPI.getUserCoinInfo({userId: '333'}, ''))

    globalData.gameInfo.userCoinInfo = [
      { antes: 500, multiple: 1, userIndex: 1, userId: '111', eatChessNum: 5, coin: 2222, balance: 500 },
      { antes: 500, multiple: 1, userIndex: 2, userId: '222', eatChessNum: 20, coin: 2222, balance: 0 },    
      { antes: 500, multiple: 1, userIndex: 3, userId: '333', eatChessNum: 2, coin: 2222, balance: -500 },
      { antes: 500, multiple: 1, userIndex: 4, userId: '444', eatChessNum: 2, coin: 2222, balance: 0 },
    ]
    setTimeout(() => {
      console.log(exportAPI.getUserCoinInfo({userId: '333'}, ''))
    }, 1000)
  }, 4000)
}

/**
 * @zh
 */