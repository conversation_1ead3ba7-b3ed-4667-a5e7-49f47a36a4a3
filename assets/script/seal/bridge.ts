import { I18n } from '../framework/i18n'
import { cs } from '../framework/log'
import globalData from '../globalData'
import { sealBridge, sealClick } from './SealBridge'
import { _decorator, find } from "cc";

import {
  IGetSystemInfoRes,
  IGetRoomInfoRes,
  IGetUserInfoRes,
  IGetRoomPageInfoRes,
  ILoginRes,
  GameStatusType,
  PlayerType,
  IGameEffectStatus
} from './types'
import { commonUtil } from '../utils/commonUtil'
import { getUrlParam1 } from '../utils/utils'
import { ENativeLayer } from '../type/enum';

let userId = getUrlParam1('userId')
let roomId = getUrlParam1('roomId')

console.log('isSeal', sealBridge.isSeal)

// 需要第一时间调用，用最快的方法
export function getAppInfo(): Promise<IGetSystemInfoRes> {
  return new Promise(async (resolve) => {
    let env = 0;
    if (window.location.href.indexOf('yfxn') >= 0 || window.location.port) {
      env = 2;
    }
    if (window.location.href.indexOf('pre') >= 0) {
      env = 1;
    }
    const APP_INFO: IGetSystemInfoRes = {
      appId: 'xxx',
      deviceId: '64C66B4C-5C0C-4972-B82D-D9D8C8BF471E',
      model: 'iPhone11,2',
      platform: 'iOS',
      system: '12.3.1',
      version: '1.0.1',
      SealsdkVersion: '1.0.0',
      language: 'ar',
      countryCode: 'xx',
      env, //0:线上环境， 1:预发环境，  2:灯塔环境
      isPreload: false
    }
    console.log('getSystemInfo call', sealBridge.isSeal)
    if (sealBridge.isSeal) {
      sealBridge.call('getSystemInfo', {}, (data: IGetSystemInfoRes) => {
        globalData.isIos = data.platform ?? "";// /ios/i.test(data.platform);
        let lang = data.language || 'en'
        if (lang.indexOf('en') >= 0) {
          lang = 'en'
          I18n.languageState = 1;
        } else if (lang.indexOf('ar') >= 0) {
          lang = 'ar'
          I18n.languageState = 2;
        } else if (lang.indexOf('id') >= 0) {
          lang = 'id'
        } else if (lang.indexOf('ja') >= 0) {
          lang = 'ja'
        } else if (lang.indexOf('es') >= 0) {
          lang = 'es'
        } else if (lang.indexOf('pt') >= 0) {
          lang = 'pt'
        } else if (lang.indexOf('vi') >= 0) {
          lang = 'vi'
        } else {
          lang = 'en'
          I18n.languageState = 1;
        }
        data.language = lang
        console.log('I18n.languageState:', I18n.languageState, data.language)
        resolve(data)
      })
    } else {
      I18n.languageState = 2;
      resolve(APP_INFO)
    }
  })
}

/**
 * 获取游戏ui配置信息，如是否影藏 开始按钮  加入按钮 结束弹窗
 */
// export function getGameConfig(): Promise<any> {
//   let GAME_UI_CONFIG = {
//     "uiConfig": { // UI相关的配置
//       "joinBtn": { // 控制加入按钮
//         "hide": Boolean(getUrlParam('hideJoinBtn'))
//       },
//       "startGameBtn": { // 控制开始按钮
//         "hide": Boolean(getUrlParam('hideStartGameBtn'))
//       },
//       "gameOverDialog": { // 控制游戏结束弹窗
//         "hide": Boolean(getUrlParam('hideGameOverDialog'))
//       },
//       "lobbyPlayers": { // 控制大厅游戏位
//         "noAction": Boolean(getUrlParam('noAction')), //是否禁止游戏位响应加入事件
//         "hide": Boolean(getUrlParam('lobbyPlayers')) //是否隐藏大厅游戏位ui
//       },
//     }
//   }
//   console.log('---noAction',getUrlParam('noAction'),Boolean(getUrlParam('noAction')))
//   return new Promise((resolve) => {
//     if (sealBridge.isSeal && !sealBridge.debug) {
//       sealBridge.call('getGameConfig', {}, (data: unknown) => {
//         resolve(data)
//       })
//     } else {
//       resolve(GAME_UI_CONFIG)
//     }
//   })
// }

/**
 * 从客户端获取临时凭证
 * @returns ILoginRes
 */
export function getCode(): Promise<ILoginRes> {
  let LOGIN_INFO = {
    code: '',
    appId: '',
    appKey: '',
  }
  return new Promise((resolve) => {
    if (sealBridge.isSeal && !sealBridge.debug) {
      sealBridge.call('login', {}, (data) => {
        resolve(data as ILoginRes)
      })
    } else {
      resolve(LOGIN_INFO)
    }
  })
}

/**
 * 从客户端获取Token
 * @returns ILoginRes
 */
export function getToken(): Promise<string> {
  return new Promise((resolve) => {
    if (sealBridge.isSeal && !sealBridge.debug) {
      sealBridge.call('login', {}, (data: ILoginRes) => {
        resolve(data?.code)
      })
    } else {
      resolve(getUrlParam1('userId'))
    }
  })
}

/**
 * 获取GameSubType。
 * @returns 
 */
export function getGameSubType(): Promise<any> {
  return new Promise(async (resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call('getGameSubType', {}, (data) => {
        const { gameSubType } = data
        resolve(gameSubType)
      })
    } else {
      resolve("")
    }
  })
}

/**
 * 获取ConfigId。
 * @returns 
 */
export function getGameConfigId(): Promise<any> {
  return new Promise(async (resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call('getGameConfigId', {}, (data) => {
        const { gameConfigId } = data
        resolve(gameConfigId)
      })
    } else {
      resolve("")
    }
  })
}

export function getRoomInfo(): Promise<any> {
  const PARTY_INFO = {
    roomId: roomId, // 派对id 5132644403684365951 5167883295437982335  5119606497881476223 5119606643910370431
    ownerId: userId, // 房主用户id 5053916942549704364，5196498940228474924
  }
  return new Promise(async (resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call('getGameRoomInfo', {}, (data) => {
        const { roomId, ownerId } = data as IGetRoomInfoRes
        resolve({
          roomId: roomId,
          ownerId,
        })
      })
    } else {
      resolve(PARTY_INFO)
    }
  })
}

export function getUserInfo(): Promise<any> {
  const PARTY_USER_INFO = {
    userId: getUrlParam1('userId') || '5073029512386800684', //5073029512386800684  5052441683560563884  (ps:测试数据，不要删)
    nickName: `name-${Math.floor(Math.random() * 1000)}`,
    gender: 1, //0：未知 1：男 2：女
    portrait: 'https://cdnimg.cqpiwan.com/sociality/2022/06/21/2947588494976067132.png', //头像
    portraitMask: ''
  }
  return new Promise((resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call('getUserInfo', {}, (data) => {
        const { userId, nickName, gender, portrait, portraitMask } = data as IGetUserInfoRes
        resolve({
          userId,
          nickName,
          gender,
          image: portrait,
          portrait,
          portraitMask
        })
      })
    } else {
      resolve(PARTY_USER_INFO)
    }
  })
}

/**
 * 获取游戏模式下屏幕信息用于做游戏页面适配
 * @returns
 */
export function getGameModePageInfo(): Promise<IGetRoomPageInfoRes> {
  let canvasHeight = commonUtil.getUITransform(find("Canvas")).height;
  let canvasWidth = commonUtil.getUITransform(find("Canvas")).width;
  console.log('canvasHeight', canvasHeight)
  console.log('canvasWidth', canvasWidth)
  const PAGE_INFO = {
    topPartHeight: canvasHeight * 0.02,//116
    middlePartHeight: canvasHeight * 9,//370
    bottomPartHeight: canvasHeight * 0.08,//130
  }
  return new Promise(async (resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call('getGameRoomInfo', {}, (data: IGetRoomInfoRes) => {
        const { topPartHeight, middlePartHeight, bottomPartHeight } =
          data as IGetRoomInfoRes
        if (!data.middlePartHeight) {
          resolve(PAGE_INFO)
        } else {
          resolve({ topPartHeight, middlePartHeight, bottomPartHeight })
        }

      })
    } else {
      resolve(PAGE_INFO)
    }
  })
}

/**
 * 同步游戏状态给APP
 * @param status
 * @param data
 */
export function NotifyGameStatus(status: GameStatusType, data: object = {}) {
  console.log(`===游戏调用APP,NotifyGameStatus:${status},data:`, data)
  sealBridge.call(
    'notifyGameStatus',
    {
      status: status,
      data: data,
    },
    (ret) => {
      console.log(`notifyGameStatus,status:${status},ret:${ret}`)
    },
  )
}

/**
 * notify sealSdk the game had preloaded
 */
export function notifyGamePreloaded(status: number = 1, msg: string = '') {
  console.info(`===游戏调用APP notifyGamePreloaded,游戏预初始化完成,status:${status},msg:${msg}`)
  sealBridge.call('notifyGamePreloaded', { status, msg }, (ret) => {
    console.info(`notifyGamePreloaded ret:${ret}`)
  })
}

/**
 * 同步玩家状态给APP
 * @param status
 * @param userId
 * @param data
 */
export function NotifyPlayerStatus(
  status: PlayerType,
  userId: string = '',
  data: object = {},
) {
  console.log(`===游戏调用APP,notifyPlayerStatus:${status},userId:${userId},data:`, data)
  sealBridge.call(
    'notifyPlayerStatus',
    {
      status: status,
      userId,
      data: data,
    },
    (ret) => {
      console.log(`notifyPlayerStatus,status:${status},ret:${ret}`)
    },
  )
}

/**
 * 通知业务更新皮肤
 */
export function NotifyUpdateGameSkin() {
  sealBridge.call('notifyUpdateGameSkin', {},
    (ret) => {
      console.log(`notifyUpdateGameSkin,ret:${ret}`)
    })
}

/**
 * 同步玩家礼物状态给APP
 * @param status
 * @param userId
 * @param data
 */
export function NotifyPlayerGiftStatus(
  data: string,
) {
  sealBridge.call(
    'notifyGiftStatus',
    {
      userId,
      data,
    },
    (ret) => {
      console.log(`notifyPlayerStatus,ret:${ret}`)
    },
  )
}

/**
 * 离开游戏房间
 * @param gameRoundId 房间id
 */
export function notifyLeaveGame() {
  console.log(`===游戏调用APP,NotifyLeaveGame:,userId:${userId}`)
  sealBridge.call(
    'notifyLeaveGame',
    {
      gameRoundId: globalData.gameRoundId,
    },
    (ret) => {
      console.log(`NotifyLeaveGame,status,ret:${ret}`)
    },
  )
}

/**
 * 通知游戏破产
 * @param gameRoundId 房间id
 */
export function notifyBustPlayerResult(coinInfo?: any) {
  let coinInfoJson = JSON.stringify(coinInfo ?? []);
  console.log(`===游戏调用APP,notifyBustStatus`)
  sealBridge.call(
    'notifyBustStatus',
    {
      resultUrl: "",
      userId: globalData.myUserId,
      gameRoundId: globalData.gameRoundId,
      rankInfo: coinInfoJson
    },
    () => { }
  )
}


/**
 * 再来一局
 * @param gameRoundId 房间id
 */
export function replayClick() {
  console.log(`===游戏调用APP,replayClick`)
  sealClick(
    {
      notifyName: 'replayClick',
      data: {
        userId: globalData.myUserId,
      }
    },
    () => { })
}

/**
 * 点击重置
 */
export function rechargeClick() {
  console.log(`===游戏调用APP,rechargeClick`)
    sealClick(
      {
        notifyName: 'rechargeClick',
        data: {
          userId: globalData.myUserId
        }
      },
      () => { })
}
/**
 * 获取缓存数据
 * @param paramName 
 * @returns 
 */
export function getStoreLocalData(
  paramName: string
) {
  return new Promise((resolve) => {
    sealBridge.call(
      'getStoreLocalData',
      {
        param: paramName
      },
      (ret: any) => {
        try {
          const { data, status } = ret
          console.log(`===游戏调用APP,getStoreLocalData`, ret);
          const resInfo = status === "success" ? JSON.parse(data) : null;
          resolve(resInfo)
        } catch (e) {
          resolve(null)
        }
      },
    )
  })
}


export function getClientAdaptInfo() {
  return new Promise((resolve) => {
    sealBridge.call(
      'getClientAdaptInfo',
      {},
      (ret: any) => {
        try {
          const { data, status } = ret
          console.log(`===游戏调用APP,getClientAdaptInfo`, ret);
          const resInfo = status === "success" ? JSON.parse(data) : null;
          resolve(resInfo)
        } catch (e) {
          resolve(null)
        }
      },
    )
  })
}



export function getEffectSoundState(userid) {
  return new Promise((resolve) => {
    if (sealBridge.isSeal) {
      sealBridge.call(
        'getEffectSoundState',
        { userId: userid },// globalData.myUserId
        (ret: IGameEffectStatus) => {
          const { status, music_switch_on, sound_effect_switch_on } = ret
            cs.log("getEffectSoundState", userId, music_switch_on, sound_effect_switch_on, status)
          if (status === 'success') {
            resolve({music_switch_on, sound_effect_switch_on})
          } else {
            resolve({music_switch_on: true, sound_effect_switch_on: true})
          }
        },
      )
    } else {
      resolve({music_switch_on: true, sound_effect_switch_on: true})
    }
  })
}

export function traceReport(eventName: string, data: Record<any, any>) {
  return new Promise((resolve) => {
    sealBridge.call("traceReport", {
      event: eventName,
      data: JSON.stringify(data)
    }, function () { });
    resolve('')
  })
}

/**
 * @zh 上报闪电平台打点 @en send to shine platform
 */
export function rdsTraceReport(eventName: string, data: Record<any, any>) {
  return new Promise((resolve) => {
    sealBridge.call("rdsTraceReport", {
      rds: eventName,
      data: JSON.stringify(data)
    }, function () { });
    resolve('')
  })
}


export function notifyGameGuideFinish() {
  sealBridge.call(
    'notifyGameGuideFinish',
    {},
    () => {
    },
  )
}

/**
 * @zh 通知显示/隐藏头像或礼物图标
 */
export function notifyDisplayPlayerIcon(userId: string, isVisiable: boolean) {
  return new Promise((resolve) => {
    sealBridge.call("notifyDisplayPlayerIcon", {
      userId,
      isDisplay: !!isVisiable
    }, function () { });
    resolve('')
  })
}

/**
 * @zh 通知游戏大厅加载完成  @en gamehall loaded
 */
let isSuccessLoadHall = false
export function notifyGamehallLoaded() {
  const callback = () => {
    console.log('notifyGamehallLoaded handler', isSuccessLoadHall)
    if (isSuccessLoadHall) return
    sealBridge.call(
      'notifyGamehallLoaded',
      {},
      (ret) => {
        console.log('notifyGamehallLoaded callback', JSON.stringify(ret))
        isSuccessLoadHall = ret?.status === 'success'
      }
    )
    setTimeout(callback, 200)
  }
  callback()
}

/**
 * @zh 获取游戏额外配置 @en get extra config about game info
 */
export function getGameExtraConfig() {
  return new Promise((resolve) => {
    if (sealBridge.isSeal) {
      console.log("调用getGameExtraConfig")
      sealBridge.call(
        "getGameExtraConfig",
        {},
        (ret) => {
          console.log("游戏皮肤数据", ret?.data)
          let data = ret?.data
          try {
            data = JSON.parse(data)
          } catch (e) { }
          console.log("皮肤数据", JSON.stringify(data?.ludoGameSkinConfig || {}))
          resolve(data?.ludoGameSkinConfig)
        })
    } else {
      resolve({})
    }
  })
}

/**
 * @zh 通知控制游戏视图状态 @en update native view
 * @param layer  原生层内容
 * @param isVisiable 是否显示
 */
export function notifyGameViewState(layer: ENativeLayer, isVisiable: boolean) {
  if (sealBridge.isSeal) {
    sealBridge.call("notifyGameViewState", {
      type: layer, // 0是底部栏，目前只有这个，以后拓展
      state: isVisiable ? 0 : 1,// 0显示，1隐藏
    }, function(ret) {});
  }
}

