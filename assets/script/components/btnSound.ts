import { Component, _decorator } from "cc";
import { AudioMgr } from "../manager/audioMgr";
import ButtonPlus from "./buttonPlus";

const { ccclass, property } =_decorator;

@ccclass("BtnSound")
export default class BtnSound extends Component {

    onEnable() {
        this.resetBtn();
    }

    resetBtn() {
        // Button.prototype["onTouchEndedClone"] = Button.prototype["_onTouchEnded"];
        // Button.prototype["_onTouchEnded"] = function (event) {
        //     if (!ButtonPlus.state) SoundMgr.instance.play("audio/sound_btn")
        //     ButtonPlus.state = false;
        //     this.onTouchEndedClone(event);
        // }
    }

    onClickEffect() {
        if (!ButtonPlus.state) AudioMgr.instance.play("audio/sound_btn")
        ButtonPlus.state = false;
    }
}
