import { Component, Label, Layout, Sprite, SpriteFrame, _decorator } from "cc";

const { ccclass, property } = _decorator;

@ccclass("GuideResultManager")
export default class GuideResultManager extends Component {

    @property(Layout)
    userboxLayer: Layout

    @property(Sprite)
    rankBg: Sprite

    @property(Label)
    nickNameLabel: Label

    @property(Sprite)
    userSprite: Sprite

    @property(SpriteFrame)
    leftResultAlignTexture: SpriteFrame

    @property(SpriteFrame)
    rightResultAlignTexture: SpriteFrame

    public initGameResultView(isLeftLayout: boolean, userName: string, usersp: SpriteFrame) {
        this.nickNameLabel.string = userName
        if (usersp) this.userSprite.spriteFrame = usersp
        this.rankBg.spriteFrame = isLeftLayout ? this.leftResultAlignTexture : this.rightResultAlignTexture
        this.nickNameLabel.horizontalAlign = isLeftLayout ? Label.HorizontalAlign.LEFT : Label.HorizontalAlign.RIGHT
        this.userboxLayer.horizontalDirection = isLeftLayout ? Layout.HorizontalDirection.LEFT_TO_RIGHT : Layout.HorizontalDirection.RIGHT_TO_LEFT
    }

}
