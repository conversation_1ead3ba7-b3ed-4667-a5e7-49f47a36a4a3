import { GAME_MODE, END_MODE } from '../network/constants';
import GameConfig from '../gameConfig';
import globalData from '../globalData';
import { LocalGameMgr } from '../manager/localGameMgr';

/**
 * 游戏模式切换工具
 * 提供快速切换本地/网络模式的功能
 */
export class GameModeSwitch {
    
    /**
     * 切换到本地游戏模式
     */
    public static switchToLocalMode(): void {
        console.log('🎮 切换到本地游戏模式');
        
        // 修改配置
        GameConfig.FORCE_LOCAL_MODE = true;
        GameConfig.DISABLE_NETWORK = true;
        
        // 重置游戏状态
        this.resetGameState();
        
        console.log('✅ 已切换到本地模式');
    }
    
    /**
     * 切换到网络游戏模式
     */
    public static switchToNetworkMode(): void {
        console.log('🌐 切换到网络游戏模式');
        
        // 修改配置
        GameConfig.FORCE_LOCAL_MODE = false;
        GameConfig.DISABLE_NETWORK = false;
        
        // 重置游戏状态
        this.resetGameState();
        
        console.log('✅ 已切换到网络模式');
    }
    
    /**
     * 设置本地游戏配置
     */
    public static setLocalGameConfig(config: {
        gameMode?: GAME_MODE,
        endMode?: END_MODE,
        playerCount?: number,
        enableAI?: boolean,
        aiThinkTime?: number
    }): void {
        console.log('⚙️ 设置本地游戏配置:', config);
        
        // 更新配置
        if (config.gameMode !== undefined) {
            GameConfig.LOCAL_GAME_CONFIG.gameMode = config.gameMode;
        }
        if (config.endMode !== undefined) {
            GameConfig.LOCAL_GAME_CONFIG.endMode = config.endMode;
        }
        if (config.playerCount !== undefined) {
            GameConfig.LOCAL_GAME_CONFIG.playerCount = config.playerCount;
        }
        if (config.enableAI !== undefined) {
            GameConfig.LOCAL_GAME_CONFIG.enableAI = config.enableAI;
        }
        
        console.log('✅ 本地游戏配置已更新');
    }
    
    /**
     * 重置游戏状态
     */
    private static resetGameState(): void {
        // 重置全局数据
        globalData.resetGlobalData();
        
        // 停止当前游戏
        const localGameMgr = LocalGameMgr.instance;
        if (localGameMgr) {
            localGameMgr.stopLocalGame();
        }
    }
    
    /**
     * 获取当前模式
     */
    public static getCurrentMode(): 'local' | 'network' {
        return GameConfig.FORCE_LOCAL_MODE ? 'local' : 'network';
    }
    
    /**
     * 获取预设配置
     */
    public static getPresetConfigs() {
        return {
            // 经典4人对战
            classic4Player: {
                gameMode: GAME_MODE.FOUR_BATTLE,
                endMode: END_MODE.CLASSIC,
                playerCount: 4,
                enableAI: true
            },
            
            // 快速2人对战
            quick2Player: {
                gameMode: GAME_MODE.TWO_BATTLE,
                endMode: END_MODE.QUICK,
                playerCount: 2,
                enableAI: true
            },
            
            // 1v1对战
            oneVsOne: {
                gameMode: GAME_MODE.ONE_VS_ONE,
                endMode: END_MODE.CLASSIC,
                playerCount: 2,
                enableAI: true
            },
            
            // 2v2团队模式
            twoVsTwo: {
                gameMode: GAME_MODE.TWO_VS_TWO,
                endMode: END_MODE.TWO_VS_TWO,
                playerCount: 4,
                enableAI: true
            }
        };
    }
    
    /**
     * 应用预设配置
     */
    public static applyPresetConfig(presetName: keyof ReturnType<typeof GameModeSwitch.getPresetConfigs>): void {
        const presets = this.getPresetConfigs();
        const config = presets[presetName];
        
        if (!config) {
            console.error('❌ 未找到预设配置:', presetName);
            return;
        }
        
        console.log(`🎯 应用预设配置: ${presetName}`, config);
        this.setLocalGameConfig(config);
    }
}

/**
 * 全局快捷方法
 * 可以在浏览器控制台中直接使用
 */
declare global {
    interface Window {
        // 游戏模式切换
        switchToLocal: () => void;
        switchToNetwork: () => void;
        
        // 预设配置
        setClassic4Player: () => void;
        setQuick2Player: () => void;
        setOneVsOne: () => void;
        setTwoVsTwo: () => void;
        
        // 游戏控制
        restartLocalGame: () => void;
        stopLocalGame: () => void;
        
        // 状态查询
        getGameMode: () => string;
        getGameStatus: () => void;
    }
}

// 注册全局快捷方法
if (typeof window !== 'undefined') {
    // 模式切换
    window.switchToLocal = () => GameModeSwitch.switchToLocalMode();
    window.switchToNetwork = () => GameModeSwitch.switchToNetworkMode();
    
    // 预设配置
    window.setClassic4Player = () => GameModeSwitch.applyPresetConfig('classic4Player');
    window.setQuick2Player = () => GameModeSwitch.applyPresetConfig('quick2Player');
    window.setOneVsOne = () => GameModeSwitch.applyPresetConfig('oneVsOne');
    window.setTwoVsTwo = () => GameModeSwitch.applyPresetConfig('twoVsTwo');
    
    // 游戏控制
    window.restartLocalGame = () => {
        const localGameMgr = LocalGameMgr.instance;
        if (localGameMgr) {
            localGameMgr.restartLocalGame();
        } else {
            console.log('❌ 本地游戏管理器未找到');
        }
    };
    
    window.stopLocalGame = () => {
        const localGameMgr = LocalGameMgr.instance;
        if (localGameMgr) {
            localGameMgr.stopLocalGame();
        } else {
            console.log('❌ 本地游戏管理器未找到');
        }
    };
    
    // 状态查询
    window.getGameMode = () => GameModeSwitch.getCurrentMode();
    window.getGameStatus = () => {
        console.log('=== 游戏状态 ===');
        console.log('当前模式:', GameModeSwitch.getCurrentMode());
        console.log('游戏状态:', globalData.gameInfo.gameStatus);
        console.log('当前玩家:', globalData.currentOpUserId);
        console.log('玩家列表:', globalData.gameInfo.playerInfo);
        console.log('棋子信息:', globalData.gameInfo.chessInfo);
    };
    
    console.log('🎮 游戏模式切换工具已加载');
    console.log('💡 可用命令:');
    console.log('  switchToLocal()     - 切换到本地模式');
    console.log('  switchToNetwork()   - 切换到网络模式');
    console.log('  setClassic4Player() - 经典4人对战');
    console.log('  setQuick2Player()   - 快速2人对战');
    console.log('  setOneVsOne()       - 1v1对战');
    console.log('  setTwoVsTwo()       - 2v2团队模式');
    console.log('  restartLocalGame()  - 重新开始本地游戏');
    console.log('  stopLocalGame()     - 停止本地游戏');
    console.log('  getGameMode()       - 获取当前模式');
    console.log('  getGameStatus()     - 查看游戏状态');
}
