<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>angle</key>
	<real>90</real>
	<key>angleVariance</key>
	<real>17</real>
	<key>blendFuncDestination</key>
	<integer>1</integer>
	<key>blendFuncSource</key>
	<integer>770</integer>
	<key>duration</key>
	<real>1</real>
	<key>emitterType</key>
	<real>0</real>
	<key>finishColorAlpha</key>
	<real>0</real>
	<key>finishColorBlue</key>
	<real>1</real>
	<key>finishColorGreen</key>
	<real>1</real>
	<key>finishColorRed</key>
	<real>1</real>
	<key>finishColorVarianceAlpha</key>
	<real>1</real>
	<key>finishColorVarianceBlue</key>
	<real>0</real>
	<key>finishColorVarianceGreen</key>
	<real>0</real>
	<key>finishColorVarianceRed</key>
	<real>0</real>
	<key>finishParticleSize</key>
	<real>15</real>
	<key>finishParticleSizeVariance</key>
	<real>5</real>
	<key>gravityx</key>
	<real>0</real>
	<key>gravityy</key>
	<real>30</real>
	<key>maxParticles</key>
	<real>171</real>
	<key>maxRadius</key>
	<real>0</real>
	<key>maxRadiusVariance</key>
	<real>0</real>
	<key>minRadius</key>
	<real>0</real>
	<key>particleLifespan</key>
	<real>1.5</real>
	<key>particleLifespanVariance</key>
	<real>0.7</real>
	<key>radialAccelVariance</key>
	<real>10</real>
	<key>radialAcceleration</key>
	<real>15</real>
	<key>rotatePerSecond</key>
	<real>0</real>
	<key>rotatePerSecondVariance</key>
	<real>0</real>
	<key>rotationEnd</key>
	<real>-90</real>
	<key>rotationEndVariance</key>
	<real>0.0</real>
	<key>rotationStart</key>
	<real>90</real>
	<key>rotationStartVariance</key>
	<real>-180</real>
	<key>sourcePositionVariancex</key>
	<real>55</real>
	<key>sourcePositionVariancey</key>
	<real>50</real>
	<key>sourcePositionx</key>
	<real>362.95441595441594</real>
	<key>sourcePositiony</key>
	<real>678.4017094017094</real>
	<key>speed</key>
	<real>99</real>
	<key>speedVariance</key>
	<real>0</real>
	<key>startColorAlpha</key>
	<real>1</real>
	<key>startColorBlue</key>
	<real>1</real>
	<key>startColorGreen</key>
	<real>1</real>
	<key>startColorRed</key>
	<real>1</real>
	<key>startColorVarianceAlpha</key>
	<real>0</real>
	<key>startColorVarianceBlue</key>
	<real>0</real>
	<key>startColorVarianceGreen</key>
	<real>0</real>
	<key>startColorVarianceRed</key>
	<real>0</real>
	<key>startParticleSize</key>
	<real>24</real>
	<key>startParticleSizeVariance</key>
	<real>20</real>
	<key>tangentialAccelVariance</key>
	<real>5</real>
	<key>tangentialAcceleration</key>
	<real>10</real>
	<key>textureFileName</key>
	<string>particle_texture.png</string>
	<key>textureImageData</key>
	<string>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</string>
</dict>
</plist>
