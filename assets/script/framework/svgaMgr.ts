import { ImageAsset, Node, Sprite, SpriteFrame, Texture2D } from "cc";
import { parseSvgaFormat } from './../utils/svgaBuffer'

interface ISvgaResouce {
  isCache: boolean
  spriteFrames: SpriteFrame[]
}

export default class SvgaMgr {
  private static _instance: SvgaMgr
  public _svgaSourceMap: Map<string, ISvgaResouce> = new Map()

  public static get instance() {
    // SvgaMgr.instance.playSvga(this._nameLayerNode?.getChildByName('vipSprite'), 'http://*************:8080/test.svga', -1)
    if (!this._instance) this._instance = new SvgaMgr()
    return this._instance
  }

  /**
   * @zh 播放Svga @en plqy SVGA animation
   * @param node 精灵节点  sprite node
   * @param svgaUrl svga地址
   * @param playCount 动画播放次数， -1为重复播放 默认播放1次
   */
  public async playSvga(node?: Node, svgaUrl?: string, playCount: number = 1) {
    try {
      const { fps, frames } = await this._parseSvga(svgaUrl)
      if (!frames?.length || !Array.isArray(frames)) throw new Error(`too few frames`)
      const sp = node?.getComponent(Sprite)
      if (!sp) throw new Error(`can't find sprite component`)
      const svgaResource = this._svgaSourceMap.get(svgaUrl)
      if (svgaResource?.isCache) {
        this._runFrameAnimation(sp, svgaResource.spriteFrames, fps, playCount)
        return
      }
      let loadTask = []
      frames.forEach((base64: string) => {
        loadTask.push(this._convertBase64ToSpriteFrame(base64))        
      })
      const spriteFrames: SpriteFrame[] = await Promise.all(loadTask)
      this._runFrameAnimation(sp, spriteFrames, fps, playCount)
      this._svgaSourceMap.set(svgaUrl, { isCache: true, spriteFrames })
      loadTask = null
    } catch(e) {
      console.error('playSvga error', e)
    }
  }

  private async _parseSvga(svgaUrl: string) {
    try {
      if (!svgaUrl) throw new Error(`can't match svgaUrl param`)
      return await parseSvgaFormat(svgaUrl)
    } catch (error) {
      console.error("parse svga error", JSON.stringify(error))
      return null
    }
  }

  private _convertBase64ToSpriteFrame(base64: string): Promise<SpriteFrame> {
    return new Promise((resolve) => {
      const image = new Image();        
        image.src = base64;
        image.onload = function() {
          const texture = new Texture2D()
          texture.image = new ImageAsset(image)
          const _frame = new SpriteFrame()
          _frame.texture = texture
          resolve(_frame)
        }
        image.onerror = function() {
          resolve(null)
        }
    })
  }

  private _runFrameAnimation(sp: Sprite, spriteFrames: SpriteFrame[], fps: number, playCount: number): void {
    let aIndex = 0
    let aCount = 0
    const aMaxCount = spriteFrames.length
    const callback = () => {
      setTimeout(() => {
        if (sp?.node?.isValid) {
          sp.spriteFrame = spriteFrames[aIndex]
          ++aIndex
          if (aIndex >= aMaxCount) {
            ++aCount
            aIndex = 0
            if (playCount !== -1 && aCount === playCount) return
          }
          callback()
        }
      }, 1000 / fps)
    }
    callback()
  }
}
