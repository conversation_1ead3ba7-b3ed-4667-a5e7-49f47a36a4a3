import { Component, instantiate, Prefab, Node, v3, _decorator, view } from "cc";
import { Utils } from "../framework/frameworkUtils";
import { LoadMgr } from "../framework/loadMgr";
import { PoolKey, PoolMgr } from "../framework/poolMgr";
import globalData from "../globalData";
import ViewManager from "../manager/view";
import { commonUtil } from "../utils/commonUtil";
import { convertWorldCoordinate, loadLoaclResource } from "../utils/utils";
import CoinManager from "./coin";

const { ccclass } = _decorator;

export enum CoinAnimEnum {
    Wave,
    Move,
    Scale,
}

export interface coinAniConfig {
    coinNum: number, // coin count
    aniDuration: number // animation duration
    userIndex: number // loser userIndex
}

@ccclass("CoinListManager")
export default class CoinListManager extends Component {
    private coinPrefab: Node

    private coinMgrPool = []

    protected async onLoad() {
        ViewManager.coinListScript = this;
        this.coinPrefab = await LoadMgr.instance.createPrefab("prefab/anim/coin");
    }


    getNode() {
        return this.node;
    }

    /**
     * @description craete coin node
     */
    createCoinNode() {
        let node = null;
        if (this.coinPrefab) {
            node = instantiate(this.coinPrefab)
            node.parent = this.node
        }
        return node
    }

    getCoinNode() {
        let node = PoolMgr.instance.get(PoolKey.Coin);
        if (!node) {
            return this.createCoinNode();
        }
        node.parent = this.node;
        node.active = true;
        return node;
    }

    resetView() {
        this.coinMgrPool.forEach((coinMgr) => {
            if (coinMgr?.node?.isValid) {
                commonUtil.setActive(coinMgr.node, false)
                coinMgr.node.parent = null;
                PoolMgr.instance.push(PoolKey.Coin, coinMgr.node);
            }
        })
        ViewManager?.topAreaScript?.updateAllInfo()
        this.coinMgrPool = [];
    }

    public async playSlideAnimation(aniConfig: coinAniConfig) {
        this.resetView()
        const player = ViewManager.getPlayerMgr(aniConfig?.userIndex)
        if (!aniConfig || !player) return
        const finalVec = convertWorldCoordinate(player._avatorNode)
        for (let i = 0; i < aniConfig.coinNum; i++) {
            const targetNode = this.getCoinNode()
            const coinManager = targetNode?.getComponent(CoinManager)
            if (!targetNode.isValid || !coinManager) continue
            this.coinMgrPool.push(coinManager)

            const targetNodeVec = commonUtil.convertToNodeSpaceAR(coinManager.view['CoinIcon'], v3(finalVec.x, finalVec.y));
            commonUtil.setPosition(coinManager.view['CoinIcon'], targetNodeVec)
            commonUtil.setPosition(coinManager.view['ShineEffect'], targetNodeVec)
            coinManager?.playCoinMoveAnimation(aniConfig.userIndex, i === 0)

            if (i + 1 === aniConfig.coinNum) {
                await Utils.timeOut(this, .3)
                this.resetView()
            } else {
                await Utils.timeOut(this, aniConfig.aniDuration)
            }
        }
    }
}
