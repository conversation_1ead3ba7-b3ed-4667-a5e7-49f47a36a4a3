<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="game.ludo.test"
    android:installLocation="auto">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>

    <application
        android:extractNativeLibs="true"
        android:allowBackup="true"
        android:label="@string/app_name"
        android:usesCleartextTraffic="true"
        android:icon="@mipmap/ic_launcher">

        <!-- Tell CocosNativeActivity the name of our .so -->
        <meta-data android:name="android.app.lib_name"
            android:value="cocos" />

        <activity
            android:name="com.cocos.game.AppActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:launchMode="singleTask"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.cocos.lib.CocosEditBoxActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
    </application>

</manifest>
