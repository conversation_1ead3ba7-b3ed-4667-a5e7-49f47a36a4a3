import { _decorator, Button ,Node} from "cc";

const { ccclass, property } = _decorator;

@ccclass("ButtonPlus")
export default class ButtonPlus extends Button {
    public static state: boolean = false;

    onEnable() {
        super.onEnable();
        this.node.on(Node.EventType.TOUCH_START, this.soundState, this);
    }

    onDisable(): void {
        super.onDisable();
        this.node.off(Node.EventType.TOUCH_START, this.soundState, this);
    }

    soundState() {
        ButtonPlus.state = true;
    }
}
