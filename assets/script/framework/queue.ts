/**
 * @content 队列 
 * <AUTHOR>
 */

import { Utils } from "./frameworkUtils";
/**
 * 队列用到的参数
 */
export class Queue {
    private queueIdx: number = 0;
    private queueArr: Array<any> = [];
    private queueHasStart: boolean = false;
    private isClose: boolean = false;
    constructor() {
        this.isClose = false;
    }
    /**
     * 队列动画
     * @param thisArg 
     * @param Func 
     */
    queue(thisArg: any, Func: Function, queueEndCb?: Function, time: number = 0.25) {
        this.queueArr.push(Func);
        if (!this.queueHasStart) {
            this.queueFunc(thisArg, time, queueEndCb);
        }
    }

    private queueFunc(thisArg: any, time: number, queueEndCb: Function) {
        if (this.queueIdx >= this.queueArr.length) {
            this.queueIdx = 0;
            this.queueArr = [];
            queueEndCb && queueEndCb();
            this.queueHasStart = false;
            return;
        }
        this.queueHasStart = true;
        Utils.timeOut(thisArg, time).then(() => {
            if (!this.isClose) {
                const Func = this.queueArr[this.queueIdx];
                Func && Func();
                this.queueIdx++;
                this.queueFunc(thisArg, time, queueEndCb);
            }
        })
    }

    destroy() {
        this.isClose = true;
        this.queueIdx = 0;
        this.queueArr = [];
        this.queueHasStart = false;
    }

} 