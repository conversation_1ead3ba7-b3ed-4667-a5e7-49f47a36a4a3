/**
 * @content 加载load资源相关
 * <AUTHOR>
 */

import { resources, Prefab, instantiate, find, Texture2D, SpriteFrame, assetManager, Node } from "cc";
import { CachePrefabs } from "./cachePrefabs";
import { cs } from "./log";


export class LoadMgr {
    private static _ins: LoadMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new LoadMgr();
        return this._ins;
    }

    /**
     * 加载资源
     * @param url   资源路径
     * @param type  资源类型
     * @method loadRes
     */
    public async loadRes(url: string, type: any) {
        // cs.time("[Load]:time " + url);
        return new Promise((resolve, reject) => {
            resources.load(url, type, (err: any, res: any) => {
                // cs.timeEnd("[Load]:time " + url);
                if (err) {
                    cs.error(err.message || err);
                    reject([err, res]);
                    return;
                }
                resolve([null, res]);
            })
        }).catch((err) => {
            console.error("loadRes Error:" + err);
        })
    }

    /**
     * 创建prefab 
     * @param path 
     * @returns 
     */
    public async createPrefab(path: string) {
        let cacheNode = CachePrefabs.instance.prefabsCacheMap.get(path);
        if (cacheNode) {
            let node: Node = instantiate(cacheNode);
            node.setPosition(0, 0, 0);
            return node;
        }
        const info = await this.loadRes(path, Prefab);
        const err = info?.[0];
        if (err) return null;
        const prefab = info?.[1];
        CachePrefabs.instance.prefabsCacheMap.set(path, prefab);
        let node: Node = instantiate(prefab);
        node.setPosition(0, 0, 0);
        return node;
    }

    /**
     * 创建ui界面
     * @param {string} path ui路径
     * @param {Node} parent 父节点 默认为Canvas
     */
    public async create(path: string, parent?: Node) {
        let prefab = await this.getRes(path, Prefab);
        if (!prefab) {
            cs.warn("prefab is null !");
            return null;
        }
        let node: Node = instantiate(prefab);
        node.setPosition(0, 0, 0);
        if (!parent) parent = find("Canvas") as Node;
        parent.addChild(node);
        return node;
    }

    /**
     * 获取资源 针对于预制体
     * @param url 
     * @param type 
     */
    private async getRes(url: string, type: any) {
        let cacheNode = CachePrefabs.instance.prefabsCacheMap.get(url);
        if (cacheNode) return cacheNode;
        const info = await this.loadRes(url, type);
        const err = info?.[0];
        if (err) return null;
        const prefab = info?.[1];
        return prefab;
    }

    /**
     * 加载图片 
     * @param url 
     * @returns 
     */
    public async loadImg(url: string, ext?: string) {
        if (!url) {
            return new Promise((resolve, reject) => {
                resolve("");
            })
        }
        return new Promise((resolve, reject) => {
            this.loadNetImg(url, ext).then((imageAsset: Texture2D) => {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = imageAsset
                resolve(spriteFrame)
            }, (err) => {
                reject(err);
            });
        })
    }

    /**
     * 加载远程图片
     * @param url 
     * @returns 
     */
    public async loadNetImg(url: string, ext: string = "") {
        return new Promise((resolve, reject) => {
            assetManager.loadRemote(url, { ext: ext }, (err, imageAsset: Texture2D) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(imageAsset)
            });
        })
    }

    /**
     * 加载本地图片
     * @param url 
     * @returns 
     */
    public async loadImgFromRes(url: string) {
        const info = await this.loadRes(url, SpriteFrame);
        if (info[0]) {
            cs.error(info[0])
            return null;
        }
        return info[1]
    }

}