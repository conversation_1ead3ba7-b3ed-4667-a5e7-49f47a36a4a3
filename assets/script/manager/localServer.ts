
/**
 * 棋盘信息
 */
export const runPostion = {
    1: {
        move: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
            29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51],
        notEnterlast: 52,
        enterEnd: [100, 101, 102, 103, 104, 105],
        safe: [1, 9, 14, 22, 27, 35, 40, 48],
        obstacleInfo: 100,
    },
    2: {
        move: [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
            29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        notEnterlast: 13,
        enterEnd: [110, 111, 112, 113, 114, 115],
        safe: [1, 9, 14, 22, 27, 35, 40, 48],
        obstacleInfo: 110,
    },
    3: {
        move: [27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25],
        notEnterlast: 26,
        enterEnd: [120, 121, 122, 123, 124, 125],
        safe: [1, 9, 14, 22, 27, 35, 40, 48],
        obstacleInfo: 120,
    },
    4: {
        move: [40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
            27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],
        notEnterlast: 39,
        enterEnd: [130, 131, 132, 133, 134, 135],
        safe: [1, 9, 14, 22, 27, 35, 40, 48],
        obstacleInfo: 130,
    }
}
export class localServer {
    /** 
     * 基础用户的信息
     * 用户的四个棋子位置信息 
     * 当前用户四个骰子的位置
     */
    private _useAllChessInfo: Array<any> = [];

    /**
     * 当前用户四个棋子的信息
     * @chessInfo 
     */
    protected set useAllChessInfo(chessInfo: any) {
        this._useAllChessInfo = chessInfo;
    }

    /**
     * 获取当前四个棋子的信息
     */
    protected get useAllChessInfo() {
        return this._useAllChessInfo;
    }

}

