declare global {
  interface Window {
    lz: any
    LizhiJSBridge: any,
    gameLoadTime?: number
  }
}

export enum Env {
  dev = 'dev',
  pre = 'pre',
  prod = 'prod',
}

// 棋子类型
export enum ChessType {
  none = 0,
  red = 1,
  green = 2,
  yellow = 3,
  blue = 4,
}

// 表情动画
export enum FacialType {
  Sad = 'sad',
  Happy = 'happy',
}

// 玩家位置
export enum PlayerPos {
  RightTop = 1,
  RightBottom = 2,
  LeftBottom = 3,
  LeftTop = 4
}

export enum PlayerStatus {
  Gaming = 1, // 游戏中
  Quit = 3, // 退出
  Hosting = 2, // 托管
  Busting = 4, // 破产
  LEAVE = 5, // 离开
  NO1 = 10001
}

// 对话框
export enum DialogType {
  Facials = 'Facials', // 表情
  SelectDice = 'SelectDice', // 骰子
}

export enum PlayerOpStatus {
  Select, // select chess
  Move, // moving chess
  Other, // other option
  Roll,
}

// 所有棋子id
export let allChesses = ['1-1', '1-2', '1-3', '1-4', '2-1', '2-2', '2-3', '2-4', '3-1', '3-2', '3-3', '3-4', '4-1', '4-2', '4-3', '4-4']

// 棋子终点坐标
export const finalChessIndexs = [105, 115, 125 ,135]

// 棋子信息
export interface ChessInfo {
  chessId: string
  chessIndex: number
}

export enum DebugGameStatus {
  Match,
  Ready,
  Gaming
}

export enum GameScene {
  Result = 'result',
  Guide = 'guide',
  Rule = 'rule'
}

export enum Scene {
  Game = 'game',
  Guide = 'guide',
  Result = 'result'
}

export enum GameGuideStep {
  Null,
  Initial,
  RollDice,
  ClickChess,
  ReRollDice,
  ReClickChess,
  End
}
