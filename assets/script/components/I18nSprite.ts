import { _decorator, Component, Node, Sprite, SpriteFrame } from 'cc';
import { I18n } from '../framework/i18n';
const { ccclass, property } = _decorator;

@ccclass('i18nSprite')
export class i18nSprite extends Component {

    @property(SpriteFrame)
    enSpriteFrame: SpriteFrame = null!;

    @property(SpriteFrame)
    arSpriteFrame: SpriteFrame = null!;

    @property(Sprite)
    sprite: Sprite = null!;

    onLoad() {
        this.sprite.spriteFrame = I18n.languageState == 2 ? this.arSpriteFrame : this.enSpriteFrame;
    }
}

