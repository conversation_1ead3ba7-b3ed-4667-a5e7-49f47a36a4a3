import { _decorator, Component, director, JsonAsset } from 'cc';
import { cs } from '../framework/log';
import { UIMgr } from '../framework/uiMgr';
import GameConfig from '../gameConfig';
import globalData from '../globalData';
import { wsMgr } from '../manager/wsMgr';
import { GAME_STAGE, SOCKET_TYPE } from '../network/constants';
import { getAppInfo, getGameSubType,getGameConfigId, getUserInfo, notifyGamehallLoaded, rdsTraceReport } from '../seal/bridge';
import ExportAPI from '../seal/exportAPI';
import { sealBridge } from '../seal/SealBridge';
import { sealCallEvents } from '../seal/sealType';
import { IGetSystemInfoRes } from '../seal/types';
import { ESceneID, ESceneName } from '../type/gamehall';
import { loadLoaclResource } from '../utils/utils';
import { sensorResult } from '../utils/sensor';
const { ccclass, property } = _decorator;

@ccclass('gameHallScene')
export class gameHallScene extends Component {

    @property(wsMgr)
    wsMgr: wsMgr = null;

    private leaveGameEvent: string = "emit_gameHall_LeaveGame"

    private exportAPI: ExportAPI = null;

    //加载是否完成
    _loadComplete = false;
    //收到大厅切换到游戏指令时的时间戳
    _gameHallChangeGameDateTime = 0;
    //是否发送GamingRds
    _isSendGameingRds = false;
    //gameHallChangeGame游戏回调客户端时间
    _h5cbDateTime = 0
    //收到Gaming时间
    _gameingDateTime = 0;
    _gameHallRetryCnt = 0;
    _currentGameId = 0;
    _isBindRoom = false;
    _maxBindRoomRetryCnt = 3;

    onLoad() {
        console.log("gamehall onLoad")
        this.gameLoadScene()
        this.addEventMessage()
        director.addPersistRootNode(this.node)
        this.exportAPI = new ExportAPI();
        this.exportAPI.initGameHall();

        this.wsMgr.init();
        //     this.scheduleOnce(() => {
        //         globalData.token = "4323423"
        //         director.emit('INIT-PRELOAD', "6666666", null)
        //     }, 2);
        //     this.scheduleOnce(() => {
        //         director.emit(sealCallEvents.gameHall_changeGame, {
        //             currentGame: "0",
        //             switchToGameId: "5206662980335600255",
        //             roomId: "77777"
        //         }, null)
        //     }, 6);
    }

    start() {
        notifyGamehallLoaded()
    }

    onDestroy() {
        this.offEventMessage()
        this.exportAPI.onGameHallDestroy()
    }

    addEventMessage() {
        director.on('wsOpen', this.wsOpen.bind(this), this);
        director.on('handleGameStage', this.handleGameStage.bind(this), this);
        director.on('handleInvalidMessage', this.handleInvalidMessage.bind(this), this);
        director.on('gameLoaded', this.gameLoaded.bind(this), this);
        director.on(this.leaveGameEvent, this.leaveGame.bind(this), this);
        sealBridge.on(sealCallEvents.gameHall_changeGame, this.changeGame.bind(this));
    }

    offEventMessage() {
        director.off('wsOpen', this.wsOpen.bind(this), this);
        director.off(this.leaveGameEvent, this.leaveGame.bind(this), this);
        sealBridge.off(sealCallEvents.gameHall_changeGame, this.changeGame.bind(this));
        director.off('handleGameStage', this.handleGameStage.bind(this), this);
        director.off('handleInvalidMessage', this.handleInvalidMessage.bind(this), this);
        director.off('gameLoaded', this.gameLoaded.bind(this), this);
    }

    wsOpen() {
        if (globalData?.roomId) {
            globalData.socketSend(SOCKET_TYPE.BIND_ROOM, {});
        }
    }

    //收到gameHall_changeGame
    private changeGame = (data: any, callbackId: any) => {
        const { currentGame, switchToGameId, roomId } = data;
        if (this._currentGameId == switchToGameId) {
            //场景未加载完成，返回失败
            if (!this._loadComplete) {
                sealBridge.h5Callback(globalData.h5CbId, { "status": "多次调用切换同一场景，场景切换还未完成" })

                return;
            }
            //如果返回大厅直接返回成功。
            if (switchToGameId == ESceneID.Gamehall) {
                this._isBindRoom = false;
                sealBridge.h5Callback(callbackId, { "status": "success" })

                return;
            }
            //已经绑定房间成功返回成功
            if (this._isBindRoom) {
                sealBridge.h5Callback(callbackId, { "status": "success" })

                return
            }
            //绑定房间失败返回失败
            sealBridge.h5Callback(globalData.h5CbId, { "status": "BIND_ROOM失败" })
            //如果绑定房间重试超过上限+1次，则重新尝试
            if (this._gameHallRetryCnt >= this._maxBindRoomRetryCnt + 1) {
                this._gameHallRetryCnt = 0;
                globalData.h5CbId = callbackId;
                globalData.socketSend(SOCKET_TYPE.BIND_ROOM, {}, callbackId);

                return;
            }

            return;
        }
        this._currentGameId = switchToGameId;
        this._loadComplete = false;
        globalData.h5CbId = callbackId;
        globalData.h5GameId = switchToGameId;
        globalData.gameInfo.gameStatus = GAME_STAGE.INIT;

        switch (switchToGameId) {
            case ESceneID.Ludo:
                //BindRoom
                globalData.roomId = roomId;
                this._isSendGameingRds = false
                this._gameHallChangeGameDateTime = new Date().getTime();
                const cb = async () => {
                    const costTime = (new Date().getTime()) - this._gameHallChangeGameDateTime;
                    rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
                        link: "Cocos_LoadLudoGame",
                        roomId: globalData.roomId,
                        costTime: costTime,
                        totalCostTime: costTime,
                        result: "Success",
                    })
                    this._h5cbDateTime = new Date().getTime();
                    if (!globalData.WS.connected) {
                        this.rdsloadludoGameFail('WX_CLOSE');
                    }

                    cs.log(`【cocos】开始获取GameSubType`);
                    globalData.gameSubType = await getGameSubType();
                    cs.log(`【cocos】获取到GameSubType:${globalData.gameSubType}`);
                    cs.log(`【cocos】开始获取GameConfigId`);
                    globalData.gameConfigId = await getGameConfigId();
                    cs.log(`【cocos】获取到GameConfigId:${globalData.gameConfigId}`);
                    globalData.socketSend(SOCKET_TYPE.BIND_ROOM, {}, callbackId);
                }
                this._toggleScene(ESceneName.Ludo, null, cb)
                break;
            case ESceneID.LudoGuide:
                this._toggleScene(ESceneName.Ludo, callbackId)
                break
            default:
                this._isBindRoom = false;
                UIMgr.instance.hideDialog("prefab/dialog/gameOver");
                UIMgr.instance.hideDialog("prefab/2v2/gameOver_2v2");
                globalData.roomId = null;
                //UnBindRoom
                const unBindRoomCb = () => {
                    globalData.socketSend(SOCKET_TYPE.UNBIND_ROOM, {});
                    this._h5cbDateTime = new Date().getTime();
                    sealBridge.h5Callback(globalData.h5CbId, { "status": "success" })
                }
                // globalData.roomId = null;
                this._toggleScene(ESceneName.Gamehall, null, unBindRoomCb)
                break;
        }
    }

    //消息成功
    handleGameStage(type, message) {
        switch (type) {
            case SOCKET_TYPE.BIND_ROOM: {
                // console.log(`【cocos】BindRoom成功` + "|" + new Date().getTime());
                sealBridge.h5Callback(globalData.h5CbId, { "status": "success" })
                director.emit('initWebSocket');
                this._gameHallRetryCnt = 0;
                this._isBindRoom = true;
            }
                break;
            case SOCKET_TYPE.UNBIND_ROOM: {
                // console.log(`【cocos】UnBindRoom成功` + "|" + new Date().getTime());
            }
                break;
            case SOCKET_TYPE.GAME_INFO: {
                const { status } = message;
                if (status === GAME_STAGE.GAMEING) {
                    if (!this._isSendGameingRds) {
                        const totalCostTime = (new Date().getTime()) - this._gameHallChangeGameDateTime;
                        const costTime = (new Date().getTime()) - this._h5cbDateTime;
                        rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
                            link: "Cocos_GamePlaying",
                            roomId: globalData.roomId,
                            costTime: costTime,
                            totalCostTime: totalCostTime,
                            result: "Success"
                        })
                        this._gameingDateTime = new Date().getTime();
                        this._isSendGameingRds = true;
                    }
                }
            }
                break;
            default:
                break;
        }
    }

    //消息失败
    handleInvalidMessage(type, code, message) {
        switch (type) {
            case SOCKET_TYPE.BIND_ROOM: {
                this._isBindRoom = false;
                if (this._gameHallRetryCnt++ < this._maxBindRoomRetryCnt) {
                    this.scheduleOnce(() => {
                        globalData.socketSend(SOCKET_TYPE.BIND_ROOM, {});
                    }, 1);

                    return;
                }
                // console.log(`【cocos】BindRoom失败` + "|" + new Date().getTime());
                sealBridge.h5Callback(globalData.h5CbId, { "status": "绑定房间失败" })
                this.rdsloadludoGameFail('BIND_ROOM_FAIL');

            }
                break;
            case SOCKET_TYPE.UNBIND_ROOM: {
                // console.log(`【cocos】UnBindRoom失败` + "|" + new Date().getTime());
                // sealBridge.h5Callback(globalDataInstance.h5CbId, { "status": "fail" })
            }
                break;
            case SOCKET_TYPE.GAME_INFO: {
                const { status } = message;
                if (status === GAME_STAGE.GAMEING) {
                    if (!this._isSendGameingRds) {
                        const totalCostTime = (new Date().getTime()) - this._gameHallChangeGameDateTime;
                        const costTime = (new Date().getTime()) - this._h5cbDateTime;
                        rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
                            link: "Cocos_GamePlaying",
                            roomId: globalData.roomId,
                            costTime: costTime,
                            totalCostTime: totalCostTime,
                            result: "Fail",
                            failReason: "GAME_INFO失败"
                        })
                        this._gameingDateTime = new Date().getTime();
                        this._isSendGameingRds = true;
                    }
                }
            }
                break;
            default:
                break;
        }
    }

    gameLoaded() {
        const costTime = (new Date().getTime()) - this._gameingDateTime;
        const totalCostTime = (new Date().getTime()) - this._gameHallChangeGameDateTime;
        rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
            link: "Cocos_LoadGameBoard",
            roomId: globalData.roomId,
            costTime: costTime,
            totalCostTime: totalCostTime,
        })
        sensorResult({
            result_type: 'LudoFinishLoading',
        })
    }

    private _toggleScene(sceneName: string, callbackId?: any, cb?) {
        director.loadScene(sceneName, () => {
            // console.log(`【cocos】加载场景成功` + new Date().getTime());
            this._loadComplete = true;
            callbackId && sealBridge.h5Callback(callbackId, { "status": "success" })
            cb && cb();
        })
    }

    private gameLoadScene() {
        // director.preloadScene("game", () => {
        // });
    }

    private leaveGame() {
        // director.loadScene("gameHall");
        sealBridge.call("gameHall_leaveGame", {}, (ret) => {
            console.log("gameHall_leaveGame ", ret + "|" + new Date().getTime())
        });
    }

    private rdsloadludoGameFail(failReason: "LOAD_LUDO_FAIL" | "BIND_ROOM_FAIL" | "WX_CLOSE") {
        const costTime = (new Date().getTime()) - this._gameHallChangeGameDateTime;
        rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
            link: "Cocos_LoadLudoGame",
            roomId: globalData.roomId,
            costTime: costTime,
            totalCostTime: costTime,
            result: "Fail",
            failReason: "failReason"
        })
    }
}

