<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>diamond.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{41,41}</string>
                <key>spriteSourceSize</key>
                <string>{41,41}</string>
                <key>textureRect</key>
                <string>{{0,0},{41,41}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>reset-disabled.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{88,88}</string>
                <key>spriteSourceSize</key>
                <string>{88,88}</string>
                <key>textureRect</key>
                <string>{{41,0},{88,88}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>reset.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{88,88}</string>
                <key>spriteSourceSize</key>
                <string>{88,88}</string>
                <key>textureRect</key>
                <string>{{129,0},{88,88}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>resetSp.png</string>
            <key>size</key>
            <string>{217,88}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:4d123597ef8bd9f12ce81c9f8e5ed424:03372d4547187834843e62368daa1ffa:5d90c022b3c490cbfd42a42fef6363ac$</string>
            <key>textureFileName</key>
            <string>resetSp.png</string>
        </dict>
    </dict>
</plist>
