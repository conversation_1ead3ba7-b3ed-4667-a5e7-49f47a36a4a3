import { Component, Label, Node, tween, v3, _decorator } from "cc";
import { Easing } from "../framework/easing";
import { Utils } from "../framework/frameworkUtils";
import globalData from "../globalData";
import { AudioMgr } from "../manager/audioMgr";
import ViewManager from "../manager/view";
import { commonUtil } from "../utils/commonUtil";

const { ccclass, property } = _decorator;

@ccclass("BalanceManager")
export default class BalanceManager extends Component {
    @property(Node)
    coinBoxNode: Node

    @property(Label)
    positiveLabel: Label

    @property(Label)
    negativeLabel: Label

    marginWidth = 0

    private handleBalanceNode(balance: number) {
        commonUtil.setOpacity(this.node, balance === 0 ? 0 : 100);
        commonUtil.setActive(this.coinBoxNode, balance !== 0)
        commonUtil.setActive(this.positiveLabel?.node, balance > 0)
        commonUtil.setActive(this.negativeLabel?.node, balance < 0)
        const balanceStr = `${balance}`
        if (balance === 0) return
        if (balance > 0) {
            this.positiveLabel.string = `+${balanceStr}`
            this.setMarginwidth(this.positiveLabel.string)
        } else {
            this.negativeLabel.string = `${balanceStr}`
            this.setMarginwidth(this.negativeLabel.string)
        }
    }

    private setMarginwidth(balanceStr: string) {
        const len = balanceStr.length
        if (len > 4) {
            this.marginWidth = (len - 4) * 50
        } else {
            this.marginWidth = 0
        }
    }

    private playAnimation(userIndex: number) {
        return new Promise(async (resolve) => {
            if (!this.node.isValid || !ViewManager.getPlayerMgr(userIndex)) return
            const { px, py, pHeight } = ViewManager.getPlayerMgr(userIndex).getPlayerCoorInfo()
            const { x, y } = commonUtil.convertToNodeSpaceAR(this.node.parent, v3(px, py))
            const finalY = globalData.gameScaleRate * (y > 0 ? y + pHeight * 2.5: y - pHeight * 1.5)
            const isLeftMove = x > 0
            commonUtil.setAnchorX(this.node, isLeftMove ? 0 : 1)
            commonUtil.setPosition(this.node, v3(x + (isLeftMove ? 300 : -300), finalY, 0))
            await Utils.timeOut1(1000)
            if (!this.node.isValid) return
            const finalX = (isLeftMove ? -1 : 1) * this.marginWidth
            AudioMgr.instance.play('audio/sound_reduceAdd')
            commonUtil.setActive(this.node, true)
            console.log("playAnimation", finalX, finalY, globalData.gameScaleRate)
            tween(this.node)
                .to(.16, {
                    position: v3(finalX, finalY, 0)
                }, { easing: Easing.bounceIn })
                .to(.2, { position: v3(finalX + (isLeftMove ? 1 : -1) * 30, finalY, 0) })
                .delay(.675)
                .call(() => {
                    commonUtil.setActive(this.node, false)
                    resolve('')
                })
                .start();

            tween(commonUtil.getUIOpacity(this.node))
                .to(.16, {
                    opacity: 255
                }, {
                    easing: Easing.bounceIn
                })
                .delay(.75)
                .to(.125, { opacity: 0 })
                .start();
        })
    }

    public playBalanceAnimation(userIndex: number, balance: number) {
        this.handleBalanceNode(balance)
        this.playAnimation(userIndex)
    }
}
