import { _decorator, Node, Component, Label, tween, Vec3, v3 } from "cc"
import { commonUtil } from "../utils/commonUtil";

const { ccclass, property } = _decorator

@ccclass("Toast")
export default class Toast extends Component {
  static instance: Toast

  @property(Node)
  bgNode: Node = null; // 游戏节点

  @property(Label)
  label: Label

  private _targetTween
  private _verticleMargin = 36
  private _horizonMargin = 60

  onLoad() {
    Toast.instance = this
  }

  protected setLabel(str: string) {
    if (this.node.isValid) {
      this.label.string = str
      setTimeout(() => {
        let width = commonUtil.getWidth(this.label.node)
        setTimeout(() => {
          commonUtil.setWidth(this.bgNode, width + this._horizonMargin)
          commonUtil.setHeight(this.bgNode, commonUtil.getHeight(this.label.node) + this._verticleMargin)
        }, 20)
      }, 50)
    }
  }

  protected playAnimation(duration: number = 3000) {
    this.hide()
    this._targetTween = tween(commonUtil.getUIOpacity(this.node))
      .delay(0.05)
      .to(.1, { opacity: 255 }, {})
      .delay(duration / 1000)
      .to(.1, { opacity: 0 })
      .call(() => {
      })
      .start()
  }

  protected hide() {
    if (this.node.isValid) {
      this._targetTween?.stop()
      commonUtil.setOpacity(this.node, 0)
    }
  }

  /**
   * @en display toast tip @zh 显示提醒
   * @param txt 
   * @param duration 
   * @param position 
   */
  static show(txt: string, duration: number = 3000, position: Vec3 = v3(0, 0, 0)) {
    commonUtil.setPosition(Toast.instance?.node, position)
    Toast.instance?.setLabel(txt)
    Toast.instance?.playAnimation(duration)
  }

  /**
   * @en close toast tip @zh 关闭提醒
   * @param txt 
   * @param duration 
   * @param position 
   */
  static close() {
    Toast.instance?.hide()
  }
}
