import { Component, Node, _decorator } from "cc";
import globalData from "../globalData";

const { ccclass, property } = _decorator;

@ccclass("HostingManager")
export default class HostingManager extends Component {

  @property(Node)
  checkIconNode: Node = null

  // 点击托管
  public onClickHosting(e) {
    console.log("==onClickHosting", e.currentTarget.name)
    globalData.cancelHosting(e.currentTarget.name)
  }
}
