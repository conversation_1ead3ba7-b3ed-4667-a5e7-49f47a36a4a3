<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>red.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{0,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>green.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{64,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>yellow.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{128,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>blue.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{192,0},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>chess.png</string>
            <key>size</key>
            <string>{256,64}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:91e5edbbb7a8c41375dd569bea5880ec:6306a395850e83527b129ff68da027ff:c79d8b3933e8df135abe69e9fe2866d3$</string>
            <key>textureFileName</key>
            <string>chess.png</string>
        </dict>
    </dict>
</plist>
