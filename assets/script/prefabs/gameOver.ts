import { <PERSON>out, <PERSON>de, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>p<PERSON>, <PERSON>prite<PERSON><PERSON><PERSON>, tween, Tween, v3, Vec3, _decorator } from "cc";
import { DEBUG } from "cc/env";
import I18nLabel from "../components/i18nLabel";
import { EventMgr } from "../framework/eventMgr";
import { EventName } from "../framework/eventName";
import { Utils } from "../framework/frameworkUtils";
import { I18n } from "../framework/i18n";
import { LoadMgr } from "../framework/loadMgr";
import { cs } from "../framework/log";
import { Queue } from "../framework/queue";
import SuperPanel from "../framework/superPanel";
import globalData from "../globalData";
import { AudioMgr } from "../manager/audioMgr";
import { GameOverMgr } from "../manager/gameOverMgr";
import ViewManager from "../manager/view";
import { notifyLeaveGame } from "../seal/bridge";
import { sealClick } from "../seal/SealBridge";
import { commonUtil } from "../utils/commonUtil";
import { sensorAppViewScreen, sensorClick } from "../utils/sensor";
import settlementItem from "./settlementItem";

const { ccclass, property } = _decorator;

interface IStarObj { x: number, y: number, w: number, h: number }

const OpenState = { bankruptcy: 2, lose: 1, win: 0 }
@ccclass("GameOver")
export default class GameOver extends SuperPanel {
    private allItemArr: Array<{ node: Node, script: settlementItem }> = [];
    private queue: Queue = null;
    private gameFrame: number = 60;
    // 0 win 1 fail 2 bankruptcy  3 新手引导
    private openState: number = 0;
    //后改成resource加载
    @property(ScrollView)
    scrollView: ScrollView
    @property(SpriteFrame)
    ribbon: SpriteFrame = null;
    @property(SpriteFrame)
    trophy: SpriteFrame = null;
    @property(SpriteFrame)
    winRibbon: SpriteFrame = null;
    @property(SpriteFrame)
    winTrophy: SpriteFrame = null;

    private lightTween: any = null;
    private starTween: any = null;
    private starTween1: any = null;
    private layout: Layout = null;
    private trophyTween1: any = null;
    private trophyTween2: any = null;
    private ribbonTween: any = null;
    private ribbonTween1: any = null;
    private tween1: any = null;
    private tween2: any = null;
    private tween3: any = null;
    private tween4: any = null;

    protected reserve(): void {
        this.isAddBlockInputEvent = false;
        this.panelAnim.anim = {
            openIsAnim: false,
            openAnimType: -1,
            closeIsAnim: false,
            closeAnimType: -1
        }
    }

    /// TODO  待优化列表 超出屏幕部分就进行隐藏或者透明度进行调整
    init(data: any): void {
        super.init(data);
        this.openState = this.myData?.type;
        console.log("data", data, this.myData, this.openState);
        this.playSound(this.openState);
        this.allItemArr = [];
        this.queue = new Queue();
        this.initUi();
        cs.log('------------- game open game over! init');
        this.playAnim();

        sensorAppViewScreen({
            "$title": 'ludo游戏结果页'
        })
    }

    private playSound(type: any) {
        //0 破产 1 lose 2 win 
        switch (type) {
            case OpenState.bankruptcy:
                break;
            case OpenState.win:
                AudioMgr.instance.play('audio/sound_resultWin');
                break;
            case OpenState.lose:
                AudioMgr.instance.play('audio/sound_resultFail');
                break;
            default:
                break;
        }
    }

    protected onEnable(): void {
        super.onEnable();
    }

    private initUi() {
        if (this.openState == OpenState.bankruptcy || this.openState == OpenState.lose) {
            this.view["ribbon"].getComponent(Sprite).spriteFrame = this.ribbon;
            this.view["trophy"].getComponent(Sprite).spriteFrame = this.trophy;
        } else {
            this.view["ribbon"].getComponent(Sprite).spriteFrame = this.winRibbon;
            this.view["trophy"].getComponent(Sprite).spriteFrame = this.winTrophy;
        }
        // this.tools.visible("trophy", this.openState == 0)
        this.tools.visible("quitGameBtn", this.openState !== 3);
        this.tools.visible("nextGameBtn", this.openState !== 3);
        this.tools.visible("guideStartBtn", this.openState == 3);

        //
        this.tools.visible("btnArea", false);
        this.tools.opacity("btnArea", 0);

        this.tools.visible("ribbon", false);
        this.tools.opacity("ribbon", 0);
        // this.tools.scale("trophy", 0.3);
        commonUtil.setY(this.view["trophy"], 2);
        this.tools.opacity("trophy", 0);
        this.tools.scale("light", 0.6);
        this.tools.opacity("light", 0);
        this.view["light"].angle = 0;
        for (let i = 1; i <= 6; i++) {
            commonUtil.setHeight(this.view["star" + i], 0);
            commonUtil.setWidth(this.view["star" + i], 0);
            commonUtil.setPosition(this.view["star" + i], v3(0, 101, 0));
        }
        this.udpateI18nLab();
    }

    protected udpateI18nLab() {
        // win   离开游戏  下一局
        // fail 离开游戏  下一局
        // 破产  离开游戏  继续围观
        // 引导和这个里无关可以单独展示
        const keyStr = this.openState == OpenState.bankruptcy ? "continueToWatch" : "nextGame";
        const quitGameText = I18n.str(keyStr);
        this.tools.text("nextGameLab", quitGameText);
        this.view["quitGameLab"].getComponent(I18nLabel).updateText();
    }

    protected onDisable(): void {
        super.onDisable();
        this.allItemArr.forEach((value) => {
            value?.node?.destroy();
            value.script = null;
        })
        this.allItemArr = [];
        this.view["content"].removeAllChildren();
        if (this.lightTween) {
            this.lightTween?.removeSelf();
            this.lightTween = null;
        }
        if (this.starTween) {
            this.starTween?.removeSelf();
            this.starTween = null;
        }
        if (this.starTween1) {
            this.starTween1?.removeSelf();
            this.starTween1 = null;
        }
        Tween.stopAll();
    }

    protected onMessageEvent(): void {
        super.onMessageEvent();
        // this.view["nextGameBtn"].on("click", this.clickNextGameBtn, this);
        // this.view["quitGameBtn"].on("click", this.clickQuitGameBtn, this);
        this.view["guideStartBtn"].on("click", this.clickGuideStartBtn, this);
        EventMgr.on(EventName.EVENT_GAMEOVER_LAYOUT, this, this.updateLayout);
    }


    protected offMessageEvent(): void {
        super.offMessageEvent();
        // this.view["nextGameBtn"].off("click", this.clickNextGameBtn, this);
        // this.view["quitGameBtn"].off("click", this.clickQuitGameBtn, this);
        this.view["guideStartBtn"].off("click", this.clickGuideStartBtn, this);
        EventMgr.off(EventName.EVENT_GAMEOVER_LAYOUT, this, this.updateLayout);
    }

    private async createAllItem() {
        this.view["content"].removeAllChildren();
        await Utils.timeOut(this, 0.33);
        this.view["content"].getComponent(Layout).enabled = false;
        commonUtil.setHeight(this.view["content"], 549);
        let info = GameOverMgr.instance.coinInfo ?? [];

        if (info.length <= 0) {
            this.btnAnim();
        }
        let posArr = this.openState == OpenState.bankruptcy ? [-175, -305, -440, -575] : [-72, -219, -354, -489];//[-60, -195, -330, -465]
        for (let i = 0; i < info.length; i++) {
            this.createItem(i, posArr[i], info[i]);
        }
        this.updateLayout();
    }

    private async createItem(idx: number, posY: number, data: object) {
        let node = await LoadMgr.instance.createPrefab("prefab/settlementItem");
        commonUtil.setOpacity(node, 0);
        commonUtil.setY(node, posY - 20);
        const func = async () => {
            this.view["content"].addChild(node);
            let itemState = (this.openState == OpenState.bankruptcy && idx == 0) ? true : false;
            node.getComponent(settlementItem).init({ itemState, idx, data, openState: this.openState });
            this.tween3 = tween(node)
                .to(0.25, { position: v3(node.position.x, posY, node.position.z) })
                .call(() => {
                    this.tween3?.removeSelf();
                    this.tween3 = null;
                })
                .start();

            this.tween4 = tween(commonUtil.getUIOpacity(node))
                .to(0.25, { opacity: 255 })
                .call(() => {
                    this.tween4?.removeSelf();
                    this.tween4 = null;
                })
                .start();
        }
        this.queue.queue(this, func, async () => {
            this.btnAnim();
        }, 0.008)
    }

    /** 下一局游戏 */
    protected clickNextGameBtn() {
        AudioMgr.instance.play("audio/sound_btn")
        if (this.openState == 0 || this.openState == 1) {
            sensorClick({
                $title: 'ludo游戏结果页',
                $element_name: '再玩一局',
            })
            sealClick({
                notifyName: 'replayClick',
                data: { userId: globalData.myUserId, }
            }, () => {
                console.log('=replayClick callback')
            })
        } else if (this.openState == 2) {
            this.closePage();
            sensorClick({
                $title: '破产结算弹窗',
                $element_name: '继续围观',
            })
            sealClick({
                notifyName: 'observeClick',
                data: {
                    userId: globalData.myUserId,
                }
            }, () => {
                console.log('=observeClick callback')
            })
        }
        ViewManager.destroyGameOverView()
    }

    /** 离开游戏 */
    protected clickQuitGameBtn() {
        if (this.openState == 2) {
            sensorClick({
                $title: '破产结算弹窗',
                $element_name: '离开游戏',
            })
        } else {
            sensorClick({
                $title: 'ludo游戏结果页',
                $element_name: '放弃',
            })
        }
        // this.clickClosePage();
        notifyLeaveGame()
        AudioMgr.instance.play("audio/sound_btn")
        ViewManager.destroyGameOverView()
    }

    /** 点击引导开始按钮要做的事情 */
    private clickGuideStartBtn() {
        // this.clickClosePage();
        AudioMgr.instance.play("audio/sound_btn")
    }

    private updateLayout() {
        if (!this.layout) {
            this.layout = this.view["content"].getComponent(Layout)
        }
        this.layout.updateLayout();
    }

    /** 动画启动 */
    private async playAnim() {
        cs.log('------------- game open game over! playAnim');
        this.ribbonAnim();
        this.trophyAnim();
        this.createAllItem();
        this.starAnim();
    }

    private async btnAnim() {
        await Utils.timeOut(this, 0.008)
        this.tools.visible("btnArea", true);
        this.tools.opacity("btnArea", 0);
        commonUtil.setY(this.view["btnArea"], -530)
        this.tween1 = tween(this.view["btnArea"])
            .to(0.25, { position: v3(this.view["btnArea"].position.x, -510, this.view["btnArea"].position.z) })
            .call(() => {
                this.tween1?.removeSelf();
                this.tween1 = null;
            })
            .start();
        this.tween2 = tween(commonUtil.getUIOpacity(this.view["btnArea"]))
            .to(0.25, { opacity: 255 })
            .call(() => {
                this.tween2?.removeSelf();
                this.tween2 = null;
            })
            .start();
        this.view["content"].getComponent(Layout).enabled = true;
    }

    private async ribbonAnim() {
        return new Promise((resolve) => {
            const ribbonInitY = -36;
            const ribbonEndY = -66;
            const initOpacity = 0;
            commonUtil.setY(this.view["ribbon"], ribbonInitY);
            this.tools.visible("ribbon", true);
            this.tools.opacity("ribbon", initOpacity);
            this.ribbonTween = tween(this.view["ribbon"])
                .to(0.16, { position: v3(0, ribbonEndY, 0) })
                .call(() => {
                    resolve("")
                    this.ribbonTween?.removeSelf();
                    this.ribbonTween = null;
                })
                .start();
            this.ribbonTween1 = tween(commonUtil.getUIOpacity(this.view["ribbon"]))
                .to(0.16, { opacity: 255 })
                .call(() => {
                    this.ribbonTween1?.removeSelf();
                    this.ribbonTween1 = null;
                })
                .start();
        })
    }

    private async trophyAnim() {
        await Utils.timeOut(this, 0.08);
        this.lightAnim();
        if (this.view["trophy"]) {
            this.trophyTween1 = tween(this.view["trophy"])
                .to(0.25, { scale: Vec3.ONE, position: v3(this.view["trophy"].position.x, -30, this.view["trophy"].position.z) })
                .call(() => {
                    this.trophyTween1?.removeSelf();
                    this.trophyTween1 = null;
                })
                .start();
            this.trophyTween2 = tween(commonUtil.getUIOpacity(this.view["trophy"]))
                .to(0.17, { opacity: 255 })
                .call(() => {
                    this.trophyTween2?.removeSelf();
                    this.trophyTween2 = null;
                })
                .start();
        }
    }

    private lightAnim() {
        if (this.openState == OpenState.bankruptcy || this.openState == OpenState.lose) return;
        this.lightTween = tween(this.view["light"])
            .to(0.2, { scale: Vec3.ONE })
            .call(() => {
                tween(this.view["light"])
                    .by(10, { angle: 720 })
                    .repeatForever()
                    .start();
                this.lightTween?.removeSelf();
                this.lightTween = null;
            }).start();
    }

    private async starAnim() {
        if (this.openState == OpenState.bankruptcy || this.openState == OpenState.lose) return;
        await Utils.timeOut(this, 0.33);
        const hwArr: IStarObj[] = [{ w: 36, h: 34, x: -154, y: 121 }, { w: 52, h: 50, x: -183, y: 53 },
        { w: 26, h: 24, x: -96, y: 27.6 }, { w: 30, h: 28, x: 101.5, y: 29.5 },
        { w: 38, h: 36, x: 163.5, y: 61.5 }, { w: 38, h: 36, x: 141.5, y: 117.5 }]
        hwArr.forEach((value: IStarObj, idx) => {
            this.playStarAnim("star" + (idx + 1), value);
            tween(this.view["star" + (idx + 1)]).by(4, { angle: 720 })
                .repeatForever().start();
        })
    }

    private playStarAnim(name: string, obj: IStarObj) {
        this.starTween = tween(this.view[name])
            .to(0.5, { position: v3(obj.x, obj.y, this.view[name].position.z) })
            .call(() => {
                this.starTween?.removeSelf();
                this.starTween = null;
            })
            .start();

        this.starTween1 = tween(commonUtil.getUITransform(this.view[name]))
            .to(0.5, { width: obj.w, height: obj.h })
            .call(() => {
                this.starTween1?.removeSelf();
                this.starTween1 = null;
            })
            .start();
    }

}
