// ViewManager.notifyPlayerStatus(1, PlayerStatus.Hosting)
// 显示可以移动棋子的波纹效果
// let canMoveChesses: any = [
//   ['1-1', [6, 5]],
//   ['1-2', [6]],
//   ['1-3', [6]],
//   ['1-4', [6]],
// ];
// globalData.canMoveChessMap = canMoveChesses
// let canMoveChessMap: Map<string, number[]> = new Map(canMoveChesses);
// ViewManager.notifyPlayerSelectDice(canMoveChessMap)
// ViewManager.chessMove(
//   '1-2',
//   [46, 47, 48], ['1-1'],
//   JSON.parse(finalChessDatas), [100, 110],
//   [
//     { antes: 500, multiple: 1, userIndex: 1, userId: '111', eatChessNum: 2, coin: 2222, balance: -500 },
//     { antes: 500, multiple: 13, userIndex: 3, userId: '333', eatChessNum: 20, coin: 2222, balance: 500 },
//   ])
// ViewManager.chessMove('1-2', [45, 46, 47, 48], ['4-1'], JSON.parse(finalChessDatas), [100, 110], [
//   { antes: 500, multiple: 1, userIndex: 1, userId: '111', eatChessNum: 2, coin: 2222, balance: -500 },
//   { antes: 500, multiple: 13, userIndex: 3, userId: '333', eatChessNum: 20, coin: 2222, balance: 500 },
// ])

// ViewManager.baseComponentManager?.playNumEffectAnimation('111', `+${600}`, `+${5000}`,`${200}`)
// NotifyUpdateGameSkin()
// ViewManager.notifyDiceResult(1, 1, 6, [6, 3])
// ViewManager.notifyPlayerOpDice('111', 1, 5 * 1000, [6, 3])
// ViewManager.notifyResetDiceCountdown(1, 0)
// ViewManager.notifyPlayerDiceList(1, [6, 3])
// ViewManager.notifyResetDiceCountdown(1, 5 * 1000)
// setTimeout(() => {
//   ViewManager.notifyDiceResult('1', 1, 6, [6, 3])
// }, 3000)
   
