<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_9" orientation="landscape">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment version="1792" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13173"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="fm7-M6-edp"/>
                        <viewControllerLayoutGuide type="bottom" id="uRH-d6-mvd"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="812" height="375"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleAspectFill" insetsLayoutMarginsFromSafeArea="NO" image="LaunchScreenBackground.png" translatesAutoresizingMaskIntoConstraints="NO" id="YCC-wj-Gww" userLabel="Background">
                                <rect key="frame" x="0.0" y="0.0" width="812" height="375"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="YCC-wj-Gww" secondAttribute="bottom" id="Naz-ae-jWI"/>
                            <constraint firstAttribute="trailing" secondItem="YCC-wj-Gww" secondAttribute="trailing" id="myj-85-hk9"/>
                            <constraint firstItem="YCC-wj-Gww" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="qOq-Cg-doS"/>
                            <constraint firstItem="YCC-wj-Gww" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="xL7-Fo-4bl"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="50.399999999999999" y="373.15270935960592"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchScreenBackground.png" width="2208" height="1242"/>
    </resources>
</document>
