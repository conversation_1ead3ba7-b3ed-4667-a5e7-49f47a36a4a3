export enum SOCKET_TYPE {
  LOGIN = 121, // 登陆游戏
  CREATE_GAME = 1, // 创建游戏
  START_GAME = 2,
  JOIN_GAME = 3, // 加入游戏
  CLOSE_GAME = 5, // 主动关闭游戏
  GAME_INFO = 6, //游戏信息
  BROADCAST = 7, // 广播/推送数据
  SYNCHRONIZATION = 8, // 同步本场游戏信息
  OPERATION = 9, // 游戏操作
  LEAVE = 10,
  OTHER = 11,
  RE_JOIN = 12,
  HEARTCHECK = 120, // 心跳维持
  BIND_ROOM = 200,//绑定房间
  UNBIND_ROOM = 201,//解绑房间
}

export enum GAME_STAGE {
  INIT = 1, //暂不适用
  JOIN = 2, // 等待玩家加入
  GAMEING = 3, // 游戏中
  END = 4, // 游戏结束
}

export enum ACTIONS {
  GAME_INFO = 'gameInfo',
  START_GAME = 'startGame',
  JOIN_GAME = 'joinGame',
  LEAVE_GAME = 'leaveGame',
  ROLL_DICE = 'rollDice', // 请求 摇骰子
  RESET_DICE = 'resetDiceNum', // 重置骰子
  CHESS_MOVE_SELECT = 'chessMoveSelect', // 请求 选择棋子
  FINISH_DICE_MOVING = 'finishDiceMoving',
  FINISH_MOVE = 'finishMove', // 请求 棋子移动结束
  NOTIFY_PLAYER_DICE = 'notifyPlayerDice', // 推送 提醒玩家游戏 notifyplayerdice
  NOTIFY_DICE_RESULT = 'notifyDiceResult', // 推送 骰子结果
  NOTIFY_MOVE_SELECT = 'notifyMoveSelect', // 推送 玩家选择移动的棋子
  NOTIFY_CHESS_MOVE = 'notifyChessMove', // 推送 骰子移动
  NOTIFY_PLAYERSTATUS_UPDATE = 'notifyPlayerStatusUpdate', // 托管模式变更
  NOTIFY_RESET_INFO = 'notifyReset', // 重置信息变更
  NOTIFY_RESET_COUNTDOWN = 'notifyResetCountDown',// 推送棋子重置倒计时事件，只推送给自己
  NOTIFY_BUST_PLAYER_RESULT = 'notifyBustPlayerResult', // 玩家破产
}

// 1: 1vs1模式、2:4team模式、3：自由模式 5:2V2杠杆模式 6 经典模式
export enum GAME_MODE {
  ONE_VS_ONE = 1, // 1vs1模式
  FOUR_TEAM = 2, // 4team模式
  FREE = 3, // 自由模式
  LEVER = 4, // 杠杆模式
  TWO_VS_TWO = 5, // 2V2杠杆模式
  TWO_BATTLE = 6, // 2人对战
  FOUR_BATTLE = 7, // 4人对战
}

export enum END_MODE {
  CLASSIC = 1, // 经典模式
  QUICK = 2, // 快速模式
  MASTER = 3, // 大师模式
  TWO_VS_TWO = 4 // 2v2快速模式
}