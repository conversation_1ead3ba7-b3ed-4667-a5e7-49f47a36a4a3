import { assetManager, resources } from "cc";
import { cs } from "./log";
import { ResUrl } from "./resUrl";

export class CachePrefabs {
    private static _ins: CachePrefabs = null;
    public static get instance() {
        if (!this._ins) this._ins = new CachePrefabs();
        return this._ins;
    }

    private prefabsUrlList: string[] = [];
    public prefabsCacheMap = new Map();

    /**
     * 获取需要加载的资源
     * @returns 
     */
    private getResList() {
        this.prefabsUrlList = [];
        for (const key in ResUrl.allResInfo) {
            if (Object.prototype.hasOwnProperty.call(ResUrl.allResInfo, key)) {
                const element = ResUrl.allResInfo[key];
                if (element?.["isLoad"]) {
                    this.prefabsUrlList.push(element?.["url"]);
                }
            }
        }
        return this.prefabsUrlList;
    }


    /**
     * 加载全部预制体
     * @returns 
     */
    public loadAllPrefabs() {
        return new Promise((resolve, reject) => {
            const prefabsList = this.getResList();
            const len = prefabsList.length;
            let num = 0;
            prefabsList.map((item) => {
                resources.load(item, (err, res) => {
                    num++;
                    if (err) cs.log(err);
                    const name = res?.["data"]?.["_name"];
                    const uuid = res?.["_uuid"];
                    this.prefabsCacheMap.set(item, res)
                    this.changeUuidByName(name, uuid);
                    if (num == len) resolve("");
                })
            })
        })
    }

    /**
     * 通过名字改变uuid
     * @param name 
     * @param uuid 
     */
    private changeUuidByName(name: string, uuid: string) {
        for (const key in ResUrl.allResInfo) {
            if (Object.prototype.hasOwnProperty.call(ResUrl.allResInfo, key)) {
                const element = ResUrl.allResInfo[key];
                if (element?.["name"] == name) {
                    ResUrl.allResInfo[key]["uuid"] = uuid;
                }
            }
        }
    }


    /**
     * 销毁
     * 针对于内存管理
     * TODO
     */
    public destroy() { }

}