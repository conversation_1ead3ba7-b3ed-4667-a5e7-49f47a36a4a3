import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tween, Twe<PERSON>, v3, Vec3, _decorator, is<PERSON><PERSON><PERSON>, Sprite, Label, view, director, debug } from "cc";
import { BasePanel } from "../../framework/basePanel";
import { EventMgr } from "../../framework/eventMgr";
import { EventName } from "../../framework/eventName";
import { Utils } from "../../framework/frameworkUtils";
import { I18n } from "../../framework/i18n";
import { Lab } from "../../framework/lab";
import globalData from "../../globalData";
import ViewManager from "../../manager/view";
import { GAME_MODE } from "../../network/constants";
import { sealClick } from "../../seal/SealBridge";
import { EventType } from "../../type/events";
import { commonUtil } from "../../utils/commonUtil";
import { sensorClick } from "../../utils/sensor";
import { loadRemoteImg, throttleFunc } from "../../utils/utils";
import { TopAreaMgr } from "../../manager/topAreaMgr";
import { cs } from "../../framework/log";
import { gameLocalServer } from "../../manager/gameLocalServer";

const { ccclass, property } = _decorator;

@ccclass('TopArea_Classic')
export default class TopArea_Classic extends BasePanel {

    private _onClickRule = throttleFunc(this.showRules.bind(this), 1000)    

    init(data: any): void {
        super.init(data)
    }


    onLoad() {
        super.onLoad()
    }

    /**
     * 添加消息事件
     */
    onMessageEvent() {
        super.onMessageEvent()
        // 绑定事件
        this.addEvent()
        // 监听数据更新，改变顶部栏区域数据
        EventMgr.on(EventName.UPDATE_TOP_AREA_DATA, this, this.updateDataShow)
        // 屏幕适配
        director.on(EventType.CHANGE_VIEW, this.updateWidget, this)
    }

    /**
     * 移除消息事件
     */
    offMessageEvent() {
        super.offMessageEvent()
        this.removeEvent()
        EventMgr.off(EventName.UPDATE_TOP_AREA_DATA, this, this.updateDataShow)
        director.off(EventType.CHANGE_VIEW, this.updateWidget, this)
    }

    /**
     * 事件绑定
     *  */
    addEvent() {
        // 说明按钮
        if (this.view['illustrate']) this.view['illustrate'].on('click', this.showPromptBox, this)
        // 退出
        if (this.view['Exit']) this.view['Exit'].on('click', this.handleExit, this)
        // 规则
        if (this.view['Rules']) this.view['Rules'].on('click', this._onClickRule, this)
        // 遮罩层
        if (this.view['classicTopAreaMask']) this.view['classicTopAreaMask'].on('click', this.hideClassicTopAreaMask, this)
        EventMgr.on(EventName.EVENT_RENDER_BASE_COMPONENT, this, this.updateAllInfo)
    }

    /*
    *  隐藏遮罩层
    */
    hideClassicTopAreaMask() {
        let target = this.view['promptBox']
        if (!target.active) return
        let userId = [ViewManager.playerSortPosIndexs[I18n.languageState === 2 ? 3 : 0]].map((i: number) => `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`)[0]
        // 隐藏对话框
        this.tools.visible('promptBox', false)
        // 隐藏遮罩层
        this.view['classicTopAreaMask'].active = false
        this.handleClickSetup()
    }

    /**
    *    移除事件
    *  */
    removeEvent() {
        // 说明按钮
        if (this.view['illustrate']) this.view['illustrate'].off('click', this.showPromptBox, this)
        // 退出
        if (this.view['Exit']) this.view['Exit'].off('click', this.handleExit, this)
        // 规则
        if (this.view['Rules']) this.view['Rules'].off('click', this._onClickRule, this)
        // 遮罩层
        if (this.view['classicTopAreaMask']) this.view['classicTopAreaMask'].off('click', this.hideClassicTopAreaMask, this)

    }


    /** 
     * 点击离开游戏
     * */
    handleExit() {
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '离开游戏',
        })
        console.log('金币数目', globalData.gameInfo?.modeData?.coinThreshold, gameLocalServer.instance.get1stUserIndex(), globalData.getUserInfoByUserId(globalData.myUserId)?.userIndex === gameLocalServer.instance.get1stUserIndex() ? 1 : 2)
        sealClick({
            notifyName: 'leaveClick',
            data: {
                userId: globalData.myUserId, // 用户ID
                coinThreshold: globalData.gameInfo?.modeData?.coinThreshold, // 扣减金币额
                antes: 100, // 基数
                maxEatChess: 0, // 最大吃棋子数
                rank: globalData.getUserInfoByUserId(globalData.myUserId)?.userIndex === gameLocalServer.instance.get1stUserIndex() ? 1 : 2
            }
        }, () => { })
    }

    /** 
    *  点击规则弹窗
     */
    showRules() {
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '规则入口',
        })

        sealClick({
            notifyName: 'ruleClick',
            data: {}
        }, () => { })
    }

    protected onEnable(): void {
        super.onEnable();
        this.updateAllInfo();
        this.updateWidget()
    }

    /** 
     * 更新信息
    */
    updateAllInfo() {
        if (!isValid(this.node, true)) return
        this.updateDataShow()
    }

    /**
     *  展示顶部栏信息
    */
    private async updateDataShow() {
        // 排名数据
        let firstPlaceData = TopAreaMgr.instance.getFirstPlace()
        let secondPlaceData = TopAreaMgr.instance.getSecondPlace()
        // 模式变化展示
        let gameMode = TopAreaMgr.instance.getGameMode()
        if (gameMode === GAME_MODE.TWO_BATTLE) {
            this.tools?.text('firstCoinNum', firstPlaceData?.reward || 0)
            this.tools?.visible('runnerUp', false)
        } else if (gameMode === GAME_MODE.FOUR_BATTLE) {
            this.tools?.visible('runnerUp', true)
            this.tools?.text('firstCoinNum', firstPlaceData?.reward || 0)
            this.tools?.text('secondCoinNum', secondPlaceData?.reward || 0)
        }

    }

    onDisable() {
        super.onDisable();
    }



    /**
*  显示说明提示框
*/
    showPromptBox() {
        let target = this.view['promptBox']
        let classicTopAreaMask = this.view['classicTopAreaMask']
        if (target.active) {
            this.tools.visible('promptBox', false)
            classicTopAreaMask.active = false
        } else {
            this.tools.visible('promptBox', true)
            classicTopAreaMask.active = true
        }
        let userId = [ViewManager.playerSortPosIndexs[0]].map((i: number) => `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`)[0]
        //  通知客户端
        this.handleClickSetup()
        //  埋点
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '设置',
            page_business_type: globalData.getUserInfoByUserId(globalData.myUserId)?.status
        })
    }


    /**
     * 屏幕适配
     */
    updateWidget() {
        const { height } = view.getVisibleSize()
        cs.log('屏幕适配 view.getVisibleSize = ', height)
        cs.log('屏幕适配 topPartHeight', globalData?.roomInfo?.topPartHeight)
        if (this.node?.isValid && height > 0 && height < 2000 && globalData?.roomInfo?.topPartHeight > 0) {
            commonUtil.setPosition(this.node, v3(0, height / 2 - (globalData?.roomInfo?.topPartHeight ?? 0), 0))
        }
        // console.error('updateWidget topArea', JSON.stringify(this.node.getPosition()), globalData?.roomInfo?.topPartHeight)
    }

    start() {

    }


    update(deltaTime: number) {

    }

    /**
     * 处理点击setupClick功能
     */
    handleClickSetup() {
        const userId = [ViewManager.playerSortPosIndexs[0], ViewManager.playerSortPosIndexs[3]].map(
            (i: number) => {
                // console.error("=====", globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userIndex)
                return `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`
            }
        )[I18n.languageState === 2 ? 1 : 0]
        console.error("====", userId)
        sealClick({
            notifyName: 'setupClick',
            data: {
                userId, // 右上角用户ID
                isExpand: this.view['promptBox']?.active // 是否展开设置面板
            }
        }, () => { })
    }
}

