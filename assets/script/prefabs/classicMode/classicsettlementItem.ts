import { color, Label, Sprite, SpriteFrame, _decorator } from "cc";
import { BasePanel } from "../../framework/basePanel";
import { I18n } from "../../framework/i18n";
import { Lab } from "../../framework/lab";
import { cs } from "../../framework/log";
import { IResultCoinInfo } from "../../manager/gameOverMgr";
import { commonUtil } from "../../utils/commonUtil";
import { loadRemoteImg } from "../../utils/utils";

const { ccclass, property } = _decorator;


/**
 * 节点说明
 * winItemOtherBg 针对于胜利的第一个展示情况
 * winItemOtherBg1 针对于失败的item展示
 * bankruptcyArea1 破产展示内容，有吃棋等操作
 * bankruptcyArea2 破产金币为0的tips
 */
@ccclass("ClassicSettlementItem")
export default class ClassicsettlementItem extends BasePanel {
    private curNodeState: boolean = false;
    // idx 
    private idx: number = -1;
    //data IResultCoinInfo
    private itemData: IResultCoinInfo = null;
    // 0 win 1 fail 2 bankruptcy  3 新手引导
    private openState: number = 0;
    // 排行版长度
    private rankLen: number = 0
    // 当前item状态 赢 和  输
    private itemState: boolean = false;

    /**
      * itemState 节点状态 boolean 
      * idx 下标 win fail  无论输赢都是 0 展示赢
      * data 数据
      * openState 打开gameover的状态  暂时无用
      */
    init(data: any): void {
        super.init(data);
        cs.log('settlement:', data, this.myData?.rankLen);
        this.idx = this.myData?.idx;
        this.itemData = this.myData?.data;
        this.openState = this.myData?.openState;
        this.itemState = this.myData?.itemState;
        this.rankLen = this.myData?.rankLen;
        this.updateUi();
    }

    async updateUi() {
        cs.log("游戏结算数据====", this.itemData?.isEscape, this.itemData?.isEscape === 1)
        let isWin = this.itemData?.isWinner == 2
        const isEscape = this.itemData?.isEscape == 1 ? true : false
        // 如果是破产，展示false
        if (this.openState == 2) isWin = false;
        commonUtil.setHeight(this.view["area"], isWin ? 144 : 120);
        // this.view["area"].height = isWin == true ? 144 : 120;
        this.tools.visible("winBg", isWin);
        this.tools.visible("failBg", !isWin);
        this.tools.visible("right", isWin);
        commonUtil.setColor(this.view["name"], isWin ? color(156, 89, 28, 255) : color(255, 255, 255, 255));
        // this.view["name"].color = isWin == true ? color(156, 89, 28, 255) : color(255, 255, 255, 255);
        // 这是做什么处理？
        // 头像边框
        if (this.view["rank"]) {
            const rankSp = this.view["rank"].getComponent(Sprite)
            let rankIndex = this.idx + 1
            // 表达啥？
            if (this.idx >= 1) rankIndex++
            if (this.idx === 1 && this.rankLen === 4) rankIndex = 2
            rankSp.spriteFrame = rankSp.spriteAtlas.getSpriteFrame(`${rankIndex}`)
        }
        // 图片地址？
        const portrait = this.itemData?.portrait ?? "";
        loadRemoteImg(portrait).then((portraitSp: SpriteFrame) => {
            if (this.view["icon"] && portraitSp) commonUtil.setSpriteFrameAndAdapteHeight(this.view["icon"].getComponent(Sprite), portraitSp);
        })
        const portraitMask = this.itemData?.portraitMask ?? "";
        loadRemoteImg(portraitMask).then((portraitMaskSp: SpriteFrame) => {
            if (this.view["avatorMask"] && portraitMaskSp) this.view["avatorMask"].getComponent(Sprite).spriteFrame = portraitMaskSp;
        })
        // 昵称和赢输
        let name = this.itemData?.name ?? "";
        let actuallyTotalBalance: number | string = this.itemData?.actuallyTotalBalance ?? 0;
        actuallyTotalBalance = actuallyTotalBalance >= 0 ? (Lab.plus + actuallyTotalBalance) : actuallyTotalBalance;
        if (I18n.languageState == 2) {
            name = name + Lab.space + Lab.space;
        }
        // this.tools.text("name", name)
        this.tools.text("score", actuallyTotalBalance);
        this.tools.visible("escape", isEscape);
        this.tools.visible("diamond", Number(actuallyTotalBalance) > 0);
        this.tools.visible("score", Number(actuallyTotalBalance) > 0);
        commonUtil.setLabelEllipsis(this.view['name']?.getComponent(Label), name, 10, I18n.languageState === 1)
    }

}
