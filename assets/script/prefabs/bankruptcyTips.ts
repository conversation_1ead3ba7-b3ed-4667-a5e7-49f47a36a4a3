import { _decorator } from "cc";
import SuperPanel from "../framework/superPanel";

const { ccclass, property } = _decorator;

@ccclass("BankruptcyTips")
export default class BankruptcyTips extends SuperPanel {
    protected reserve(): void {
        this.isAddBlockInputEvent = true;
        this.panelAnim.anim = {
            openIsAnim: true,
            openAnimType: 12,
            closeIsAnim: false,
            closeAnimType: -1
        }
    }

    init(data: any): void {
        super.init(data);
    }

    protected onMessageEvent(): void {
        super.onMessageEvent();
        if (this.view["okBtn"]) this.view["okBtn"].on("click", this.clickOkBtn, this);
    }

    protected offMessageEvent(): void {
        super.offMessageEvent();
        if (this.view["okBtn"]) this.view["okBtn"].off("click", this.clickOkBtn, this);
    }

    private clickOkBtn() {
        this.clickClosePage();
    }
}
