/**
 * @content panel 工具
 * <AUTHOR>
 */
import { _decorator, sys, Node, Label, RichText, SpriteFrame, Sprite, Texture2D, Vec3, UITransform, UIOpacity } from "cc";
import { Lab } from "./lab";
import { LoadMgr } from "./loadMgr";
import { cs } from "./log";

export class PanelTools {
    constructor(private view: any, private adaptImgObj: object) { }

    destroy() { }

    /**
     * 节点显示隐藏
     * @param {string} nodeName  节点名字
     * @param {boolean} bool 是否显示
     */
    public visible(nodeName: string, bool: boolean) {
        if (this.view?.[nodeName]?.isValid) this.view[nodeName].active = bool;
    }

    /**
      * 设置透明度
      * @param {string} nodeName  节点名字
      * @param {boolean} value 透明度数值
      */
    public opacity(nodeName: string, value: number) {
        if (this.view?.[nodeName]) {
            if (!this.view[nodeName].getComponent(UIOpacity)) {
                this.view[nodeName].addComponent(UIOpacity);
            }
            this.view[nodeName].getComponent(UIOpacity).opacity = value;
        }
    }

    /**
     * 设置缩放
     * @param {string} nodeName  节点名字
     * @param {boolean} value 透明度数值
     */
    public scale(nodeName: string, value: number) {
        if (this.view?.[nodeName]?.isValid) this.view[nodeName].scale = value;
    }

    /**
     * 文字
     * @param {string} nodeName 节点名字
     * @param {string | number} str 文字
     */
    public text(nodeName: string, str: string | number) {
        if (this.view?.[nodeName]) {
            if (this.view[nodeName].getComponent(Label)) {
                this.view[nodeName].getComponent(Label).string = str + Lab.str;
            } else if (this.view[nodeName].getComponent(RichText)) {
                this.view[nodeName].getComponent(RichText).string = str + Lab.str;
            }
        }
    }

    /**
     * 网络加载图片
     * @param {string} name 节点名字
     * @param {string} url 图片url
     * @param {string} ext 额外参数，处理 例如：微信头像返回的img没有.png 情况等等
     * @param {Function} cbFunc 回调 TODO 后续会把这个回调干掉
     * @returns 
     */
    public loadImage(name: string, url: string, ext?: string, cbFunc?: Function) {
        if (!name || !url) {
            cs.log("loadImage name or url not found!");
            return;
        }
        LoadMgr.instance.loadImg(url, ext).then((sp: SpriteFrame) => {
            const node = this.view?.[name];
            if (node) node.getComponent(Sprite).spriteFrame = sp;
            cbFunc && cbFunc();
        }, (err) => { }).catch((err) => { cs.log(err) })
    }

    /**
     * 从本地加载图片
     * @param {string} name 节点名字
     * @param {string} url 图片url
     * @param {Function} cbFunc 回调
     * @returns 
     */
    public loadImageFromRes(name: string, url: string, cbFunc?: Function) {
        if (!name || !url) {
            cs.log("loadImage name or url not found!");
            return;
        }
        LoadMgr.instance.loadImgFromRes(url).then((sp: SpriteFrame) => {
            const node = this.view?.[name];
            if (node) node.getComponent(Sprite).spriteFrame = sp;
            cbFunc && cbFunc();
        })
    }

    /**
     * 加载远程图片并适配大小
     * @param {string} url 图片url
     * @param {string} name 需要填充的位置名字
     */
    public loadImgAndAdapt(url: string, name: string) {
        if (!name || !url) {
            cs.log("loadImgAndAdapt name or url not found!");
            return;
        }
        LoadMgr.instance.loadNetImg(url).then((imageAsset: Texture2D) => {
            this.adaptImg(name, imageAsset);
        })
    }

    /**
     * 加载本地图片并适配大小
     * @param {string} url 图片url
     * @param {string} name 需要填充的位置名字
     */
    public loadImgResAndAdapt(url: string, name: string) {
        if (!name || !url) {
            cs.log("loadImgResAndAdapt name or url not found!");
            return;
        }
        if (!this.view[name]) {
            cs.log("loadImgResAndAdapt ,check name is right?");
            return
        }
        LoadMgr.instance.loadImgFromRes(url).then((sp: SpriteFrame) => {
            const exist = this.adaptImgObj.hasOwnProperty(name);
            const node = this.view[name];
            let uiH = exist ? this.adaptImgObj[name] : this.getUITransform(node)?.height;
            const height = sp.getRect().height;
            const rote = Number((uiH / height).toFixed(3));
            this.adaptImgObj[name] = uiH;
            const sprite = node.getComponent(Sprite);
            sprite.sizeMode = 1;
            sprite.type = 0;
            node.setScale(new Vec3(rote, rote, 0));
            sprite.spriteFrame = sp;
        })
    }

    /**
     * 适配图片
     * @param name 名字
     * @param imageAsset 资源 
     */
    public adaptImg(name: string, imageAsset: Texture2D) {
        const exist = this.adaptImgObj.hasOwnProperty(name);
        const node = this.view[name];
        const h = this.getUITransform(node)?.height;
        const w = this.getUITransform(node)?.width;
        let uiH = exist ? this.adaptImgObj[name] : (h >= w ? w : h);
        const height = imageAsset.height >= imageAsset.width ? imageAsset.width : imageAsset.height;
        const rote = Number((uiH / height).toFixed(3));
        const spriteFrame = new SpriteFrame();;
        this.adaptImgObj[name] = uiH;
        spriteFrame.texture = imageAsset
        const sprite = node.getComponent(Sprite);
        sprite.sizeMode = 1;
        sprite.type = 0;
        node.setScale(new Vec3(rote, rote, 0));
        sprite.spriteFrame = spriteFrame;
    }

    private getUITransform(node: Node) {
        if (!node) return null;
        if (!node?.getComponent(UITransform)) node.addComponent(UITransform);
        return node.getComponent(UITransform);
    }

}