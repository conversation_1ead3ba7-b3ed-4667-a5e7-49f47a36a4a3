import { _decorator, Component, Node, instantiate, Vec3, Prefab, UITransform } from 'cc';
import { BasePanel } from '../framework/basePanel';
import { Utils } from '../framework/frameworkUtils';
import { largeScaleRate } from '../gameConfig';
import ViewManager from '../manager/view';
import view from '../manager/view';
import { DialogType, FacialType } from '../type';
import { commonUtil } from '../utils/commonUtil';
const { ccclass, property } = _decorator;

interface IPrefType {
    pref: any, useState: boolean, id: number
}
@ccclass('faceAnimNode')
export class faceAnimNode extends Component {
    @property(Prefab)
    faceNode: Prefab = null!;

    private pool: Array<IPrefType> = [];
    private prefId: number = 0;

    onLoad() {
        ViewManager.faceAimScript = this;
    }

    playAnim(type: FacialType, cb: Function, worldPos: Vec3) {
        const { pref, id } = this.getPrefByPool();
        pref.active = true;
        pref.setScale(Vec3.ONE);
        const uit = this.node.getComponent(UITransform);
        const curPos = uit.convertToNodeSpaceAR(worldPos);
        curPos.y = curPos.y + 60;
        pref.setPosition(curPos);
        pref.children.forEach((node) => (commonUtil.setActive(node, false)))
        switch (type) {
            case FacialType.Happy: {
                commonUtil.setActive(pref.getChildByName(FacialType.Happy), true)
                break
            }
            case FacialType.Sad: {
                commonUtil.setActive(pref.getChildByName(FacialType.Sad), true)
                break
            }
        }
        if ((pref.scale.x == 1 && pref.scale.y == 1)) {
            pref.setScale(commonUtil.getEqualVec3(largeScaleRate))
        }
        Utils.timeOut(this, 0.1).then(() => {
            cb && cb(() => {
                commonUtil.setActive(pref, false);
                this.setStateById(id);
            });
        })
    }

    private setStateById(id: number) {
        for (let i = 0; i < this.pool.length; i++) {
            if (this.pool[i].id == id) {
                this.pool[i].useState = false;
                return;
            }
        }
    }

    private getPrefByPool() {
        if (this.pool.length <= 0) {
            return this.createAndPush();
        }
        for (let i = 0; i < this.pool.length; i++) {
            if (this.pool[i]?.useState == false) {
                this.pool[i].useState = true;
                return { pref: this.pool[i].pref, id: this.pool[i].id };
            }
        }
        return this.createAndPush();
    }

    private createAndPush() {
        const pref = this.createItem();
        this.prefId++;
        const obj: IPrefType = { pref: pref, useState: true, id: this.prefId }
        this.pool.push(obj);
        return { pref, id: this.prefId };
    }

    private createItem() {
        const pref = instantiate(this.faceNode);
        this.node.addChild(pref);
        return pref;
    }
}

