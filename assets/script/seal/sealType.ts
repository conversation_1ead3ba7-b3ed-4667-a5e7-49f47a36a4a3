
export type TCallback = (data: any, __callback_id?: string) => void;
export type TNativeMethod =
  | 'getGameConfig'
  | 'login'
  | 'getUserInfo'
  | 'getSystemInfo'
  | 'getGameRoomInfo'
  | 'notifyGameStatus'
  | 'notifyPlayerStatus'
  | 'notifyClickEvent'
  | 'setSpeakerMode'
  | 'setMicMode'
  | 'notifyResetDiceResult'
  | 'log'
  | 'notifyGiftStatus'
  | 'notifyUpdateGameSkin'
  | 'notifyGamePreloaded'
  | 'notifyLeaveGame'
  | 'notifyBustStatus'
  | 'replayClick'
  | 'getStoreLocalData'
  | 'getClientAdaptInfo'
  | 'getEffectSoundState'
  | 'traceReport'
  | 'rdsTraceReport'
  | 'playSoundEffect'
  | 'notifyGameGuideFinish'
  | 'gameHall_leaveGame'
  | 'notifyGamehallLoaded'
  | 'notifyDisplayPlayerIcon'
  | 'getGameExtraConfig'
  | 'getGameSubType'
  | 'getGameConfigId'
  | 'notifyGameViewState'


export enum sealCallEvents {
  setGameMode = 'setGameMode',
  startGame = 'startGame',
  joinGame = 'joinGame',
  cancelJoinGame = 'cancelJoinGame',
  customEvent = 'customEvent',
  onCustomEvent = 'onCustomEvent',
  setEffect = 'setEffect',
  setSpeak = 'setSpeak',
  setMicStatus = 'setMicStatus',
  getSeatRect = 'getSeatRect',
  setSeatGiftIcon = 'setSeatGiftIcon',
  notifyGitfStatus = 'notifyGitfStatus',
  preload = 'preload',
  closeWebsocket = 'closeWebsocket',
  setGameRoomInfo = 'setGameRoomInfo',
  toggleAvatorBorderVisiable = 'toggleAvatorBorderVisiable',
  gameHall_changeGame = 'gameHall_changeGame',
  gameHall_leaveGame = 'gameHall_leaveGame',
  getUserCoinInfo = 'getUserCoinInfo'
}



// 音效状态
export interface ISoundEffectStatus {
  success: 'success' | 'failed',   //"success"表示成功，"failed"表示失败
  music_switch_on: boolean,        // 表示背景音乐是开启的，false 表示关闭
  sound_effect_switch_on: boolean  // 表示音效是开启的，false 表示关闭
}


export const SEAL_BRIDGE_NAME = 'sealJSBridge'
