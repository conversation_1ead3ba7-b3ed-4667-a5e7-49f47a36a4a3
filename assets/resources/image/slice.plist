<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{0,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{1980,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{2200,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{2420,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{2640,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>14.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{2860,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>15.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{3080,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>16.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{3300,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{220,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{440,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{660,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{880,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{1100,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{1320,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{1540,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{220,220}</string>
                <key>spriteSourceSize</key>
                <string>{220,220}</string>
                <key>textureRect</key>
                <string>{{1760,0},{220,220}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>slice.png</string>
            <key>size</key>
            <string>{3520,220}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:c6f0e69710b466cc32af21ca103b74e7:660767b0d74343f2329bf60103b4ef98:96fb3d46aab20ec9eb9704de1ab972b6$</string>
            <key>textureFileName</key>
            <string>slice.png</string>
        </dict>
    </dict>
</plist>
