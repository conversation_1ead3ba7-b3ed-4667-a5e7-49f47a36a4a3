import { color, Color, EventHandler, instantiate, Label, Layout, Node, Prefab, RichText, screen, size, Sprite, SpriteFrame, sys, tween, UIOpacity, UIRenderer, UITransform, v2, v3, Vec2, Vec3, view } from "cc";

export class commonUtil {
    public static addClickEvent(target, node, jsName, cbName, edata = null) {
        var checkEventHandler = new EventHandler();
        checkEventHandler.target = node; //这个 node 节点是你的事件处理代码组件所属的节点
        checkEventHandler.component = jsName;
        checkEventHandler.handler = cbName;
        if (edata)
            checkEventHandler.customEventData = edata;
        target.clickEvents.push(checkEventHandler);
    }

    /**
     * 补0。
     * @param time 
     * @returns 
     */
    public static timeSupplementZero(time) {
        return (time >= 0 && time < 10) ? `0${time}` : time;
    }

    public static judgeRandom(percent, max = 100) {
        //判断一个随机值是否成功
        if (percent == 0) return false;
        if (percent == max) return true;
        let r = this.getRandom(0, max)
        if (r <= percent) {
            return true;
        }
        return false;
    }

    /**
     * 范围取值。
     * @param num 
     * @param min 
     * @param max 
     * @returns 
     */
    public static limitNumber(num: number, min: number, max: number): number {
        return Math.min(max, Math.max(min, num));
    }

    /**
     * 范围随机数。
     * @param min 
     * @param max 
     * @returns 
     */
    public static getRandom(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min) + min);
    }

    /**
     * 范围随机数。
     * @param min 
     * @param max 
     * @param decimals 小数位数，默认为0，表示不限制位数，否则应该为大于0的数
     * @returns 
     */
    public static getRandomF(min: number, max: number, decimals = 0): number {
        let ret = Math.random() * (max - min) + min;
        if (decimals > 0) {
            ret = parseFloat(ret.toFixed(decimals));
        }
        return ret;
    }

    public static getEqualVec3(scaleRatio: number) {
        return v3(scaleRatio, scaleRatio, scaleRatio)
    }

    /**
     * 数组随机值。
     * @param arr 
     * @returns 
     */
    public static getRandomValueInArr<T>(arr: T[]): T {
        return arr[this.getRandom(0, arr.length)];
    }

    /**
     * 数组排序。
     * @param arr 
     */
    public static sortArrInRandom<T>(arr: T[]) {
        let i = arr.length;

        while (i) {
            let j = Math.floor(Math.random() * i--);

            [arr[j], arr[i]] = [arr[i], arr[j]];
        }
    }

    /**
     * 判断概率。
     * @param target 
     * @param percent 
     * @returns 
     */
    public static judgePercent(target, percent = 100) {
        return target == 0 ? false : target == percent ? true : this.getRandom(0, percent) < target ? true : false;
    }

    /**
     * 是否在数组中。
     * @param val 
     * @param arr 
     * @returns 
     */
    public static isInArr(val, arr) {
        if (typeof arr != 'object') {
            return false;
        }

        return arr.indexOf(val) > -1;
    }

    /**
     * 通过值来获取对应的key，要获取的key必须是可枚举属性
     * @param obj 对象
     * @param value 值
     */
    public static getKeyByValue(obj, value) {
        if (obj instanceof Object) {
            return Object.keys(obj)[Object.values(obj).indexOf(value)];
        }
    }

    /**
     * 数组去重
     * @param a 
     * @returns 
     */
    public static distinct(a) {
        return Array.from(new Set(a));
    }

    /**
     * 复制Obj
     * @param obj 
     * @returns 
     */
    public static copyObj(obj) {
        return JSON.parse(JSON.stringify(obj));
    }

    /**
     * Obj随机取值。
     * @param obj 
     * @returns 
     */
    public static randObjKey(obj) {
        let keys = Object.keys(obj);

        return keys[this.getRandom(0, keys.length)];
    }

    /**
     * 整数分成整数份。
     * @param all 
     * @param parts 
     * @returns 
     */
    public static spiltIntNumber(all, parts) {
        let ret = [];
        let sum = 0;

        if (parts == 0 || Math.floor(all / parts) == 0) {
            return [all];
        }

        for (let i = 0; i < parts; i++) {
            if (i != parts - 1) {
                let one = Math.floor(all / parts);
                sum += one;
                ret.push(one);
            } else {
                ret.push(all - sum);
            }
        }
        return ret;
    }

    /**
     * 判断点是否在多边形内(射线法)。
     * @param {[Vec2]} polygonPoints 
     * @param {Vec2} p 
     * @returns {Boolean} 
     */
    public static checkPointInPolygon(polygonPoints, p) {
        let interectionPoint = 0;

        for (let i = 0, len = polygonPoints.length; i < len; i++) {
            const p1 = polygonPoints[i];
            const p2 = i + 1 >= len ? polygonPoints[0] : polygonPoints[i + 1];

            if ((p.y >= p1.y && p.y <= p2.y) || (p.y >= p2.y && p.y <= p1.y)) {
                const t = (p.y - p1.y) / (p2.y - p1.y);
                const xt = p1.x + t * (p2.x - p1.x);

                if (p.x == xt) {
                    return true;
                }

                if (p.x > xt) {
                    interectionPoint++;
                }
            }
        }

        return interectionPoint % 2 == 0 ? false : true;
    }

    /**
     * 强制保留2位小数
     * @param x 
     */
    public static changeTwoDecimalF(x) {
        var f_x = parseFloat(x);
        if (isNaN(f_x)) {
            return 0;
        }
        var f_x = Math.floor(x * 100) / 100;
        var s_x = f_x.toString();
        var pos_decimal = s_x.indexOf('.');
        if (pos_decimal < 0) {
            pos_decimal = s_x.length;
            s_x += '.';
        }
        while (s_x.length <= pos_decimal + 2) {
            s_x += '0';
        }
        return s_x;
    }

    /**
     * 延迟指定时间
     * @param timeInSec 延迟秒数
     */
    public static delay(timeInSec: number): Promise<void> {
        return new Promise((resolve, reject) => {
            setTimeout(resolve, timeInSec * 1000);
        });
    }

    /**
     * 检测手机。
     * @param phone
     * @returns 
     */
    public static checkPhone(phone) {
        if (!(/^1[3456789]\d{9}$/.test(phone))) {
            return false;
        }

        return true;
    }

    /**
     * 检测是否是iphoneX及更高
     * @param model 
     * @returns 
     */
    public static checkIphoneX(model) {
        if (!model) {
            return false;
        }

        model = String(model);

        if (model.indexOf('iPhoneX') || model.indexOf('unknown')) {
            return true;
        }

        for (let i = 11; i <= 14; i++) {
            if (model.indexOf(`iPhone ${i}`) < 0) {
                continue;
            }

            return true;
        }

        return false;

    }

    public static getRequestId() {
        return this.randomRange(8);
    }

    /** 随机生成固定位数或者一定范围内的字符串数字组合
    * @param {Number} min 范围最小值
    * @param {Number} max 范围最大值，当不传递时表示生成指定位数的组合
    * @param {String} charStr指定的字符串中生成组合
    * @returns {String} 返回字符串结果
    **/
    public static randomRange(min, max?, charStr?) {
        var returnStr = "", //返回的字符串
            range; //生成的字符串长度

        //随机生成字符
        var autoGetStr = function () {
            var charFun = function () {
                var n = Math.floor(Math.random() * 35);
                if (n < 10) {
                    return n; //1-10
                }
                else if (n < 36) {
                    return String.fromCharCode(n + 55); //A-Z
                }
                // else {
                //     return String.fromCharCode(n + 61); //a-z    
                // }
            }
            while (returnStr.length < range) {
                returnStr += charFun();
            }
        };

        //根据指定的字符串中生成组合
        var accordCharStrGet = function () {
            for (var i = 0; i < range; i++) {
                var index = Math.round(Math.random() * (charStr.length - 1));
                returnStr += charStr.substring(index, index + 1);
            }
        };

        if (typeof min == 'undefined') {
            min = 8;
        }
        if (typeof max == 'string') {
            charStr = max;
        }
        range = ((max && typeof max == 'number') ? Math.round(Math.random() * (max - min)) + min : min);

        if (charStr) {
            accordCharStrGet();
        } else {
            autoGetStr();
        }

        return returnStr;
    }

    public static isNative() {
        return sys.platform == sys.Platform.ANDROID;
    }

    public static bezierTo(target: any, duration: number, c1: Vec3, c2: Vec3, to: Vec3, callBack, opts?: any) {
        opts = opts || Object.create(null);
        let twoBezier = (t: number, p1: Vec3, cp: Vec3, p2: Vec3) => {
            let x = (1 - t) * (1 - t) * p1.x + 2 * t * (1 - t) * cp.x + t * t * p2.x;
            let y = (1 - t) * (1 - t) * p1.y + 2 * t * (1 - t) * cp.y + t * t * p2.y;
            return new Vec3(x, y, 0);
        };
        opts.onUpdate = (arg: Vec3, ratio: number) => {
            target.position = twoBezier(ratio, c1, c2, to);
        };
        return tween(target).to(duration, {}, opts).call(() => {
            callBack && callBack();
        }).start();
    }


    public static bezierTo1(target: Node, duration: number, c1: Vec3, c2: Vec3, to: Vec3) {
        const bezierAt = function (a, b, c, d, t) {
            return (Math.pow(1 - t, 3) * a +
                3 * t * (Math.pow(1 - t, 2)) * b +
                3 * Math.pow(t, 2) * (1 - t) * c +
                Math.pow(t, 3) * d);
        }
        return tween(target)
            .to(duration, {}, {
                onUpdate(node: Node, dt: number) {
                    if (!node?.isValid) return
                    const startPos = node.getPosition()
                    let x = bezierAt(startPos.x, c1.x, c2.x, to.x, dt)
                    let y = bezierAt(startPos.y, c1.y, c2.y, to.y, dt)
                    let z = bezierAt(startPos.z, c1.z, c2.z, to.z, dt)
                    target.setPosition(v3(x, y, z))
                }
            })
    }

    //#region Node相关
    /**
     * 获取UITransform
     * @param node 
     * @returns 
     */
    public static getUITransform(node: Node) {
        if (!node.getComponent(UITransform)) {
            node.addComponent(UITransform);
        }

        return node.getComponent(UITransform);
    }

    public static getUIOpacity(node: Node) {
        if (!node.getComponent(UIOpacity)) {
            node.addComponent(UIOpacity);
        }

        return node.getComponent(UIOpacity);
    }

    public static getUIRenderable(node: Node) {
        if (!node.getComponent(UIRenderer)) {
            node.addComponent(UIRenderer);
        }

        return node.getComponent(UIRenderer);
    }

    public static convertToNodeSpaceAR(node: Node, v: Vec3) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).convertToNodeSpaceAR(v);
    }

    public static convertToWorldSpaceAR(node: Node, v: Vec3 = Vec3.ZERO) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).convertToWorldSpaceAR(v);
    }

    public static convertToWorldSpaceARToZero(node: Node) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).convertToWorldSpaceAR(v3(0, 0, 0));
    }

    /**
     * 把一个世界坐标转换为与目标节点所使用的坐标系下的坐标值（以参数节点的父节点为参照）
     * @param {Node} node 
     * @param {Vec2} worldPosition 
     */
    public static worldConvertToNode(node: Node, worldPosition: Vec3): Vec3 {
        return this.convertToNodeSpaceAR(node.parent, worldPosition);
    }

    /**
     * 把一个外地节点的坐标值，转换为与本地节点所使用的坐标系下的坐标值（以本地节点的父节点为参照）
     * @param {Node} localNode 本地节点
     * @param {Node} nonlocalNode 非本地节点
     * @param {Vec2} targetPos 需要转换的坐标(默认使用第二个参数节点的坐标)
     */
    public static fromNodeToNode(localNode: Node, nonlocalNode: Node): Vec3 {
        return this.worldConvertToNode(localNode, this.convertToWorldSpaceARToZero(nonlocalNode));
    }

    public static setScale(node: Node, scaleRate: number) {
        node.isValid && node.setScale(this.getEqualVec3(scaleRate))
    }

    public static getBoundingBox(node: Node) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).getBoundingBox();

    }

    public static getBoundingBoxToWorld(node: Node) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).getBoundingBoxToWorld();

    }

    public static getHeight(node: Node) {
        if (!node) {
            return 0;
        }

        return this.getUITransform(node).height;
    }

    public static setHeight(node: Node, height) {
        if (!node) {
            return 0;
        }

        this.getUITransform(node).height = height;
    }

    public static getWidth(node: Node) {
        if (!node) {
            return 0;
        }

        return this.getUITransform(node).width;
    }

    public static setWidth(node: Node, width) {
        if (!node) {
            return 0;
        }

        this.getUITransform(node).width = width;
    }

    public static setPosition(node: Node, pos: Vec3) {
        if (!node) {
            return 0;
        }

        node.setPosition(pos);
    }

    public static getX(node: Node) {
        if (!node) {
            return 0;
        }

        return node.getPosition().x;
    }

    public static setX(node: Node, x) {
        if (!node) {
            return 0;
        }

        node.setPosition(x, node.position.y);
    }

    public static getY(node: Node) {
        if (!node) {
            return 0;
        }

        return node.getPosition().y;
    }

    public static setY(node: Node, y) {
        if (!node) {
            return 0;
        }

        node.setPosition(node.position.x, y);
    }

    public static getOpacity(node: Node) {
        if (!node) {
            return 0;
        }

        return this.getUIOpacity(node).opacity;
    }

    public static setActive(node: Node, isActive: boolean) {
        node && (node.active = isActive)
    }

    public static setOpacity(node: Node, opacity) {
        if (!node) {
            return 0;
        }

        this.getUIOpacity(node).opacity = Math.max(1, opacity);
    }

    public static setColor(node, color) {
        if (!node) {
            return;
        }

        this.getUIRenderable(node).color = color;
    }

    public static setColorFromHEX(node, hex) {
        if (!node) {
            return;
        }

        const color1: Color = null;
        Color.fromHEX(color1, hex);

        this.setColor(node, color1);
    }

    public static setAngle(node, angle) {
        if (!node) return
        node.angle = angle || 0
    }

    public static getAnchorX(node) {
        if (!node) {
            return 0;
        }

        return this.getUITransform(node).anchorX;
    }

    public static getAnchorY(node) {
        if (!node) {
            return 0;
        }

        return this.getUITransform(node).anchorY;
    }

    public static setAnchor(node, anchorX?: number, anchorY?: number) {
        if (!node) {
            return
        }
        const ui = this.getUITransform(node)
        if (typeof anchorY === 'number') ui.anchorX = anchorX
        if (typeof anchorY === 'number') ui.anchorY = anchorY
    }

    public static setAnchorX(node, anchorX) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).anchorX = anchorX;
    }

    public static setAnchorY(node, anchorY) {
        if (!node) {
            return;
        }

        return this.getUITransform(node).anchorY = anchorY;
    }
    //#endregion

    //#region 系统相关
    private static _designSize = size(750, 1334)
    public static getDesignSize() {
        return this._designSize;
    }

    public static isInDesignSize() {
        let dhw = this._designSize.height / this._designSize.width;
        let vs = this.getVisibleSize();
        let vhw = vs.height / vs.width;
        if (vhw > 2) {
            return false;
        }
        return true;
    }

    public static getVisibleSize() {
        return view.getVisibleSize();
    }

    public static getVisibleHeight() {
        //获取游戏分辨率高度
        return view.getVisibleSize().height;
    }

    public static getVisibleWidth() {
        //获取游戏分辨率宽度
        return view.getVisibleSize().width;
    }

    public static getFrameSize() {
        return screen.windowSize;
    }
    //#endregion

    /**
     * 获取文件大小
     * @param size 
     * @returns 
     */
    public static getfilesize(size) {//把字节转换成正常文件大小
        if (!size) return "";
        var num = 1024.00; //byte
        if (size < num)
            return size + "B";
        if (size < Math.pow(num, 2))
            return (size / num).toFixed(2) + "KB"; //kb
        if (size < Math.pow(num, 3))
            return (size / Math.pow(num, 2)).toFixed(2) + "MB"; //M
        if (size < Math.pow(num, 4))
            return (size / Math.pow(num, 3)).toFixed(2) + "G"; //G
        return (size / Math.pow(num, 4)).toFixed(2) + "T"; //T
    }

    /**
     * 深度拷贝
     * @param o1 
     * @param o2 
     * @returns 
     */
    public static deepClone(o1, o2: any = {}) {
        for (let k in o1) {
            if (typeof o1[k] == "object" && o1[k]?.uuid == null) {
                if (o1[k]?.length != null) {
                    o2[k] = [];
                } else {
                    o2[k] = {};
                }

                this.deepClone(o1[k], o2[k]);
            } else {
                o2[k] = o1[k];
            }
        }
        return o2;
    }

    /**
     * v3转v2
     * @param v3 
     * @returns 
     */
    public static v3tov2(v3: Vec3) {
        return v2(v3.x, v3.y);

    }

    public static numToV3(num) {
        return v3(num, num);
    }

    /**
     * 节点是否在世界包围盒中
     * @param node 
     * @param pos 
     * @returns 
     */
    public static containsWorld(node: Node, pos: Vec3) {
        return this.getBoundingBoxToWorld(node).contains(this.v3tov2(pos));
    }

    /**
     * 坐标是否在包围盒中
     * @param node 
     * @param pos 
     * @returns 
     */
    public static contains(node: Node, pos: Vec3) {
        return this.getBoundingBox(node).contains(this.v3tov2(pos));
    }

    public static distance(v1: Vec2, v2: Vec2) {
        return Vec2.distance(v1, v2);
    }

    /**
     * @zh 设置文本省略 @en set long text ellipsis
     */
    public static setLabelEllipsis(labelComp: Label | RichText, labelStr: string, len: number, isLeftAlign: boolean) {
        if (!labelComp) return
        let str = ''
        const labelArr = labelStr.split(' ')
        for (let i = 0; i < labelArr.length; i++) {
            let isLast = i === labelArr.length - 1
            if ((str + labelArr[i]).length > len) {
                if (!str) str = labelArr[i].substring(0, len)
                labelComp.string = isLeftAlign ? `${str}...` : `...${str}`
                return
            }
            str += `${labelArr[i]}${isLast ? '' : ' '}`
            if (isLast) {
                labelComp.string = str
                return
            }
        }
        labelComp.string = isLeftAlign ? `${labelStr.substring(0, len)}${labelStr.length > len ? '...' : ''}` : `${labelStr.length > len ? '...' : ''}${labelStr.substring(0, len)}`
    }

    /**
     * @zh 适配精灵图片高度 @en adapter sp height
     */
    public static setSpriteFrameAndAdapteHeight(sprite: Sprite, spriteFrame: SpriteFrame) {
        const defaultHeight = this.getHeight(sprite.node)
        if (!defaultHeight) return
        const nativeWidth = defaultHeight / spriteFrame.originalSize.height * spriteFrame.originalSize.width
        const scaleRadio = nativeWidth / defaultHeight
        sprite.spriteFrame = spriteFrame
        this.setWidth(sprite.node, scaleRadio >= 1 ? nativeWidth : (nativeWidth / scaleRadio))
        this.setHeight(sprite.node, scaleRadio >= 1 ? defaultHeight : (defaultHeight / scaleRadio))
    }

    /**
     * @zh 设置节点层级 @en set node layer
     */
    public static setIndex(node: Node, index: number) {
        if (!node.isValid) return
        node.setSiblingIndex(index)
    }

    /**
     * @zh 设置对齐 @en set horizen align
     */
    public static setLayoutAlign(node: Node, direction: number) {
        if (!node.isValid) return
        node.getComponent(Layout).horizontalDirection = direction
    }
}
