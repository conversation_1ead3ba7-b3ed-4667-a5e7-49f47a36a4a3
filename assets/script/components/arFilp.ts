import { Component, Label, macro, RichText, Sprite, _decorator, Node, UITransform, HorizontalTextAlignment } from "cc";
import { I18n } from "../framework/i18n";
const { ccclass, property } = _decorator;

/**
 * ar 语言翻转脚本
 * <AUTHOR>
 */
@ccclass("ArFilp")
export default class ArFilp extends Component {
    @property
    isNeedAllFilp: boolean = true;
    onLoad() {
        this.init();
    }

    init() {
        if (I18n.languageState !== 2) return;
        // 整体翻转
        if (this.isNeedAllFilp) {
            const curScaleX = this.node.scale.x;
            this.node.setScale(-curScaleX, this.node.scale.y, this.node.scale.z);
        }
        this.findAllChildren(this.node);
    }

    private findAllChildren(root: Node) {
        const childrenLen = root.children.length
        for (let i = 0; i < childrenLen; i++) {
            const lab = root.children[i].getComponent(Label);
            const richTextLab = root.children[i].getComponent(RichText);

            const sprite = root.children[i].getComponent(Sprite);
            if (lab) {
                this.handleLab(lab);
            }
            else if (richTextLab) {
                this.handleRichLab(richTextLab);
            }
            else if (sprite) {
                this.handleImg(sprite);
            }
            if (root.children[i].children.length > 0) {
                this.findAllChildren(root.children[i]);
            }
        }
    }

    handleLab(lab: Label) {
        lab.node.setScale(- lab.node.scale.x, lab.node.scale.y, lab.node.scale.z);
        const uiT = lab.node.getComponent(UITransform);
        switch (uiT.anchorX) {
            case 0.5:// 不变更位置信息
                break;
            case 0: //从左到右
                lab.horizontalAlign = Label.HorizontalAlign.RIGHT;
                uiT.anchorX = 1;
                break;
            case 1: // 从右到左
                lab.horizontalAlign = Label.HorizontalAlign.LEFT;
                uiT.anchorX = 0;
                break;
            default:
                break;
        }
    }

    handleRichLab(lab: RichText) {
        lab.node.setScale(- lab.node.scale.x, lab.node.scale.y, lab.node.scale.z);
        const uit = lab.node.getComponent(UITransform);
        switch (uit.anchorX) {
            case 0.5:// 不变更位置信息
                break;
            case 0: //从左到右
                lab.horizontalAlign = HorizontalTextAlignment.RIGHT;
                uit.anchorX = 1;
                break;
            case 1: // 从右到左
                lab.horizontalAlign = HorizontalTextAlignment.LEFT;
                uit.anchorX = 0;
                break;
            default:
                break;
        }
    }

    handleImg(sprite: Sprite) {
        if (sprite.node.children.length <= 0) {
            sprite.node.setScale( - sprite.node.scale.x, sprite.node.scale.y ,  sprite.node.scale.z ) ;
        }
    }


}