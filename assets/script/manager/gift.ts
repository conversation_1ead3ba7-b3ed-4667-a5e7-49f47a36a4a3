import { Component, director, Node, Sprite, _decorator } from "cc";
import { Utils } from "../framework/frameworkUtils";
import globalData from "../globalData";
import { sealClick } from "../seal/SealBridge";
import { sealCallEvents } from "../seal/sealType";
import { commonUtil } from "../utils/commonUtil";
import { convertWorldCoordinate } from "../utils/utils";

const { ccclass, property } = _decorator;

@ccclass("GiftManager")
export default class GiftManager extends Component {

    @property(Node)
    giftNode: Node = null

    @property(Sprite)
    giftSp: Sprite = null

    async onLoad() {
        await Utils.timeOut1(5000)
        director.emit(sealCallEvents.notifyGitfStatus)
    }

    /**
     * @name 礼物点击事件
     */
    public onClickGiftEvent(e) {
        e.cancelable = false
        const userId = e.currentTarget.name
        console.info("click gift", userId)
        globalData.cancelHosting(userId)
        const { x, y } = convertWorldCoordinate(this.giftNode)
        sealClick({
            notifyName: 'giftClick',
            data: {
                userId: userId,
                coordinate: {
                    x: x / 2,
                    y: y / 2,
                    width: commonUtil.getWidth(this.giftNode) / 2,
                    height: commonUtil.getHeight(this.giftNode) / 2
                }
            }
        }, () => {})
    }

    /**
     * @name 设置礼物图标
     * @description set user's gift
     * @param url 
     */
    public async setSeatGiftIcon(url: string) {
        const giftNode = this.giftSp.node
        commonUtil.setWidth(giftNode, 72);
        commonUtil.setHeight(giftNode, 72);
        // this.giftSp.spriteFrame = await loadRemoteImg(url)
    }
}
