import { rdsTraceReport } from "../seal/bridge";

export class rdsTrack {

    /**
     * EVENT_PIWAN_MINIGAME_COST_TIME_FLOW。
     */
    public static rdsGameCostTimeFlow(link: "GetToken" | "WebSocket" | "LoadLudoGame" | "GamePlaying" | "LoadGameBoard"
        , costTime, totalCostTime, result: "Success" | "Fail") {
        const curTime = new Date().getTime();
        rdsTraceReport('EVENT_PIWAN_MINIGAME_COST_TIME_FLOW', {
            link: `Cocos_${link}`,
            costTime: curTime - costTime,
            totalCostTime: curTime - totalCostTime,
            result: result,
        })
    }
}