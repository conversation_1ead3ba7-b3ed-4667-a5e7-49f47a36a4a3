import { Component, Node, ProgressBar, Sprite, Sprite<PERSON>rame, _decorator } from "cc";
import globalData from "../globalData";
import { sealClick } from "../seal/SealBridge";
import { ChessType, PlayerStatus } from "../type";
import { commonUtil } from "../utils/commonUtil";
import { sensorClick } from "../utils/sensor";
import { convertChessKey, convertWorldCoordinate, loadRemoteImg } from "../utils/utils";

const { ccclass, property } = _decorator;

@ccclass("ProcessCoverManager")
export default class ProcessCoverManager extends Component {
  @property(Sprite)
  borderSpComp: Sprite = null // 边框

  @property(Sprite)
  coverSpComp: Sprite = null; // 封面

  @property(ProgressBar)
  progressComp: ProgressBar = null // 进度条

  @property(Node)
  teamNode: Node = null;

  _teamName = "";

  private _userId

  private _defaultSpriteframe

  /**
   * @zh 头像初始化 @en init avator node
   * @param userId 
   * @param userIndex 
   * @param portrait 
   */
  public init(userId, userIndex, portrait) {
    this._setUserId(userId)
    this._setBorderSp(convertChessKey(userIndex) as ChessType)
    loadRemoteImg(portrait).then((res: SpriteFrame) => this._setCoverSp(res))
  }

  /**
   * @name 封面
   * @description set cover iamge
   */
  private _setCoverSp(coverSp: SpriteFrame) {
    if (!coverSp) return
    // this.coverSpComp.spriteFrame = coverSp
    commonUtil.setSpriteFrameAndAdapteHeight(this.coverSpComp, coverSp)
  }

  /**
   * @name 边框
   * @description set border iamge
   */
  private _setBorderSp(borderType: ChessType) {
    if (!borderType) return
    this.borderSpComp.spriteFrame = this._defaultSpriteframe = this.borderSpComp.spriteAtlas.getSpriteFrame(`${borderType}-border`)
    // console.error('get width', commonUtil.getWidth(this.borderSpComp.node), commonUtil.getHeight(this.borderSpComp.node))
  }

  private _setUserId(userid: string) {
    this._userId = userid
  }

  public setTeamName(teamName: string) {
    this._teamName = teamName;
  }

  /**
   * @description toggle border visiable
   */
  public toggleBorderVisiable(isVisiable: boolean) {
    if (this.borderSpComp.isValid) {
      this.borderSpComp.spriteFrame = isVisiable ? this._defaultSpriteframe : null
    }
  }

  /**
   * @name 点击头像回调
   * @description click callback event about user avator
   */
  public onClickAvatorCallback() {
    const _userId = this._userId
    console.log('click avator node', _userId)
    globalData.cancelHosting(_userId)
    if (
      globalData.myUserId === _userId &&
      globalData.getUserInfoByUserId(_userId)?.status === PlayerStatus.Hosting) {
      return
    }
    // sensorClick({
    //   $title: 'ludo游戏房页',
    //   page_type: 'ludo',
    //   $element_name: '头像',
    //   // userId: that.node.name
    // })
    const { x, y } = convertWorldCoordinate(this.node)
    sealClick(
      {
        notifyName: 'portraitClick',
        data: {
          userId: _userId,
          coordinate: {
            x: x / 2,
            y: y / 2,
            width: commonUtil.getWidth(this.node) / 2,
            height: commonUtil.getHeight(this.node) / 2
          }
        }
      },
      () => { })
  }
}
