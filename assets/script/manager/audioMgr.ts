import { AudioClip, AudioSource, clamp01, Node } from "cc"
import { LoadMgr } from "../framework/loadMgr"
import { cs } from "../framework/log";

const gameSoundList = [
  "audio/sound_btn",              // 棋子音效
  "audio/sound_chessEnter",       // 棋子进入终点
  "audio/sound_eat",              // 被吃掉
  "audio/sound_eatBack",          // 被吃掉时返回家
  "audio/sound_iMove",            // 到自己移动
  "audio/sound_otherMove",        // 到其他人移动
  "audio/sound_outTime",          // 倒计时
  "audio/sound_pieceMove",        // 棋子移动
  "audio/sound_rock6",            // 摇到6
  "audio/sound_useDice",          // 摇一摇骰子
  "audio/sound_flyCoin",          // 飞金币  
  "audio/sound_numScale",         // 小数字倍率变化
  "audio/sound_coinMoveAllValue", // 金币总量变化
  "audio/sound_reduceAdd",        // 加减300金币
  "audio/sound_resultFail",       // 游戏失败
  "audio/sound_resultWin",        // 游戏成功
  "audio/sound_match",            // 匹配声音
];

export class AudioMgr {

  private static _instance: AudioMgr

  private _isMuted: boolean = false // 静音选项

  static get instance() {
    if (!this._instance) this._instance = new AudioMgr()
    return this._instance
  }

  private _isLoop = false
  private _volume = 1
  private _soundMap: Map<string, AudioSource> = new Map()

  /**
   * @zh 初始化声音资源 @en init sound resource
   * @param node
   */
  public init(node: Node) {
    if (!node?.isValid) {
      cs.log('register invalid node about audio')
      return
    }
    this._soundMap = new Map()
    gameSoundList.forEach((audioUrl: string) => {
      LoadMgr.instance.loadRes(audioUrl, AudioClip).then((resource: [null, any]) => {
        if (resource[0]) {
          cs.error("load AudioSource", audioUrl, resource[1])
        } else {
          const child = new Node(audioUrl)
          const audioSource = child.addComponent(AudioSource)
          audioSource.playOnAwake = false
          audioSource.loop = this._isLoop
          audioSource.clip = resource[1]
          child.parent = node
          this._soundMap.set(audioUrl, audioSource)
        }
      })
    })
  }

  /**
   * @zh 设置音效开关 @en toggle current audio
   * @param isMuted 是否静音
   */
  public setAudioMute(isMuted: boolean) {
    this._isMuted = !!isMuted
  }

  /**
   * @zh 播放音乐 @en play music
   * @param loop 是否循环播放
   */
  public play(name) {
    if (this._isMuted) return
    this._soundMap.get(name)?.play()
  }

  /**
   * @zh 播放音效 @en play sound
   * @param name 音效名称
   * @param volumeScale 播放音量倍数
   */
  public playSound(name: string, volumeScale: number = 1) {
    if (this._isMuted) return
    // 注意：第二个参数 “volumeScale” 是指播放音量的倍数，最终播放的音量为 “audioSource.volume * volumeScale”
    const audioSource = this._soundMap.get(name)
    if (audioSource) {
      audioSource.playOneShot(audioSource.clip, volumeScale)
    }
  }

  /**
   * @zh 设置音乐音量 @en set music volume
   * @param flag 音量
   */
  public setMusicVolume(audioSource: AudioSource, flag: number) {
    // assert(audioSource, 'AudioManager not inited!')
    audioSource.volume = clamp01(flag || this._volume)
  }

}
