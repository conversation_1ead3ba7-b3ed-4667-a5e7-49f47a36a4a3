import { director } from 'cc';
import globalData from '../globalData';
import { sealBridge } from '../seal/SealBridge';
import wsClient from './wsClient';
export default class WsUtil {

  private static TIMEOUT_DURATION: number = 10000;
  private static _requestKey: number = 1;
  public static get requestKey(): number {
    return WsUtil._requestKey;
  }
  public static set requestKey(value: number) {
    WsUtil._requestKey = value;
  }
  private static _timeOutCnt = 0;
  public static get timeOutCnt() {
    return WsUtil._timeOutCnt;
  }
  public static set timeOutCnt(value) {
    WsUtil._timeOutCnt = value;
  }
  private static requestMap: object = {};
  private static timerMap: object = {};

  /**
   * websocket send
   * @param params 参数
   * @param h5callId 回调通知
   */
  public static async send(ws: wsClient, params: any, h5callId?: string) {
    // console.log('send parms', params);
    try {
      let key = ''
      if (params.message && !params.message.noBack) {
        key = `${this.requestKey}`
        params.requestKey = key
        this.requestMap[key] = params
        this.timerMap[key] = setTimeout(() => {
          if (this.requestMap[key]) {
            // 超时
            this.timeOutCnt++;
            console.warn('接口超时: ', params.type, params)
            director.emit('checkWebsocketConnect');
            sealBridge.h5Callback(h5callId, { "status": "fail" })
          }
        }, this.TIMEOUT_DURATION)
        this.requestKey += 1
      }
      params.message.token = params.token/* token */
      // console.info('===socket send params:===', params)
      ws.postMessage(JSON.stringify(params))
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * websocket 清空requestKey 和超时逻辑
   * @param response
   */
  public static receive(response: any) {
    // console.info('###websocket onmessage:###', response)
    // if (response.requestKey == '' && response.type == 6) {
    //   console.info('被动收到type=6事件', response)
    // }
    const { requestKey } = response || {}
    const key = `${requestKey}`
    if (this.requestMap[key]) {
      delete this.requestMap[key]
    }
    if (this.timerMap[key]) {
      clearTimeout(this.timerMap[key])
      delete this.timerMap[key]
    }
  }


  /**
   * 心跳逻辑，用户维护websocket连接
   */
  private static serverTimeOutTimer: number = null
  public static heartCheck(ws: wsClient) {
    this.serverTimeOutTimer && clearTimeout(this.serverTimeOutTimer);
    this.serverTimeOutTimer = setTimeout(() => {
      let params = {
        type: 120,
        message: { info: 'heartCheck' },
      }
      this.send(ws, params)
      this.heartCheck(globalData.WS);
    }, 12000)
  }
}
