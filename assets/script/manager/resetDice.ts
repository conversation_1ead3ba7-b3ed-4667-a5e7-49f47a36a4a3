import { SOCKET_TYPE } from '../network/constants';
import globalData from "../globalData";
import ActionManager from "./action";
import { Component, Label, Node, ProgressBar, sys, v3, _decorator } from 'cc';
import { commonUtil } from '../utils/commonUtil';
import { I18n } from '../framework/i18n';
import { throttleFunc } from '../utils/utils';

/**
 * 重置按钮逻辑
 */
const { ccclass, property } = _decorator;

@ccclass("ResetDiceManager")
export default class ResetDiceManager extends Component {

  @property(Label)
  resetLabel: Label = null // 重置文案

  @property(ProgressBar)
  progressBar: ProgressBar = null

  @property(Node)
  resetIconNode: Node = null

  @property(Node)
  resetProgressNode: Node = null

  private userInfo = null

  public onClickPlayerResetDice = throttleFunc(this.onClickResetDice.bind(this), 500) // 点击玩家重置按钮防抖

  protected onLoad() {
    this.node?.on?.("click", this.onClickPlayerResetDice, this)
    const label = window.cocosI18n?.free || "Free"
    if (this.resetLabel) {
      this.resetLabel.string = sys.Platform.ANDROID ? ("  " + label + "  ") : label
      this.adapterLabelIOSPosition()
    }
  }

  /**
   * IOS文案适配问题
   * @param noAdapter 是否适配
   */
  public adapterLabelIOSPosition(isNormal?: boolean) {
    if (sys.Platform.IOS && I18n.languageState === 2) {
      commonUtil.setPosition(this.resetLabel?.node, v3(isNormal ? 0 : 10, 10, 0))
    }
  }

  public async initResetDiceView(userInfo) {
    this.userInfo = userInfo

    let currentUserId = this.userInfo.userId
    const { myUserId, gameInfo } = globalData
    const { resetInfo } = gameInfo

    if (currentUserId == myUserId && resetInfo) {
      let target = resetInfo.find(item => item.userId == currentUserId) || { freeCount: 0, price: '0' }
      console.log('resetbtn target', target)
      const label = I18n.languageState === 2 ? ("  " + window.cocosI18n?.free + "  ") : window.cocosI18n?.free
      this.setResetDiceView(target.freeCount > 0 ? label : `${target.price}`)
    }
      this.toggleButtonView()
    }

    public toggleButtonView(isValid?: boolean) {
      if (globalData.myUserId !== this.userInfo?.userId) {
        commonUtil.setOpacity(this.node, 0);
        // this.node.opacity = 0
        return
      }
      commonUtil.setOpacity(this.resetIconNode, isValid ? 255 : 155);
      // this.resetIconNode.opacity = isValid ? 255 : 155
      commonUtil.setOpacity(this.node, isValid ? 255 : 250);
      // this.node.opacity = isValid ? 255 : 250
    }

  public onClickResetDice() {
    globalData.cancelHosting(globalData.myUserId)
    if (!(globalData.isMyRound && commonUtil.getOpacity(this.resetIconNode) > 200)) return
    globalData.clickResetDiceTime = Date.now()
    globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand: { commandKey: 'resetDiceNum' } })
    this.playPlayerResetCountTime(0)
  }

  // 切换免费按钮视图
  public setResetDiceView(label: string) {
    this.resetLabel.string = label || ''
    this.adapterLabelIOSPosition(Number(label) > 0)
  }

  private resetCloseTimmer = null
  public playPlayerResetCountTime(countTime: number) {
    commonUtil.setActive(this.resetProgressNode, true)
    let lastTime = null
    ActionManager.playCountdown(
      this.progressBar,
      countTime,
      (dt) => {
        if (!lastTime) lastTime = dt
        this.toggleButtonView((countTime - (dt - lastTime)) >= 0)
        this.resetCloseTimmer && clearTimeout(this.resetCloseTimmer)
        this.resetCloseTimmer = setTimeout(() => {
          commonUtil.setActive(this.resetProgressNode, false)
          this.toggleButtonView(false)
          this.resetCloseTimmer && clearTimeout(this.resetCloseTimmer)
        }, 200)
      }
    )
  }

}
