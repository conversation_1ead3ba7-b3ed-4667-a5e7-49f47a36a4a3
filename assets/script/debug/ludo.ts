import { Prefab, instantiate, director, Node, Socket } from "cc"
import { Utils } from "../framework/frameworkUtils"
import globalData from "../globalData"
import ViewManager from "../manager/view"
import { wsMgr } from "../manager/wsMgr"
import { END_MODE, GAME_MODE, GAME_STAGE, SOCKET_TYPE } from "../network/constants"
import { sealBridge } from "../seal/SealBridge"
import { DebugGameStatus } from "../type/index"
import { getUrlParam1, loadLoaclResource } from "../utils/utils"


const toggleDebugModal = function (debugNode: Node, gameState?: DebugGameStatus) {
  if (!debugNode) return
  if (sealBridge.debug) {
    // 待加入状态，显示待加入页面、更新玩家列表信息
    switch (gameState) {
      case DebugGameStatus.Match: {
        debugNode.active = true
        debugNode.getChildByName('ReadyModal').active = false
        debugNode.getChildByName('MatchingModal').active = true
        director.emit('updateMatchingModalUI')
        break
      }
      case DebugGameStatus.Ready: {
        debugNode.active = true
        debugNode.getChildByName('ReadyModal').active = true
        debugNode.getChildByName('MatchingModal').active = false
        break
      }
      case DebugGameStatus.Gaming:
      default: {
        debugNode.active = false
      }
    }
  } else {
    debugNode.active = false
  }
}

const initOnlineGame = (parentNode) => {
  let timer = null
  const timerCallback = (debugNode) => {
    if ([GAME_STAGE.JOIN, GAME_STAGE.INIT].includes(globalData.gameInfo.gameStatus) && globalData.gameInfo?.playerInfo?.length === 0) {
      toggleDebugModal(debugNode, DebugGameStatus.Ready)
    } else if ([GAME_STAGE.GAMEING].includes(globalData.gameInfo.gameStatus)) {
      timer && clearInterval(timer)
      toggleDebugModal(debugNode, DebugGameStatus.Gaming)
    } else {
      toggleDebugModal(debugNode, DebugGameStatus.Match)
    }
  }
  loadLoaclResource('prefab/debug', Prefab)
    .then((debugPrefab: Prefab) => {
      const debugNode = instantiate(debugPrefab)
      debugNode.parent = parentNode
      debugNode.getChildByName('wsMgr').getComponent(wsMgr).init()
      Utils.timeOut1(200).then(_ => {
        director.emit('INIT-PRELOAD', getUrlParam1('userId'), '')
      })
      Utils.timeOut1(3000).then(_ => {
        globalData.socketSend(SOCKET_TYPE.BIND_ROOM, {})
      })
      Utils.timeOut1(5000).then(_ => {
        globalData.socketSend(SOCKET_TYPE.CREATE_GAME, {});
        globalData.socketSend(SOCKET_TYPE.LOGIN, {});
        globalData.socketSend(SOCKET_TYPE.RE_JOIN, {});
        globalData.socketSend(SOCKET_TYPE.GAME_INFO, {});
        // globalData.socketSend(SOCKET_TYPE.RE_JOIN, {});
        // globalData.socketSend(SOCKET_TYPE.GAME_INFO, {});
      })
      Utils.timeOut1(8000).then(_ => {
        timer = setInterval(() => {
          timerCallback(debugNode)
        }, 100)
      })
    })
}

/**
 * @zh 测试ludo游戏场景 @en test ludo game
 */
const initLocalGame = () => {
  // name: "ال مقدار اكل الفائز",
  //  " مقدار اكل الفائز"
  // name: " مقدار اكل الفائز",
  // " مقدار اكل الفائز",
  let playerList = [
    { userId: "111", name: '2223', portrait: 'https://cdnimg.cqpiwan.com//user//2022//04//07//2933626050215201794_320x320.jpg', userIndex: 1, isAuto: false, portraitMask: "", status: 1 },
    { userId: "222", name: '12313', portrait: " https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQQtVs1FqmZhWnLMNrdyKTTS2KB2UYIDygwEA&usqp=CAU", userIndex: 2, isAuto: false, portraitMask: "", status: 2, vipMedalEffect: 'http://*************:8080/vipEffect.png' },
    { userId: "333", name: '11', portrait: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSOdn9kgri-0dtvdcyU1F0vnSj1sUhCMtg75A&usqp=CAU", userIndex: 3, isAuto: false, portraitMask: "", status: 3, vipMedalEffect: 'http://*************:8080/vipEffect.png' },
    { userId: "444", name: '1233332312321', portrait: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSsAaUQqpRQ1hYDzZiceDJt3QiLHFucgzYwCQ&usqp=CAU", userIndex: 4, isAuto: false, portraitMask: "", status: 5, vipMedalEffect: 'http://*************:8080/vipEffect.png' },
  ]
  globalData.gameInfo.gameMode = GAME_MODE.FOUR_BATTLE;
  globalData.gameInfo.endMode = END_MODE.CLASSIC;
  globalData.gameInfo.playerInfo = playerList
  const finalChessDatas = '[{"chessId":"1-1","chessIndex": 0},{"chessId":"1-2","chessIndex":45},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":115},{"chessId":"2-2","chessIndex":115},{"chessId":"2-3","chessIndex":115},{"chessId":"2-4","chessIndex":115},{"chessId":"3-1","chessIndex":125},{"chessId":"3-2","chessIndex":125},{"chessId":"3-3","chessIndex":125},{"chessId":"3-4","chessIndex":125},{"chessId":"4-1","chessIndex":0},{"chessId":"4-2","chessIndex":135},{"chessId":"4-3","chessIndex":135},{"chessId":"4-4","chessIndex":135}]'
  const chessDatas = `[{"chessId":"1-1","chessIndex":0},{"chessId":"1-2","chessIndex":20},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":0},{"chessId":"2-2","chessIndex":0},{"chessId":"2-3","chessIndex":0},{"chessId":"2-4","chessIndex":0},{"chessId":"3-1","chessIndex":0},{"chessId":"3-2","chessIndex":0},{"chessId":"3-3","chessIndex":0},{"chessId":"3-4","chessIndex":0},{"chessId":"4-1","chessIndex":0},{"chessId":"4-2","chessIndex":0},{"chessId":"4-3","chessIndex":0},{"chessId":"4-4","chessIndex":0}]`

  ViewManager.initUiInfo(3, playerList, JSON.parse(finalChessDatas), 2, [100, 110])
  
  const testData = {
    "roundEnd": false,
    "eatOff": ["2-1"],
    "movePath": [15, 16],
    "eatChessFlag": true,
    "obstacleInfo": [110, 120],
    "restDiceNumArray": [],
    "finalChesses": [{
      "chessId": "1-1",
      "chessIndex": 14
    }, {
      "chessId": "1-2",
      "chessIndex": 1
    }, {
      "chessId": "1-3",
      "chessIndex": 0
    }, {
      "chessId": "1-4",
      "chessIndex": 0
    }, {
      "chessId": "2-1",
      "chessIndex": 0
    }, {
      "chessId": "2-2",
      "chessIndex": 0
    }, {
      "chessId": "2-3",
      "chessIndex": 0
    }, {
      "chessId": "2-4",
      "chessIndex": 14
    }, {
      "chessId": "3-1",
      "chessIndex": 36
    }, {
      "chessId": "3-2",
      "chessIndex": 27
    }, {
      "chessId": "3-3",
      "chessIndex": 0
    }, {
      "chessId": "3-4",
      "chessIndex": 0
    }, {
      "chessId": "4-1",
      "chessIndex": 45
    }, {
      "chessId": "4-2",
      "chessIndex": 42
    }, {
      "chessId": "4-3",
      "chessIndex": 41
    }, {
      "chessId": "4-4",
      "chessIndex": 41
    }],
    "userCoinInfo": [{
      "isEatenChessNum": 0,
      "userIndex": 1,
      "multiple": 1,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2020/05/26/2807309702636112386.jpg",
      "userId": "5113152715523891756",
      "balance": 0,
      "eatChessNum": 1,
      "teamId": 5288321675268461695,
      "name": "1256🙉",
      "antes": 10,
      "coin": 50,
      "teamName": "A"
    }, {
      "isEatenChessNum": 5,
      "userIndex": 2,
      "multiple": 5,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2020/12/29/2847574727060536322.png",
      "userId": "5237594338376025644",
      "balance": -10,
      "eatChessNum": 0,
      "teamId": 5766278482510254080,
      "name": "funbox8050",
      "antes": -10,
      "coin": 99991225,
      "teamName": "B"
    }, {
      "isEatenChessNum": 0,
      "userIndex": 4,
      "multiple": 5,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2022/05/16/2940891298963727362.jpg",
      "userId": "5236517478397395500",
      "balance": 10,
      "eatChessNum": 5,
      "teamId": 5766278482510254080,
      "name": "8048",
      "antes": 10,
      "coin": 65866,
      "teamName": "B"
    }, {
      "isEatenChessNum": 1,
      "userIndex": 3,
      "multiple": 1,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2020/05/19/2805997298069438978.jpg",
      "userId": "5111840305364163756",
      "balance": 0,
      "eatChessNum": 2,
      "teamId": 5288321675268461695,
      "name": "123456756",
      "antes": 10,
      "coin": 40,
      "teamName": "A"
    }],
    "opIndex": 1,
    "opUserId": "5113152715523891756",
    "chessId": "1-1"
  };
  const { opUserId, opIndex, chessId, movePath, eatOff, finalChesses, restDiceNumArray, obstacleInfo, userCoinInfo, roundEnd, eatChessFlag } = testData;
  console.error("====测试====")
  
  setTimeout(() => {
    // ViewManager.chessMove(chessId, movePath, eatOff, finalChesses, obstacleInfo, userCoinInfo, opIndex, restDiceNumArray, roundEnd);
  }, 10000)
}

/**
 * @zh 测试游戏 @en test game
 */
export const testGame = (parentNode) => {
  if (getUrlParam1('roomId') && getUrlParam1('userId')) {
    globalData.roomId = getUrlParam1('roomId')
    globalData.token = getUrlParam1('userId')
    initOnlineGame(parentNode)
  } else {
    initLocalGame()
  }
}
