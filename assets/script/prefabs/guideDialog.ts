import { Component, Label, Layout, Node, tween, v3, Vec3, _decorator } from "cc";
import { commonUtil } from "../utils/commonUtil";

const { ccclass, property } = _decorator;

@ccclass("GuideDialog")
export default class GuideDialog extends Component {

    @property(Node)
    bgNode: Node

    @property(Node)
    labelNode: Node

    public movePosition(position: Vec3) {
        this.node.setPosition(v3(position.x, position.y + 40, position.z))
        commonUtil.setOpacity(this.node, 0);
        // this.node.opacity = 0
        return new Promise((resolve) => {
            tween(this.node)
                .to(1, { position })
                .call(() => {
                    resolve('')
                })
                .start()
        })
    }

    public setDialogSize(height: number, width: number) {
        this.bgNode.getComponent(Layout).paddingTop = height
        this.bgNode.getComponent(Layout).paddingBottom = height
        commonUtil.setWidth(this.bgNode, width);
        // this.bgNode.width = width
    }

    public setDialogLabel(labelStr: string) {
        if (!this.labelNode) return
        // this.labelNode.getComponent(RichText).string = labelStr
        this.labelNode.getComponent(Label).string = labelStr
    }
}
