/**
 *  i18n 配置文件类
 *  <AUTHOR>
 */
export class I18n {
    /**
     * 语言类型 
     * 0 中文  1 英文  2 阿拉伯
     */
    public static languageState: number = 1;
    private static language: object = {
        antes: {
            cn: "获得", en: "WIN", ar: "ربحت" + "  "
        },
        multiple: {
            cn: "倍数", en: "Multiplier", ar: "مضاعف"
        },
        catchChess: {
            cn: "吃棋", en: "BEAT", ar: "أكلت" + "  "
        },
        endWinTips: {
            cn: "结束时，赢家<color=#0ABB46>获得</color>=底注X赢家吃棋数X3位玩家X95%",
            en: `<color=#11929B>Coins winner <color=#0ABB46>won</color> in this round:Bets * Multiples* 3 * 95%(multiples=beats amount of winner;3 means other players except winner)</color>`,
            ar: "<color=#11929B>" + `العملات التي حصلت الفائز = رهان * مضاعف * 3 * 95٪（المضاعف =  مقدار اكل الفائز； 3 تعني ثلاثة لاعبين）` + "</color>",
        },
        endWinCatchChess: {
            cn: (a: number) => {
                return `赢家吃棋 ${a} 枚`
            },
            en: (a: number) => {   // 改动了
                return `Winner beated ${a} `
            },
            ar: (a: number) => {
                return ` الفائز اكلت ${a} `
            },
        },
        platformCharges: {
            cn: "Funbox费率",
            en: "Funbox fees",
            ar: "Funbox رسوم",
        },
        catchChessTips: {
            cn: "游戏中，吃棋立即奖罚",
            en: "Rewards of beating will sent at once in games",
            ar: "في اللعبة ، يكافأ على الفور بعد أكل القطع",
        },
        catchChessNum: {
            cn: (a: number) => {
                return `吃棋 ${a} 枚`
            },
            en: (a: number) => {
                return `Beated ${a}`
            },
            ar: (a: number) => {
                return `أكل ${a} قطع `
            },
        },
        beCatchChess: {
            cn: "被吃棋",
            en: "Was beated",   // 修改
            ar: "قطع المأكول ",
        },
        bankruptcyNum: {
            cn: (a: number) => {
                return `${a} 位破产玩家`
            },
            en: (a: number, b: number) => {
                return `${a} bankrupt,${b} quited`;
            },
            ar: (a: number, b: number) => {
                return `${b} مفلس  ${a} هارب`;
            },
        },
        actuallyReceived: {
            cn: "实收",
            en: "Real income",
            ar: "تلقى بالفعل",
        },
        back: {
            cn: "返回",
            en: "Back",
            ar: "رجوع",
        },
        nextGame: {
            cn: "再玩一局",
            en: "Play again",
            ar: "ألعب مرة اخرى",
        },//-----
        failEndTips: {
            cn: "结束时，<color=#F85A5B>输家扣除</color> = 底注X赢家吃棋数",
            en: "Coins loser <color=#F85A5B>lost</color> in this round:Bets * Multiples (multiples = beats amount of winner)",
            ar: 'العملات التي يخصم الخاسر =  رهان * مضاعف （المضاعف =  مقدار   اكل الفائز）',
        },
        tips: {
            cn: "tips",
            en: "Tips",
            ar: "نصائح",
        },
        bankruptcyTips: {
            cn: "您的金币余额为0，已被淘汰。",
            en: "Your coins left 0 ,has been out.",
            ar: "رصيدك من الذهب هو 0 وتم خروج من اللعبة.",
        },
        leaveGameTime: {
            cn: "15秒后自动离开游戏...",
            en: "Automatically exit the game in 15 seconds...",
            ar: "مغادرة اللعبة تلقائيًا بعد 15 ثانية ...",
        },
        bankruptcy: {
            cn: "破产",
            en: "Bankruptcy",
            ar: "الإفلاس",
        },
        leaveGame: {
            cn: "离开游戏",
            en: "Quit game",
            ar: "اترك اللعبة",
        },
        continueToWatch: {
            cn: "继续围观",
            en: "Become spectator",
            ar: "كن مشاهدا",
        },

        // TODO 新增未同步翻译更新
        guideStartGame: {
            cn: "开始游戏",
            en: "start Game",
            ar: "استمر في المشاهدة",
        },
        ok: {
            cn: "确定",
            en: "OK",
            ar: "اوك",
        },
        coinZero: {
            cn: "您的金币余额为0",
            en: "Your coins left ",
            ar: "رصيد العملة الذهبية هو",
        },
        washout: {
            cn: "已被淘汰",
            en: "has been out.",
            ar: " لقد تم إقصاؤك",
        },
        escape: {
            cn: "逃跑玩家扣除局验资",
            en: "<color=#11929B>Deduct the entrance fee of the quitting player</color>",
            ar: "<color=#11929B>" + "خصم رسوم دخول اللاعب الهارب" + "</color>",
        },
        escapeVerification: {
            cn: "局验资",
            en: "Entrance Fee",
            ar: `رسم دخول`,
        },
        endWinCatchChess2v2: {
            cn: (a: number) => {
                return `<color=#FFFFFF>赢家吃棋<color=#FFFA78> ${a} </color>枚</color>`
            },
            en: (a: number) => {   // 改动了
                return `<color=#FFFFFF>Winner beated</color><color=#FFFA78> ${a} </color>`
            },
            ar: (a: number) => {
                return "" + ` الفائز اكلت ${a}` + "  "
            },
        },
        endWinTips2v2: {
            cn: (a: number) => {
                return `获胜队奖励=底注X获胜队总吃棋数x2位对手玩家x${a}%\n获胜队员奖励=获胜队奖励÷2，逃跑破产无奖`;
            },
            en: (a: number) => {
                return "<color=#11929B>" + `Winner team won: Bets * Multiples*2 opponents* ${a}%（multiples=beats amount of winner team）\nWinning teammate rewards = Team rewards / 2, no reward for quitting and bankruptcy` + "</color>";
            },
            ar: (a: number) => {
                return "<color=#11929B>" + "  " + `لعملات التي حصلت الفريق الفائز = رهان*مضاعف*المعارضين* ${a}٪ \n  المضاعف = مقدار اكل الفائز\n مكافأة الفريق الفائز = مكافأة الفريق الفائز / 2 \n لا مكافأة على الهروب والإفلاس` + "  " + "</color>";
            },
        },
        failEndTips2v2: {
            cn: "输队队员扣除=底注x获胜队总吃棋数",
            en: "<color=#11929B>" + "Loser team lost: Bets * Multiples（multiples=beats amount of winner team）" + "</color>",
            ar: "<color=#11929B>" + "  " + "العملات التي يخصم الخاسر =  رهان * مضاعف \n المضاعف = مقدار اكل الفريق الفائز" + "  " + "</color>",
        },
        teamName: {
            cn: (a: string) => {
                return `团队${a}`;
            },
            en: (a: string) => {
                return `Team${a}`;
            },
            ar: (a: string) => {
                return "  " + `${a}فريق` + "  ";
            },
        },
        coinZero2v2: {
            cn: "您的金币余额，",
            en: "Your coin balance is，",
            ar: "  " + "رصيدك من العملات المعدنية هو" + "  "
        },
        gameOut2v2: {
            cn: "已被淘汰",
            en: "you are eliminated;",
            ar: "  " + "، لقد تم استبعادك" + "  "
        },
        gameOutNotReward2v2: {
            cn: "如所在队获胜，您也不会获得奖金",
            en: "Even if your team wins, you will not receive the coins",
            ar: "  " + "؛ حتى لو فاز فريقك ، فلن تحصل على المكافأة" + "  "
        },
        second: {
            cn: "秒",
            en: " seconds",
            ar: "  " + " ثانية" + "  "
        },
        secondTip: {
            cn: "倒计时后，自动回到首页",
            en: "After the countdown, automatically return to the home page",
            ar: "  " + "بعد العد التنازلي ، تعود تلقائيًا إلى الصفحة الرئيسية " + "  "
        },
        completedTips: {
            cn: "个人战绩达成",
            en: "You completed the goal",
            ar: "  " + "لقد أكملت هدف هذه الجولة " + "  "
        },
        helpYourTeamate: {
            cn: "去帮队友走棋吧",
            en: "After token enters the end, teammate can help move",
            ar: "  " + "نصيحة : بعد دخول القطعة للنهاية \n يمكن لرفيقك مساعدتك في التحرك" + "  "
        },
        leaveGame2v2: {
            cn: "离开游戏",
            en: "Back",
            ar: "  " + "عودة" + "  ",
        },
        nextGame2V2: {
            cn: "再玩一局",
            en: "Next Game",
            ar: "  " + "اللعبة التالية" + "  ",
        },//-----
    }

    /**
     * 获取对应语言string
     * @param key 键值
     * @param language 语言 TODO 后面给定参数之后可以不用传递，直接函数自己获取
     * @returns 返回值，注意：有些返回的是一个方法，需要再次进行获取，因为某些参是需要传递，才能生成最终的string
     */
    public static str(key: string) {
        let languageStr = "en";
        if (this.languageState == 0) {
            languageStr = "cn";
        } else if (this.languageState == 1) {
            languageStr = "en";
        } else if (this.languageState == 2) {
            languageStr = "ar";
        }
        return this.language?.[key]?.[languageStr];
    }

}
