[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "ludo", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 112}, "autoReleaseAssets": true, "_globals": {"__id__": 116}, "_id": "21936b10-d44a-483b-89b9-c96e70b90a08"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 9}, {"__id__": 48}, {"__id__": 52}, {"__id__": 55}, {"__id__": 72}, {"__id__": 102}], "_active": true, "_components": [{"__id__": 105}, {"__id__": 106}, {"__id__": 107}, {"__id__": 108}, {"__id__": 109}, {"__id__": 110}, {"__id__": 111}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 374.99999999999994, "y": 811.9999999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a286bbGknJLZpRpxROV6M94"}, {"__type__": "cc.Node", "_name": "soundLayer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "901iiGmX1JYZKKten8UlFx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f2eqH0Q1ZHuIpAKq3ZFxuI"}, {"__type__": "cc.Node", "_name": "GameBg", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}, {"__id__": 8}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "75Ct8PiHNEVrxMW9MwPV6l"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "845938ac-4461-4e21-9d31-de816c55ac85@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "0ex1UiHzhCdYKd6xA/QsLo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 1800}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cEMC2Q2NGEYPsZV21Hg2F"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_id": "f3/x5fnLpCEbQPDUsVl+J8"}, {"__type__": "cc.Node", "_name": "Game", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 14}, {"__id__": 29}, {"__id__": 31}, {"__id__": 41}, {"__id__": 44}], "_active": true, "_components": [{"__id__": 47}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "590ZYK1W5HcaESatvPFaGV"}, {"__type__": "cc.Node", "_name": "Border", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "40mpMc0L1Kr6daP5vr+iEh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "66d64e3d-1c68-4245-aa36-6fb1c8455adb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "1dvSjpHS5DNLqKwaeS+FJd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 774}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5asiUUYTBHmYLGXQlYBGNm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "64wicH5N9Chp/OqdTT2dnh"}, {"__type__": "cc.Node", "_name": "ChessBoard", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 15}, {"__id__": 19}, {"__id__": 23}], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2dGVYKF+dNGqY2pINyy86S"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}, {"__id__": 18}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "40W7pCEfRKuolCWyML1tAw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "362cfb6e-096b-4c71-9446-42ebbb578f14@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b6A14xibhADZYpeMhNvL4U"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 727, "height": 727}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "65hXc2cGpOlq/T2/4EJ9bR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "4fza5l6n5ALKpikCtMWpaz"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "724L2jaw5PrYg905GGYivo"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "beZ5LXD9RJTLHSGKfJ6fLC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6faoYuCvJNxYB+ThmggGN6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "13SeUeMRNJGbIZHxmzTN/7"}, {"__type__": "cc.Node", "_name": "ObstacleBox", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}, {"__id__": 26}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0ekSn/8JBCU7C62XM3BISW"}, {"__type__": "b7f5d3b361NWKuOw+bwv8yh", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_id": "93bvMvzxRE7qdMPJYu7qF2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2crcOjnq1Jv6fTCSaY7FlW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "51owq4c/xCyrKsJ36dXfvQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "28mCufEcdHYqz9t5rHwUP/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8f3mkthFVBjbRuEMDFni2G"}, {"__type__": "cc.Node", "_name": "ChessGroup", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "87OcvB8shNLr+nfi0lsiwv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1ftCCBXWhOaIa8pY1CokCp"}, {"__type__": "cc.Node", "_name": "UserGroup", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dd9br10mlAj7HWf3N75hua"}, {"__type__": "cc.Node", "_name": "User1", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 168, "y": 478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "990nbGUH1KLpA5w/b6y32K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dfMA3CojxNPaz5duUX0WeY"}, {"__type__": "cc.Node", "_name": "User2", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 168, "y": -490, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "46DAUexslJBZ8yHJY1Q0B0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "54jT1tFxVIC6HQtJYs4Pdu"}, {"__type__": "cc.Node", "_name": "User3", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 37}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -168, "y": -490, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fflxu/1QZFqqdV0iUdx7wd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8d9kP+/ORFKpTqgfJBrqRQ"}, {"__type__": "cc.Node", "_name": "User4", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 39}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -168, "y": 478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "03rLKqn4FLN7a7968KHTSN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "78r6bDZD1MGaCe3ay2UjJf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cfA5sJyYlHH6YR9sDtwBXB"}, {"__type__": "cc.Node", "_name": "faceAnimNode", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 43}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6e7mY2GBJCbWw3tpVS4oR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6cGDedQSFGZ5NNwtzrMYDX"}, {"__type__": "1a4b6UqqLND5qtNIBtb/at8", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "faceNode": {"__uuid__": "6371175e-101f-4bbd-bd22-fdc7c49b106d", "__expectedType__": "cc.Prefab"}, "_id": "c2f4bQOX5IeKOJSc8ggRHw"}, {"__type__": "cc.Node", "_name": "AniBox", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8aSFYlRhJDjbvkjhWhKtIJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d8ULCuDZZOabe5WuAaGQNG"}, {"__type__": "cc.ParticleSystem2D", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": 0.5, "emissionRate": 155.76923076923077, "life": 2.08, "lifeVar": 6.4, "angle": 89, "angleVar": 37, "startSize": 25, "startSizeVar": 17, "endSize": 22, "endSizeVar": 20, "startSpin": -1330, "startSpinVar": 85, "endSpin": -1184, "endSpinVar": 0, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 15, "y": 5}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -820}, "speed": 443, "speedVar": 171, "tangentialAccel": -78, "tangentialAccelVar": 0, "radialAccel": -194, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": false, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": {"__uuid__": "5aeee291-f220-49cf-a952-61318fa817a7", "__expectedType__": "cc.ParticleAsset"}, "_spriteFrame": null, "_totalParticles": 324, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_endColorVar": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_positionType": 1, "_id": "f0IImhqA5C0ZuG+xU86USR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "397jWgot5FcLImPP//yjk/"}, {"__type__": "cc.Node", "_name": "baseComponent<PERSON>ayer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}, {"__id__": 51}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63UYbvZSRAA6z8n0YZKm/K"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1624, "_alignMode": 2, "_lockFlags": 0, "_id": "cbxDmdIB5PWoAb4iwYJW5h"}, {"__type__": "c0533CQjaZH8qXgrwBdThV9", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_id": "88lgWMDVBL8IGYUOraprgL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "89GRgdzydKLpDjaJHGlaU0"}, {"__type__": "cc.Node", "_name": "popUp<PERSON>ayer", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ffEeD0Tu5L9qhOE1hqOLVN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "1a3SW3GvlALKooShyCD0j7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "33xms/4jlOFak0jblbfrB/"}, {"__type__": "cc.Node", "_name": "TipNode", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 56}, {"__id__": 59}, {"__id__": 64}], "_active": false, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 156, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ceHkAfy4lNdo/SAR++ZkNr"}, {"__type__": "cc.Node", "_name": "TIPS", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 57}, {"__id__": 58}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.1, "y": 1.1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3ddK5GH6JJRKtuS2MyGSJc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 418, "height": 308}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3cgvx5EMhBdpcFjf0KojfI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "496b7291-5c14-433c-9077-b6fd65604587@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cfb1+0Ex9JJKA089dkCTHL"}, {"__type__": "cc.Node", "_name": "TipTitle", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 51, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c4O7qodX9Py5DMuoAiEYt0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 274.91, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "64mzW/sYJAZKxvG5KLxmfN"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 109, "a": 255}, "_string": "You completed the goal", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "e5nyUf7udB85tU7//Rqx3m"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 25, "g": 141, "b": 86, "a": 255}, "_width": 3, "_id": "a1a4+I+i9BrbkmckbP9hv/"}, {"__type__": "a6acauXgFRHUoxf/mbnCyJX", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": null, "labelLen": 40, "i18nKey": "completedTips", "_id": "91SuvWr11A4agCWv6yjq0C"}, {"__type__": "cc.Node", "_name": "ContentTitle", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -57, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4fp/d9ebpOUrh7l+qaCVoo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 390, "height": 96.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a7Ssv+RxFMhJalfvNXG8mo"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "After token enters the end, teammate can help move", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 3, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "67I9kFrq1Lga03psXjD8IL"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 115, "b": 115, "a": 255}, "_width": 3, "_id": "2b920SykNGHIradg2AKnw9"}, {"__type__": "a6acauXgFRHUoxf/mbnCyJX", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "labelLen": 100, "i18nKey": "helpYourTeamate", "_id": "31N3F3muhDYpaBBbl9YQ1S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 2000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c2d7xqNQJAHKzSbKLbe9F5"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_id": "813Zy/JipLC5DhY0M40Zjw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "d1Vv9jroFPsLrfUFREvwRW"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 73}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 72}, "asset": {"__uuid__": "a761709e-f391-4ab6-bb0c-28cfdfa00213", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 74}}, {"__type__": "cc.PrefabInstance", "fileId": "755cuQQNpNYpDUqr0EsVI4", "mountedChildren": [{"__id__": 75}], "mountedComponents": [], "propertyOverrides": [{"__id__": 80}, {"__id__": 82}, {"__id__": 83}, {"__id__": 84}, {"__id__": 85}, {"__id__": 86}, {"__id__": 88}, {"__id__": 90}, {"__id__": 92}, {"__id__": 94}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 76}, "nodes": [{"__id__": 77}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 72}}, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -400, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8eVEDZZ0dOjIxDIuJ9i6RZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6aKF+mO31AAYP7x/CMF4NB"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "fep9g/htFNW7fgtMWOMOjG"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_name"], "value": "loadingLayer"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["versionLabel"], "value": {"__id__": 79}}, {"__type__": "cc.TargetInfo", "localID": ["a2tMECKGpMAZEfH9uXXZmY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 315, "height": 70}}, {"__type__": "cc.TargetInfo", "localID": ["f170+/jSBIg5O7VnOiKXgI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 91}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["5eDpyA1gVDA6ZN3NfXsCYp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -125.5, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["22jtyxxDRMALjP7UrNlnpJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -41.5, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["93aKnSNrpIro3DBVejQ42e"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42.5, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["97hXdif49NOJXpi9udWBMC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 126.5, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["9fyVRV1FlHJp4AhjYc/iP0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["25GAfL0BpCS7UZaD8q/0JP"]}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 103}, {"__id__": 104}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "98ajZqwaFCw7IKSSZ1ElQ+"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 78, "_fovAxis": 0, "_orthoHeight": 812, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": -325058561, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "21XhUYiUBOLphp7m95S2SX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "34ZLavT9ZEEps0Jcleq1Hf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1624}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "87UXaquF5NJrOGP2Nmx/gQ"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 103}, "_alignCanvasWithScreen": true, "_id": "2eKVyuDydOcor0gftMrt5R"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": -5.684341886080802e-14, "_right": -5.684341886080802e-14, "_top": -1.1368683772161603e-13, "_bottom": -1.1368683772161603e-13, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1624, "_alignMode": 2, "_lockFlags": 0, "_id": "36MpSxnn9A04CewRERQxhx"}, {"__type__": "390bfXiWShDxJEPZtUJ5cDC", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "gameContainer": {"__id__": 9}, "chessPrefab": {"__uuid__": "07be9097-32d7-457d-9c2e-bbadf701a4a0", "__expectedType__": "cc.Prefab"}, "playerPrefab": {"__uuid__": "8563d3fb-e855-4e7d-ae4a-670eea43b732", "__expectedType__": "cc.Prefab"}, "baseComponentLyer": {"__id__": 48}, "popUpLayer": {"__id__": 52}, "loadingLayer": {"__id__": 72}, "tipNode": {"__id__": 55}, "_id": "44TQdL9/pLa7aYS02Nm8Tu"}, {"__type__": "f1e10BLGT5F9J/jnUs40Ok3", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "gameBgSp": {"__id__": 6}, "chessboardBgSp": {"__id__": 16}, "chessboardBorderSp": {"__id__": 11}, "_id": "c1tF/UFedBh5+nwTPTpol9"}, {"__type__": "fd6ea0NRulMl5Ms5J7dWuX2", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "82PVO7/mZEsbQKRVlnfaJt"}, {"__type__": "722a6lTJCFAFbPlxIEEeAzP", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "f4mXAUWHBOBboBdYQN+BWX"}, {"__type__": "cc.PrefabInfo", "fileId": "21936b10-d44a-483b-89b9-c96e70b90a08", "targetOverrides": [{"__id__": 113}], "nestedPrefabInstanceRoots": [{"__id__": 72}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 72}, "sourceInfo": {"__id__": 114}, "propertyPath": ["chessNode"], "target": {"__id__": 72}, "targetInfo": {"__id__": 115}}, {"__type__": "cc.TargetInfo", "localID": ["a2tMECKGpMAZEfH9uXXZmY"]}, {"__type__": "cc.TargetInfo", "localID": ["5eDpyA1gVDA6ZN3NfXsCYp"]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 117}, "shadows": {"__id__": 118}, "_skybox": {"__id__": 119}, "fog": {"__id__": 120}, "octree": {"__id__": 121}}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 76}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}]