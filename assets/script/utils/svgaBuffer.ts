// @ts-ignore
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function e(e,s,a,f){return new(a=a||Promise)(function(r,t){function i(e){try{o(f.next(e))}catch(e){t(e)}}function n(e){try{o(f.throw(e))}catch(e){t(e)}}function o(e){var t;e.done?r(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(i,n)}o((f=f.apply(e,s||[])).next())})}function t(i,n){var o,s,a,f={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},e={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(r){return function(e){var t=[r,e];if(o)throw new TypeError("Generator is already executing.");for(;f;)try{if(o=1,s&&(a=2&t[0]?s.return:t[0]?s.throw||((a=s.return)&&a.call(s),0):s.next)&&!(a=a.call(s,t[1])).done)return a;switch(s=0,(t=a?[2&t[0],a.value]:t)[0]){case 0:case 1:a=t;break;case 4:return f.label++,{value:t[1],done:!1};case 5:f.label++,s=t[1],t=[0];continue;case 7:t=f.ops.pop(),f.trys.pop();continue;default:if(!((a=0<(a=f.trys).length&&a[a.length-1])||6!==t[0]&&2!==t[0])){f=0;continue}if(3===t[0]&&(!a||t[1]>a[0]&&t[1]<a[3]))f.label=t[1];else if(6===t[0]&&f.label<a[1])f.label=a[1],a=t;else{if(!(a&&f.label<a[2])){a[2]&&f.ops.pop(),f.trys.pop();continue}f.label=a[2],f.ops.push(t)}}t=n.call(i,f)}catch(e){t=[6,e],s=0}finally{o=a=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}}var r,i={exports:{}},n={exports:{}},s={build:"minimal"},o=a;function a(e,t){this.options=t,this.name=e,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}a.className="ReflectionObject",a.prototype.onAdd=function(e){this.parent&&this.parent!==e&&this.parent.remove(this),this.parent=e,this.resolved=!1;e=e.root;e instanceof r&&e._handleAdd(this)},a.prototype.resolve=function(){return this.resolved||this.root instanceof r&&(this.resolved=!0),this},a.prototype.getOption=function(e){if(this.options)return this.options[e]},a._configure=function(e){r=e};var f=l,h=o;function l(e,t,r,i,n){if(h.call(this,e,r),t&&"object"!=_typeof(t))throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=i,this.comments=n||{},this.reserved=void 0,t)for(var o=Object.keys(t),s=0;s<o.length;++s)"number"==typeof t[o[s]]&&(this.valuesById[this.values[o[s]]=t[o[s]]]=o[s])}((l.prototype=Object.create(h.prototype)).constructor=l).className="Enum",l.fromJSON=function(e,t){e=new l(e,t.values,t.options,t.comment,t.comments);return e.reserved=t.reserved,e};var u={},p={exports:{}},d={},c=y(y);function y(e){return"undefined"!=typeof Float32Array?(h=new Float32Array([-0]),l=new Uint8Array(h.buffer),u=128===l[3],e.writeFloatLE=u?c:y,e.writeFloatBE=u?y:c,e.readFloatLE=u?w:A,e.readFloatBE=u?A:w):(e.writeFloatLE=p.bind(null,v),e.writeFloatBE=p.bind(null,b),e.readFloatLE=d.bind(null,g),e.readFloatBE=d.bind(null,m)),"undefined"!=typeof Float64Array?(a=new Float64Array([-0]),f=new Uint8Array(a.buffer),u=128===f[7],e.writeDoubleLE=u?i:n,e.writeDoubleBE=u?n:i,e.readDoubleLE=u?o:s,e.readDoubleBE=u?s:o):(e.writeDoubleLE=t.bind(null,v,0,4),e.writeDoubleBE=t.bind(null,b,4,0),e.readDoubleLE=r.bind(null,g,0,4),e.readDoubleBE=r.bind(null,m,4,0)),e;function t(e,t,r,i,n,o){var s,a,f=i<0?1:0;0===(i=f?-i:i)?(e(0,n,o+t),e(0<1/i?0:2147483648,n,o+r)):isNaN(i)?(e(0,n,o+t),e(2146959360,n,o+r)):17976931348623157e292<i?(e(0,n,o+t),e((f<<31|2146435072)>>>0,n,o+r)):i<22250738585072014e-324?(e((s=i/5e-324)>>>0,n,o+t),e((f<<31|s/4294967296)>>>0,n,o+r)):(1024===(a=Math.floor(Math.log(i)/Math.LN2))&&(a=1023),e(4503599627370496*(s=i*Math.pow(2,-a))>>>0,n,o+t),e((f<<31|a+1023<<20|1048576*s&1048575)>>>0,n,o+r))}function r(e,t,r,i,n){t=e(i,n+t),e=e(i,n+r),i=2*(e>>31)+1,n=e>>>20&2047,r=4294967296*(1048575&e)+t;return 2047==n?r?NaN:1/0*i:0==n?5e-324*i*r:i*Math.pow(2,n-1075)*(r+4503599627370496)}function i(e,t,r){a[0]=e,t[r]=f[0],t[r+1]=f[1],t[r+2]=f[2],t[r+3]=f[3],t[r+4]=f[4],t[r+5]=f[5],t[r+6]=f[6],t[r+7]=f[7]}function n(e,t,r){a[0]=e,t[r]=f[7],t[r+1]=f[6],t[r+2]=f[5],t[r+3]=f[4],t[r+4]=f[3],t[r+5]=f[2],t[r+6]=f[1],t[r+7]=f[0]}function o(e,t){return f[0]=e[t],f[1]=e[t+1],f[2]=e[t+2],f[3]=e[t+3],f[4]=e[t+4],f[5]=e[t+5],f[6]=e[t+6],f[7]=e[t+7],a[0]}function s(e,t){return f[7]=e[t],f[6]=e[t+1],f[5]=e[t+2],f[4]=e[t+3],f[3]=e[t+4],f[2]=e[t+5],f[1]=e[t+6],f[0]=e[t+7],a[0]}var a,f,h,l,u;function p(e,t,r,i){var n=t<0?1:0;0===(t=n?-t:t)?e(0<1/t?0:2147483648,r,i):isNaN(t)?e(2143289344,r,i):e(34028234663852886e22<t?(n<<31|2139095040)>>>0:t<11754943508222875e-54?(n<<31|Math.round(t/1401298464324817e-60))>>>0:(n<<31|(e=Math.floor(Math.log(t)/Math.LN2))+127<<23|8388607&Math.round(t*Math.pow(2,-e)*8388608))>>>0,r,i)}function d(e,t,r){e=e(t,r),t=2*(e>>31)+1,r=e>>>23&255,e&=8388607;return 255==r?e?NaN:1/0*t:0==r?1401298464324817e-60*t*e:t*Math.pow(2,r-150)*(8388608+e)}function c(e,t,r){h[0]=e,t[r]=l[0],t[r+1]=l[1],t[r+2]=l[2],t[r+3]=l[3]}function y(e,t,r){h[0]=e,t[r]=l[3],t[r+1]=l[2],t[r+2]=l[1],t[r+3]=l[0]}function w(e,t){return l[0]=e[t],l[1]=e[t+1],l[2]=e[t+2],l[3]=e[t+3],h[0]}function A(e,t){return l[3]=e[t],l[2]=e[t+1],l[1]=e[t+2],l[0]=e[t+3],h[0]}}function v(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function b(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function g(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function m(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}var w={},A=(!function(){var e=w;e.length=function(e){for(var t,r=0,i=0;i<e.length;++i)(t=e.charCodeAt(i))<128?r+=1:t<2048?r+=2:55296==(64512&t)&&56320==(64512&e.charCodeAt(i+1))?(++i,r+=4):r+=3;return r},e.read=function(e,t,r){if(r-t<1)return"";for(var i,n=null,o=[],s=0;t<r;)(i=e[t++])<128?o[s++]=i:191<i&&i<224?o[s++]=(31&i)<<6|63&e[t++]:239<i&&i<365?(i=((7&i)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,o[s++]=55296+(i>>10),o[s++]=56320+(1023&i)):o[s++]=(15&i)<<12|(63&e[t++])<<6|63&e[t++],8191<s&&((n=n||[]).push(String.fromCharCode.apply(String,o)),s=0);return n?(s&&n.push(String.fromCharCode.apply(String,o.slice(0,s))),n.join("")):String.fromCharCode.apply(String,o.slice(0,s))},e.write=function(e,t,r){for(var i,n,o=r,s=0;s<e.length;++s)(i=e.charCodeAt(s))<128?t[r++]=i:(i<2048?t[r++]=i>>6|192:(55296==(64512&i)&&56320==(64512&(n=e.charCodeAt(s+1)))?(++s,t[r++]=(i=65536+((1023&i)<<10)+(1023&n))>>18|240,t[r++]=i>>12&63|128):t[r++]=i>>12|224,t[r++]=i>>6&63|128),t[r++]=63&i|128);return r-o}}(),function(){var e=d;e.float=c,e.utf8=w,e.emptyArray=Object.freeze?Object.freeze([]):[],e.emptyObject=Object.freeze?Object.freeze({}):{},e.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},e.isString=function(e){return"string"==typeof e||e instanceof String},e.isObject=function(e){return e&&"object"==_typeof(e)},e.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,e._configure=function(){var r=e.Buffer;r?(e._Buffer_from=r.from!==Uint8Array.from&&r.from||function(e,t){return new r(e,t)},e._Buffer_allocUnsafe=r.allocUnsafe||function(e){return new r(e)}):e._Buffer_from=e._Buffer_allocUnsafe=null}}(),k);function k(t,r){"string"==typeof t&&(r=t,t=void 0);var f=[];function h(e){if("string"!=typeof e){var t=l();if(k.verbose&&console.log("codegen: "+t),t="return "+t,e){for(var r=Object.keys(e),i=new Array(r.length+1),n=new Array(r.length),o=0;o<r.length;)i[o]=r[o],n[o]=e[r[o++]];return i[o]=t,Function.apply(null,i).apply(null,n)}return Function(t)()}for(var s=new Array(arguments.length-1),a=0;a<s.length;)s[a]=arguments[++a];if(a=0,e=e.replace(/%([%dfijs])/g,function(e,t){var r=s[a++];switch(t){case"d":case"f":return String(Number(r));case"i":return String(Math.floor(r));case"j":return JSON.stringify(r);case"s":return String(r)}return"%"}),a!==s.length)throw Error("parameter count mismatch");return f.push(e),h}function l(e){return"function "+(e||r||"")+"("+(t&&t.join(",")||"")+"){\n  "+f.join("\n  ")+"\n}"}return h.toString=l,h}k.verbose=!1;var E=p.exports=d,O=(E.codegen=A,E.toArray=function(e){if(e){for(var t=Object.keys(e),r=new Array(t.length),i=0;i<t.length;)r[i]=e[t[i++]];return r}return[]},/\\/g),S=/"/g,_=(E.isReserved=function(e){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(e)},E.safeProp=function(e){return!/^[$\w_]+$/.test(e)||E.isReserved(e)?'["'+e.replace(O,"\\\\").replace(S,'\\"')+'"]':"."+e},function(){var e=u,t=p.exports,n=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function r(e,t){var r=0,i={};for(t|=0;r<e.length;)i[n[r+t]]=e[r++];return i}e.basic=r([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]),e.defaults=r([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",t.emptyArray,null]),e.long=r([0,0,0,1,1],7),e.mapKey=r([0,0,0,5,5,0,0,0,1,1,0,2],2),e.packed=r([1,5,0,0,0,5,5,0,0,0,1,1,0])}(),L),x=o;((L.prototype=Object.create(x.prototype)).constructor=L).className="Field";var T,N=f,j=u,B=p.exports,I=/^required|optional|repeated$/;function L(e,t,r,i,n,o,s){if(B.isObject(i)?(s=n,o=i,i=n=void 0):B.isObject(n)&&(s=o,o=n,n=void 0),x.call(this,e,o),!B.isInteger(t)||t<0)throw TypeError("id must be a non-negative integer");if(!B.isString(r))throw TypeError("type must be a string");if(void 0!==i&&!I.test(i=i.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(void 0!==n&&!B.isString(n))throw TypeError("extend must be a string");this.rule=i&&"optional"!==i?i:void 0,this.type=r,this.id=t,this.extend=n||void 0,this.required="required"===i,this.optional=!this.required,this.repeated="repeated"===i,this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=!!B.Long&&void 0!==j.long[r],this.bytes="bytes"===r,this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=s}L.fromJSON=function(e,t){return new L(e,t.id,t.type,t.rule,t.extend,t.options,t.comment)},L.prototype.setOption=function(e,t,r){return"packed"===e&&(this._packed=null),x.prototype.setOption.call(this,e,t,r)},L.prototype.resolve=function(){var e;return this.resolved?this:(void 0===(this.typeDefault=j.defaults[this.type])&&(this.resolvedType=(this.declaringField||this).parent.lookupTypeOrEnum(this.type),this.resolvedType instanceof T?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]),this.options&&null!=this.options.default&&(this.typeDefault=this.options.default,this.resolvedType instanceof N)&&"string"==typeof this.typeDefault&&(this.typeDefault=this.resolvedType.values[this.typeDefault]),this.options&&(!0!==this.options.packed&&(void 0===this.options.packed||!this.resolvedType||this.resolvedType instanceof N)||delete this.options.packed,Object.keys(this.options).length||(this.options=void 0)),this.long?(this.typeDefault=B.Long.fromNumber(this.typeDefault,"u"===this.type.charAt(0)),Object.freeze&&Object.freeze(this.typeDefault)):this.bytes&&"string"==typeof this.typeDefault&&(B.base64.test(this.typeDefault)?B.base64.decode(this.typeDefault,e=B.newBuffer(B.base64.length(this.typeDefault)),0):B.utf8.write(this.typeDefault,e=B.newBuffer(B.utf8.length(this.typeDefault)),0),this.typeDefault=e),this.map?this.defaultValue=B.emptyObject:this.repeated?this.defaultValue=B.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof T&&(this.parent.ctor.prototype[this.name]=this.defaultValue),x.prototype.resolve.call(this))},L._configure=function(e){T=e};var R=P,U=o;((P.prototype=Object.create(U.prototype)).constructor=P).className="Namespace";var D,F,J,C=_,M=p.exports;function P(e,t){U.call(this,e,t),this.nested=void 0,this._nestedArray=null}P.fromJSON=function(e,t){return new P(e,t.options).addJSON(t.nested)},P.isReservedId=function(e,t){if(e)for(var r=0;r<e.length;++r)if("string"!=typeof e[r]&&e[r][0]<=t&&e[r][1]>=t)return!0;return!1},P.isReservedName=function(e,t){if(e)for(var r=0;r<e.length;++r)if(e[r]===t)return!0;return!1},Object.defineProperty(P.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=M.toArray(this.nested))}}),P.prototype.addJSON=function(e){if(e)for(var t,r=Object.keys(e),i=0;i<r.length;++i)t=e[r[i]],this.add((void 0!==t.fields?D:void 0!==t.values?J:void 0!==t.methods?F:void 0!==t.id?C:P).fromJSON(r[i],t));return this},P.prototype.get=function(e){return this.nested&&this.nested[e]||null},P.prototype.add=function(e){if(!(e instanceof C&&void 0!==e.extend||e instanceof D||e instanceof J||e instanceof F||e instanceof P))throw TypeError("object must be a valid nested object");if(this.nested){var t=this.get(e.name);if(t){if(!(t instanceof P&&e instanceof P)||t instanceof D||t instanceof F)throw Error("duplicate name '"+e.name+"' in "+this);for(var r=t.nestedArray,i=0;i<r.length;++i)e.add(r[i]);this.remove(t),this.nested||(this.nested={}),e.setOptions(t.options,!0)}}else this.nested={};return(this.nested[e.name]=e).onAdd(this),(t=this)._nestedArray=null,t},P.prototype.lookup=function(e,t,r){if("boolean"==typeof t?(r=t,t=void 0):t&&!Array.isArray(t)&&(t=[t]),M.isString(e)&&e.length){if("."===e)return this.root;e=e.split(".")}else if(!e.length)return this;if(""===e[0])return this.root.lookup(e.slice(1),t);var i=this.get(e[0]);if(i){if(1===e.length){if(!t||-1<t.indexOf(i.constructor))return i}else if(i instanceof P&&(i=i.lookup(e.slice(1),t,!0)))return i}else for(var n=0;n<this.nestedArray.length;++n)if(this._nestedArray[n]instanceof P&&(i=this._nestedArray[n].lookup(e,t,!0)))return i;return null===this.parent||r?null:this.parent.lookup(e,t)},P.prototype.lookupType=function(e){var t=this.lookup(e,[D]);if(t)return t;throw Error("no such type: "+e)},P.prototype.lookupTypeOrEnum=function(e){var t=this.lookup(e,[D,J]);if(t)return t;throw Error("no such Type or Enum '"+e+"' in "+this)},P._configure=function(e,t,r){D=e,F=t,J=r};var z=H,q=o,V=(((H.prototype=Object.create(q.prototype)).constructor=H).className="OneOf",_),G=p.exports;function H(e,t,r,i){if(Array.isArray(t)||(r=t,t=void 0),q.call(this,e,r),void 0!==t&&!Array.isArray(t))throw TypeError("fieldNames must be an Array");this.oneof=t||[],this.fieldsArray=[],this.comment=i}function $(e){if(e.parent)for(var t=0;t<e.fieldsArray.length;++t)e.fieldsArray[t].parent||e.parent.add(e.fieldsArray[t])}H.fromJSON=function(e,t){return new H(e,t.oneof,t.options,t.comment)},H.prototype.toJSON=function(e){e=!!e&&Boolean(e.keepComments);return G.toObject(["options",this.options,"oneof",this.oneof,"comment",e?this.comment:void 0])},H.prototype.add=function(e){if(e instanceof V)return e.parent&&e.parent!==this.parent&&e.parent.remove(e),this.oneof.push(e.name),this.fieldsArray.push(e),$(e.partOf=this),this;throw TypeError("field must be a Field")},H.prototype.remove=function(e){if(!(e instanceof V))throw TypeError("field must be a Field");var t=this.fieldsArray.indexOf(e);if(t<0)throw Error(e+" is not a member of "+this);return this.fieldsArray.splice(t,1),-1<(t=this.oneof.indexOf(e.name))&&this.oneof.splice(t,1),e.partOf=null,this},H.prototype.onAdd=function(e){q.prototype.onAdd.call(this,e);for(var t=0;t<this.oneof.length;++t){var r=e.get(this.oneof[t]);r&&!r.partOf&&(r.partOf=this).fieldsArray.push(r)}$(this)},H.prototype.onRemove=function(e){for(var t,r=0;r<this.fieldsArray.length;++r)(t=this.fieldsArray[r]).parent&&t.parent.remove(t);q.prototype.onRemove.call(this,e)},H.d=function(){for(var r=new Array(arguments.length),e=0;e<arguments.length;)r[e]=arguments[e++];return function(e,t){G.decorateType(e.constructor).add(new H(t,r)),Object.defineProperty(e,t,{get:G.oneOfGetter(r),set:G.oneOfSetter(r)})}};var K=ee,Z=R;((ee.prototype=Object.create(Z.prototype)).constructor=ee).className="Root";var W,X=_,Y=f,Q=z;function ee(e){Z.call(this,"",e),this.deferred=[],this.files=[]}ee.fromJSON=function(e,t){return t=t||new ee,e.options&&t.setOptions(e.options),t.addJSON(e.nested)};var te=/^[A-Z]/;function re(e,t){var r,i=t.parent.lookup(t.extend);return!!i&&(((r=new X(t.fullName,t.id,t.type,t.rule,void 0,t.options)).declaringField=t).extensionField=r,i.add(r),!0)}ee.prototype._handleAdd=function(e){if(e instanceof X)void 0===e.extend||e.extensionField||re(0,e)||this.deferred.push(e);else if(e instanceof Y)te.test(e.name)&&(e.parent[e.name]=e.values);else if(!(e instanceof Q)){if(e instanceof W)for(var t=0;t<this.deferred.length;)re(0,this.deferred[t])?this.deferred.splice(t,1):++t;for(var r=0;r<e.nestedArray.length;++r)this._handleAdd(e._nestedArray[r]);te.test(e.name)&&(e.parent[e.name]=e)}},ee._configure=function(e,t,r){W=e};var ie=oe,ne=_,se=(((oe.prototype=Object.create(ne.prototype)).constructor=oe).className="MapField",p.exports);function oe(e,t,r,i,n,o){if(ne.call(this,e,t,i,void 0,void 0,n,o),!se.isString(r))throw TypeError("keyType must be a string");this.keyType=r,this.resolvedKeyType=null,this.map=!0}oe.fromJSON=function(e,t){return new oe(e,t.id,t.keyType,t.type,t.options,t.comment)};var ae=he,fe=R;function he(e,t){fe.call(this,e,t),this.methods={},this._methodsArray=null}((he.prototype=Object.create(fe.prototype)).constructor=he).className="Service";var le=ce,ue=d,pe=(ue.LongBits,ue.utf8);function de(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function ce(e){this.buf=e,this.pos=0,this.len=e.length}var ye,ve="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new ce(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new ce(e);throw Error("illegal buffer")};function be(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}ce.create=ue.Buffer?function(e){return(ce.create=function(e){return ue.Buffer.isBuffer(e)?new(void 0)(e):ve(e)})(e)}:ve,ce.prototype._slice=ue.Array.prototype.subarray||ue.Array.prototype.slice,ce.prototype.uint32=(ye=4294967295,function(){if(ye=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128||(ye=(ye|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)||(ye=(ye|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)||(ye=(ye|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)||(ye=(ye|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)||!((this.pos+=5)>this.len))return ye;throw this.pos=this.len,de(this,10)}),ce.prototype.int32=function(){return 0|this.uint32()},ce.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)|0},ce.prototype.bool=function(){return 0!==this.uint32()},ce.prototype.fixed32=function(){if(this.pos+4>this.len)throw de(this,4);return be(this.buf,this.pos+=4)},ce.prototype.sfixed32=function(){if(this.pos+4>this.len)throw de(this,4);return 0|be(this.buf,this.pos+=4)},ce.prototype.float=function(){if(this.pos+4>this.len)throw de(this,4);var e=ue.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},ce.prototype.double=function(){if(this.pos+8>this.len)throw de(this,4);var e=ue.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},ce.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw de(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,r):t===r?new this.buf.constructor(0):this._slice.call(this.buf,t,r)},ce.prototype.string=function(){var e=this.bytes();return pe.read(e,0,e.length)},ce.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw de(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw de(this)}while(128&this.buf[this.pos++]);return this},ce.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this};var ge=function(e){var t=Ae.codegen(["r","l"],e.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(e.fieldsArray.filter(function(e){return e.map}).length?",k":""))("while(r.pos<c){")("var t=r.uint32()");e.group&&t("if((t&7)===4)")("break"),t("switch(t>>>3){");for(var r=0;r<e.fieldsArray.length;++r){var i=e._fieldsArray[r].resolve(),n=i.resolvedType instanceof me?"int32":i.type,o="m"+Ae.safeProp(i.name);t("case %i:",i.id),i.map?(t("r.skip().pos++")("if(%s===util.emptyObject)",o)("%s={}",o)("k=r.%s()",i.keyType)("r.pos++"),void 0!==we.long[i.keyType]?void 0===we.basic[n]?t('%s[typeof k==="object"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())',o,r):t('%s[typeof k==="object"?util.longToHash(k):k]=r.%s()',o,n):void 0===we.basic[n]?t("%s[k]=types[%i].decode(r,r.uint32())",o,r):t("%s[k]=r.%s()",o,n)):i.repeated?(t("if(!(%s&&%s.length))",o,o)("%s=[]",o),void 0!==we.packed[n]&&t("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",o,n)("}else"),void 0===we.basic[n]?t(i.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",o,r):t("%s.push(r.%s())",o,n)):void 0===we.basic[n]?t(i.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",o,r):t("%s=r.%s()",o,n),t("break")}for(t("default:")("r.skipType(t&7)")("break")("}")("}"),r=0;r<e._fieldsArray.length;++r){var s=e._fieldsArray[r];s.required&&t("if(!m.hasOwnProperty(%j))",s.name)("throw util.ProtocolError(%j,{instance:m})",ke(s))}return t("return m")},me=f,we=u,Ae=p.exports;function ke(e){return"missing required '"+e.name+"'"}var Ee=Ie,Oe=R,Se=(((Ie.prototype=Object.create(Oe.prototype)).constructor=Ie).className="Type",f),_e=_,xe=ie,Te=ae,Ne=le,je=p.exports,Be=ge;function Ie(e,t){Oe.call(this,e,t),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}Object.defineProperties(Ie.prototype,{fieldsById:{get:function(){if(!this._fieldsById){this._fieldsById={};for(var e=Object.keys(this.fields),t=0;t<e.length;++t){var r=this.fields[e[t]],i=r.id;if(this._fieldsById[i])throw Error("duplicate id "+i+" in "+this);this._fieldsById[i]=r}}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=je.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=je.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=Ie.generateConstructor(this)())},set:function(e){e.prototype,(e.$type=e.prototype.$type=this)._ctor=e;for(var t=0;t<this.fieldsArray.length;++t)this._fieldsArray[t].resolve();for(var r={},t=0;t<this.oneofsArray.length;++t)r[this._oneofsArray[t].resolve().name]={get:je.oneOfGetter(this._oneofsArray[t].oneof),set:je.oneOfSetter(this._oneofsArray[t].oneof)};t&&Object.defineProperties(e.prototype,r)}}}),Ie.generateConstructor=function(e){for(var t,r=je.codegen(["p"],e.name),i=0;i<e.fieldsArray.length;++i)(t=e._fieldsArray[i]).map?r("this%s={}",je.safeProp(t.name)):t.repeated&&r("this%s=[]",je.safeProp(t.name));return r("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")},Ie.fromJSON=function(e,t){var r=new Ie(e,t.options);r.extensions=t.extensions,r.reserved=t.reserved;for(var i=Object.keys(t.fields),n=0;n<i.length;++n)r.add((void 0!==t.fields[i[n]].keyType?xe:_e).fromJSON(i[n],t.fields[i[n]]));if(t.nested)for(i=Object.keys(t.nested),n=0;n<i.length;++n){var o=t.nested[i[n]];r.add((void 0!==o.id?_e:void 0!==o.fields?Ie:void 0!==o.values?Se:void 0!==o.methods?Te:Oe).fromJSON(i[n],o))}return t.extensions&&t.extensions.length&&(r.extensions=t.extensions),t.reserved&&t.reserved.length&&(r.reserved=t.reserved),t.group&&(r.group=!0),t.comment&&(r.comment=t.comment),r},Ie.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);if(e instanceof _e&&void 0===e.extend){if((this._fieldsById||this.fieldsById)[e.id])throw Error("duplicate id "+e.id+" in "+this);if(this.isReservedId(e.id))throw Error("id "+e.id+" is reserved in "+this);if(this.isReservedName(e.name))throw Error("name '"+e.name+"' is reserved in "+this);return e.parent&&e.parent.remove(e),(this.fields[e.name]=e).message=this,e.onAdd(this),(t=this)._fieldsById=t._fieldsArray=t._oneofsArray=null,delete t.encode,delete t.decode,delete t.verify,t}var t;return Oe.prototype.add.call(this,e)},Ie.prototype.isReservedId=function(e){return Oe.isReservedId(this.reserved,e)},Ie.prototype.isReservedName=function(e){return Oe.isReservedName(this.reserved,e)},Ie.prototype.setup=function(){this.fullName;for(var e=[],t=0;t<this.fieldsArray.length;++t)e.push(this._fieldsArray[t].resolve().resolvedType);return this.decode=Be(this)({Reader:Ne,types:e,util:je}),this},Ie.prototype.decode=function(e,t){return this.setup().decode(e,t)};var Le=n.exports=s,Re=(Le.build="light",Le.ReflectionObject=o,Le.Namespace=R,Le.Root=K,Le.Enum=f,Le.Type=Ee,Le.Field=_,Le.Service=ae,Le.ReflectionObject._configure(Le.Root),Le.Namespace._configure(Le.Type,Le.Service,Le.Enum),Le.Root._configure(Le.Type),Le.Field._configure(Le.Type),(i.exports=n.exports).build="full",i.exports),Ue=(new function(){var w=void 0,o=this;function e(e,t){var r,i=e.split("."),n=o;i[0]in n||!n.execScript||n.execScript("var "+i[0]);for(;i.length&&(r=i.shift());)i.length||t===w?n=n[r]||(n[r]={}):n[r]=t}var A="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array&&"undefined"!=typeof DataView;function k(e){for(var t,r,i,n,o,s,a,f,h,l=e.length,u=0,p=Number.POSITIVE_INFINITY,d=0;d<l;++d)e[d]>u&&(u=e[d]),e[d]<p&&(p=e[d]);for(t=1<<u,r=new(A?Uint32Array:Array)(t),i=1,n=0,o=2;i<=u;){for(d=0;d<l;++d)if(e[d]===i){for(a=n,f=s=0;f<i;++f)s=s<<1|1&a,a>>=1;for(h=i<<16|d,f=s;f<t;f+=o)r[f]=h;++n}++i,n<<=1,o<<=1}return[r,u,p]}function n(e,t){switch(this.g=[],this.h=32768,this.d=this.f=this.a=this.l=0,this.input=A?new Uint8Array(e):e,this.m=!1,this.i=O,this.r=!1,t?(t.index&&(this.a=t.index),t.bufferSize&&(this.h=t.bufferSize),t.bufferType&&(this.i=t.bufferType),t.resize&&(this.r=t.resize)):t={},this.i){case E:this.b=32768,this.c=new(A?Uint8Array:Array)(32768+this.h+258);break;case O:this.b=0,this.c=new(A?Uint8Array:Array)(this.h),this.e=this.z,this.n=this.v,this.j=this.w;break;default:throw Error("invalid inflate mode")}}var E=0,O=1,t=E,r=O;n.prototype.k=function(){for(;!this.m;){var e=T(this,3);switch(1&e&&(this.m=!0),e>>>=1){case 0:var t=this.input,r=this.a,i=this.c,n=this.b,o=t.length,s=w,a=i.length,f=w;if(this.d=this.f=0,o<=r+1)throw Error("invalid uncompressed block header: LEN");if(s=t[r++]|t[r++]<<8,o<=r+1)throw Error("invalid uncompressed block header: NLEN");if(s===~(t[r++]|t[r++]<<8))throw Error("invalid uncompressed block header: length verify");if(r+s>t.length)throw Error("input buffer is broken");switch(this.i){case E:for(;n+s>i.length;){if(s-=f=a-n,A)i.set(t.subarray(r,r+f),n),n+=f,r+=f;else for(;f--;)i[n++]=t[r++];this.b=n,i=this.e(),n=this.b}break;case O:for(;n+s>i.length;)i=this.e({p:2});break;default:throw Error("invalid inflate mode")}if(A)i.set(t.subarray(r,r+s),n),n+=s,r+=s;else for(;s--;)i[n++]=t[r++];this.a=r,this.b=n,this.c=i;break;case 1:this.j(x,S);break;case 2:for(var h,l,u,p,o=T(this,5)+257,d=T(this,5)+1,c=T(this,4)+4,y=new(A?Uint8Array:Array)(_.length),v=w,b=w,g=w,m=w,m=0;m<c;++m)y[_[m]]=T(this,3);if(!A)for(m=c,c=y.length;m<c;++m)y[_[m]]=0;for(h=k(y),v=new(A?Uint8Array:Array)(o+d),m=0,u=o+d;m<u;)switch(p=j(this,h)){case 16:for(g=3+T(this,2);g--;)v[m++]=b;break;case 17:for(g=3+T(this,3);g--;)v[m++]=0;b=0;break;case 18:for(g=11+T(this,7);g--;)v[m++]=0;b=0;break;default:b=v[m++]=p}d=k(A?v.subarray(0,o):v.slice(0,o)),l=k(A?v.subarray(o):v.slice(o)),this.j(d,l);break;default:throw Error("unknown BTYPE: "+e)}}return this.n()};for(var i=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],_=A?new Uint16Array(i):i,i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],h=A?new Uint16Array(i):i,i=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],l=A?new Uint8Array(i):i,i=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],u=A?new Uint16Array(i):i,i=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],p=A?new Uint8Array(i):i,s=new(A?Uint8Array:Array)(288),a=0,f=s.length;a<f;++a)s[a]=a<=143?8:a<=255?9:a<=279?7:8;for(var x=k(s),d=new(A?Uint8Array:Array)(30),c=0,y=d.length;c<y;++c)d[c]=5;var S=k(d);function T(e,t){for(var r,i=e.f,n=e.d,o=e.input,s=e.a,a=o.length;n<t;){if(a<=s)throw Error("input buffer is broken");i|=o[s++]<<n,n+=8}return r=i&(1<<t)-1,e.f=i>>>t,e.d=n-t,e.a=s,r}function j(e,t){for(var r=e.f,i=e.d,n=e.input,o=e.a,s=n.length,a=t[0],f=t[1];i<f&&!(s<=o);)r|=n[o++]<<i,i+=8;if((a=(t=a[r&(1<<f)-1])>>>16)>i)throw Error("invalid code length: "+a);return e.f=r>>a,e.d=i-a,e.a=o,65535&t}function v(e,t){var r,i;if(this.input=e,this.a=0,t?(t.index&&(this.a=t.index),t.verify&&(this.A=t.verify)):t={},r=e[this.a++],i=e[this.a++],(15&r)!==b)throw Error("unsupported compression method");if(this.method=b,0!=((r<<8)+i)%31)throw Error("invalid fcheck flag:"+((r<<8)+i)%31);if(32&i)throw Error("fdict flag is not supported");this.q=new n(e,{index:this.a,bufferSize:t.bufferSize,bufferType:t.bufferType,resize:t.resize})}n.prototype.j=function(e,t){var r=this.c,i=this.b;this.o=e;for(var n,o,s,a,f=r.length-258;256!==(n=j(this,e));)if(n<256)f<=i&&(this.b=i,r=this.e(),i=this.b),r[i++]=n;else for(a=h[o=n-257],0<l[o]&&(a+=T(this,l[o])),n=j(this,t),s=u[n],0<p[n]&&(s+=T(this,p[n])),f<=i&&(this.b=i,r=this.e(),i=this.b);a--;)r[i]=r[i++-s];for(;8<=this.d;)this.d-=8,this.a--;this.b=i},n.prototype.w=function(e,t){var r=this.c,i=this.b;this.o=e;for(var n,o,s,a,f=r.length;256!==(n=j(this,e));)if(n<256)f<=i&&(f=(r=this.e()).length),r[i++]=n;else for(a=h[o=n-257],0<l[o]&&(a+=T(this,l[o])),n=j(this,t),s=u[n],0<p[n]&&(s+=T(this,p[n])),f<i+a&&(f=(r=this.e()).length);a--;)r[i]=r[i++-s];for(;8<=this.d;)this.d-=8,this.a--;this.b=i},n.prototype.e=function(){var e,t,r=new(A?Uint8Array:Array)(this.b-32768),i=this.b-32768,n=this.c;if(A)r.set(n.subarray(32768,r.length));else for(e=0,t=r.length;e<t;++e)r[e]=n[e+32768];if(this.g.push(r),this.l+=r.length,A)n.set(n.subarray(i,32768+i));else for(e=0;e<32768;++e)n[e]=n[i+e];return this.b=32768,n},n.prototype.z=function(e){var t,r=this.input.length/this.a+1|0,i=this.input,n=this.c;return e&&("number"==typeof e.p&&(r=e.p),"number"==typeof e.u)&&(r+=e.u),i=r<2?(e=(i.length-this.a)/this.o[2]/2*258|0)<n.length?n.length+e:n.length<<1:n.length*r,A?(t=new Uint8Array(i)).set(n):t=n,this.c=t},n.prototype.n=function(){var e,t,r,i,n,o=0,s=this.c,a=this.g,f=new(A?Uint8Array:Array)(this.l+(this.b-32768));if(0===a.length)return A?this.c.subarray(32768,this.b):this.c.slice(32768,this.b);for(t=0,r=a.length;t<r;++t)for(i=0,n=(e=a[t]).length;i<n;++i)f[o++]=e[i];for(t=32768,r=this.b;t<r;++t)f[o++]=s[t];return this.g=[],this.buffer=f},n.prototype.v=function(){var e,t=this.b;return A?this.r?(e=new Uint8Array(t)).set(this.c.subarray(0,t)):e=this.c.subarray(0,t):(this.c.length>t&&(this.c.length=t),e=this.c),this.buffer=e},v.prototype.k=function(){var e=this.input,t=this.q.k();if(this.a=this.q.a,this.A){var e=(e[this.a++]<<24|e[this.a++]<<16|e[this.a++]<<8|e[this.a++])>>>0,r=t;if("string"==typeof r){for(var i=r.split(""),n=0,o=i.length;n<o;n++)i[n]=(255&i[n].charCodeAt(0))>>>0;r=i}for(var s,a=1,f=0,h=r.length,l=0;0<h;){for(h-=s=1024<h?1024:h;f+=a+=r[l++],--s;);a%=65521,f%=65521}if(e!=(f<<16|a)>>>0)throw Error("invalid adler-32 checksum")}return t};var b=8;e("Zlib.Inflate",v),e("Zlib.Inflate.prototype.decompress",v.prototype.k);var g,m,B,L,N={ADAPTIVE:r,BLOCK:t};if(Object.keys)g=Object.keys(N);else for(m in g=[],B=0,N)g[B++]=m;for(B=0,L=g.length;B<L;++B)e("Zlib.Inflate.BufferType."+(m=g[B]),N[m])}).Zlib;function De(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t}var Fe=Re.Root.fromJSON({nested:{com:{nested:{opensource:{nested:{svga:{options:{objc_class_prefix:"SVGAProto",java_package:"com.opensource.svgaplayer"},nested:{MovieParams:{fields:{viewBoxWidth:{type:"float",id:1},viewBoxHeight:{type:"float",id:2},fps:{type:"int32",id:3},frames:{type:"int32",id:4}}},SpriteEntity:{fields:{imageKey:{type:"string",id:1},frames:{rule:"repeated",type:"FrameEntity",id:2}}},Layout:{fields:{x:{type:"float",id:1},y:{type:"float",id:2},width:{type:"float",id:3},height:{type:"float",id:4}}},Transform:{fields:{a:{type:"float",id:1},b:{type:"float",id:2},c:{type:"float",id:3},d:{type:"float",id:4},tx:{type:"float",id:5},ty:{type:"float",id:6}}},ShapeEntity:{oneofs:{args:{oneof:["shape","rect","ellipse"]}},fields:{type:{type:"ShapeType",id:1},shape:{type:"ShapeArgs",id:2},rect:{type:"RectArgs",id:3},ellipse:{type:"EllipseArgs",id:4},styles:{type:"ShapeStyle",id:10},transform:{type:"Transform",id:11}},nested:{ShapeType:{values:{SHAPE:0,RECT:1,ELLIPSE:2,KEEP:3}},ShapeArgs:{fields:{d:{type:"string",id:1}}},RectArgs:{fields:{x:{type:"float",id:1},y:{type:"float",id:2},width:{type:"float",id:3},height:{type:"float",id:4},cornerRadius:{type:"float",id:5}}},EllipseArgs:{fields:{x:{type:"float",id:1},y:{type:"float",id:2},radiusX:{type:"float",id:3},radiusY:{type:"float",id:4}}},ShapeStyle:{fields:{fill:{type:"RGBAColor",id:1},stroke:{type:"RGBAColor",id:2},strokeWidth:{type:"float",id:3},lineCap:{type:"LineCap",id:4},lineJoin:{type:"LineJoin",id:5},miterLimit:{type:"float",id:6},lineDashI:{type:"float",id:7},lineDashII:{type:"float",id:8},lineDashIII:{type:"float",id:9}},nested:{RGBAColor:{fields:{r:{type:"float",id:1},g:{type:"float",id:2},b:{type:"float",id:3},a:{type:"float",id:4}}},LineCap:{values:{LineCap_BUTT:0,LineCap_ROUND:1,LineCap_SQUARE:2}},LineJoin:{values:{LineJoin_MITER:0,LineJoin_ROUND:1,LineJoin_BEVEL:2}}}}}},FrameEntity:{fields:{alpha:{type:"float",id:1},layout:{type:"Layout",id:2},transform:{type:"Transform",id:3},clipPath:{type:"string",id:4},shapes:{rule:"repeated",type:"ShapeEntity",id:5}}},MovieEntity:{fields:{version:{type:"string",id:1},params:{type:"MovieParams",id:2},images:{keyType:"string",type:"bytes",id:3},sprites:{rule:"repeated",type:"SpriteEntity",id:4}}}}}}}}}}}).lookupType("com.opensource.svga.MovieEntity");function Je(i){return e(this,void 0,void 0,function(){return t(this,function(e){switch(e.label){case 0:return[4,new Promise(function(e,t){var r=new XMLHttpRequest;r.open("GET",i,!0),r.responseType="arraybuffer",r.onloadend=function(){void 0===r.response||200!==r.status&&304!==r.status?t(new Error("XMLHttpRequest, "+r.statusText)):e(r.response)},r.send()})];case 1:return[2,e.sent()]}})})}var Ce=function(h){return e(void 0,void 0,void 0,function(){var r,i,n,o,s,a,f;return t(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,Je(h)];case 1:if(i=e.sent(),2!==function(e){e=new Uint8Array(e,0,4);return 80===e[0]&&75===e[1]&&3===e[2]&&4===e[3]?1:2}(new Uint8Array(i,0,4)))throw new Error("this parser only support version@2 of SVGA.");for(o in i=new Ue.Inflate(new Uint8Array(i)).decompress(),r=Fe.decode(i),i=r.params,a=i.viewBoxWidth,f=i.viewBoxHeight,i=i.fps,n=[],r.images)s=r.images[o],s=De(s),n.push("data:image/png;base64,"+btoa(s));return[2,{viewBoxWidth:a,viewBoxHeight:f,fps:i,frames:n}];case 2:throw a=e.sent(),f=a.toString(),a instanceof Error&&(f=a.message),new Error(f);case 3:return[2]}})})};export{Ce as parseSvgaFormat};
