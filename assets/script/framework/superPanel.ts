/**
 * @content 弹窗基类
 * <AUTHOR>
 */

import { BlockInputEvents, _decorator } from "cc";
import { BasePanel } from "./basePanel";
import { PanelAnim } from "./panelAnim";

const { ccclass, property } = _decorator;

@ccclass("SuperPanel")
export default class SuperPanel extends BasePanel {
    protected myData: any;
    /** 动画脚本 */
    protected panelAnim: PanelAnim = null;
    /** 是否添加穿透控制 默认添加 */
    protected isAddBlockInputEvent: boolean = true;

    init(data: any) {
        this.myData = data;
    }

    /** 添加事件 */
    protected onMessageEvent() {
        super.onMessageEvent();
        if (this.view["closeBtn"]) this.view["closeBtn"].on("click", this.clickClosePage, this);
    };

    /** 关闭事件 */
    protected offMessageEvent() {
        super.offMessageEvent();
        if (this.view["closeBtn"]) this.view["closeBtn"].off("click", this.clickClosePage, this);
    };

    protected onEnable(): void {
        super.onEnable();
        this.panelAnim = new PanelAnim(this);
        this.panelInit();
        this.adapt();
        this.panelAnim.playPanelAnim(true);
    }

    protected onDisable(): void {
        super.onDisable();
    }

    protected panelInit() {
        this.reserve();
        this.addBlockInputEvents();
    }

    /** 添加穿透事件 注意：node 是否设置尺寸大小  */
    protected addBlockInputEvents() {
        const blockInputEvents = this.node.getComponent(BlockInputEvents);
        if (!this.isAddBlockInputEvent) {
            if (blockInputEvents) blockInputEvents.enabled = false;
            return;
        }
        if (blockInputEvents) {
            blockInputEvents.enabled = true;
            return
        }
        this.node.addComponent(BlockInputEvents);
    }

    /**
     * 关闭界面
     * 预留接口进行一些事情的处理
     */
    clickClosePage() {
        this.closePage();
    }

    /** 一般不直接进行调用closePage */
    protected closePage() {
        if (this.panelAnim) this.panelAnim.playPanelAnim(false);

    }

    /** 界面适配 */
    protected adapt() { }

    /** 预留接口，以及是否播放动画等等， 可以设置是否可以点击穿透 */
    protected reserve() { }

    protected openAnimFunc() { }

    protected closeAnimFunc() {
        if (this.node.parent) {
            this.node.removeFromParent();
            this.node.parent = null;
        }
    };

}
