import { _decorator, Component, Node, tween, v3, Vec3, Label } from 'cc';
import { Utils } from '../framework/frameworkUtils';
import GameConfig from '../gameConfig';
import { commonUtil } from '../utils/commonUtil';
const { ccclass, property } = _decorator;

@ccclass('loadingMgr')
export class loadingMgr extends Component {
    @property(Node)
    chessNode: Node = null

    @property(Label)
    versionLabel: Label = null

    tweenQuaee: any[]

    onEnable() {
        this.playLoadingAnimation()
        // this.versionLabel.string = GameConfig.version
    }

    onDisable() {
        this.tweenQuaee.forEach(targetTween => {
            targetTween?.stop?.()
        })
    }

    playLoadingAnimation() {
        this.tweenQuaee = []
        this.chessNode.children.forEach(async(node: Node, index: number) => {
            await Utils.timeOut1(500 * index)
            this.playAnimation(node)
            if (index + 1 === this.chessNode.children.length) {
                await Utils.timeOut1(1000)
                this.replayLoadingAnimation()
            }
        })
    }

    replayLoadingAnimation() {
        this.chessNode.children.forEach((node: Node) => {
            commonUtil.setOpacity(node, 0)
        })
        this.playLoadingAnimation()
    }

    playAnimation(node: Node) {
        if (!node.isValid) return
        const { x } = node.getPosition()
        this.tweenQuaee.push(
            tween(node)
            .to(.3, { position: v3(x, 100, 0), scale: commonUtil.getEqualVec3(1.2) })
            .to(.3, { position: v3(x, 0, 0), scale: Vec3.ONE })
            .start()
        )
        this.tweenQuaee.push(
            tween(commonUtil.getUIOpacity(node))
            .to(.3, { opacity: 255 })
            .start()
        )
    }
}
