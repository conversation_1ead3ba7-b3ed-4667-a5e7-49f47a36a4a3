import { Label, Sprite, <PERSON>prite<PERSON><PERSON>e, Vec3, _decorator } from "cc";
import { BasePanel } from "../framework/basePanel";
import { EventMgr } from "../framework/eventMgr";
import { EventName } from "../framework/eventName";
import { Utils } from "../framework/frameworkUtils";
import { I18n } from "../framework/i18n";
import { Lab } from "../framework/lab";
import { TopAreaMgr } from "../manager/topAreaMgr";
import { commonUtil } from "../utils/commonUtil";
import { loadRemoteImg } from "../utils/utils";

const { ccclass, property } = _decorator;

@ccclass("TopItem")
export default class TopItem extends BasePanel {
    @property
    itemId: number = -1;

    private everyData: any = null;

    init(data?: any): void {
        super.init(data);
    }

    protected onMessageEvent(): void {
        super.onMessageEvent();
        EventMgr.on(EventName.EVENT_TOP_ITEM_UPDATE, this, this.updateShow);
    }

    protected offMessageEvent(): void {
        super.offMessageEvent();
        EventMgr.on(EventName.EVENT_TOP_ITEM_UPDATE, this, this.updateShow);
    }

    protected onEnable(): void {
        super.onEnable();
        this.preHandleShow();
        this.updateShow();
    }

    private preHandleShow() {
        // let multiple: string = '0';
        // if (I18n.languageState == 1) {
        //     multiple = Lab.x_1 + multiple;
        // } else if (I18n.languageState == 2) {
        //     multiple = multiple + Lab.x_1;
        // }
        // this.tools?.text("multipleNum", multiple);
    }

    getCoinWorldPos() {
        return commonUtil.convertToNodeSpaceAR(this.view["otherCoin"], Vec3.ZERO);
    }

    getItemIdx() {
        return this.itemId;
    }

    isSelf(id: string) {
        if (!this.everyData) {
            return false;
        }
        if (this.everyData?.userId == id) return true;
        return false;
    }

    async updateShow() {
        let data = TopAreaMgr.instance.getUserCoinInfoByIdx(this.itemId);
        if (this.everyData == data) return;
        if (data) this.everyData = data;
        if (!data) return;
        const antes = data?.antes ?? 0;
        let coin = data?.coin ?? 0;

        const eatChessNum = data?.eatChessNum ?? 0;
        let multiple = data?.multiple ?? 0;
        if (I18n.languageState == 1) {
            multiple = Lab.x_1 + multiple;
        } else if (I18n.languageState == 2) {
            multiple = multiple + Lab.x_1;
        }
        const portrait = data?.portrait ?? ""

        coin = Utils.bigNumberTransform(coin);
        this.tools?.text("bottomNoteNum", antes);
        this.tools?.text("multipleNum", multiple);
        this.tools?.text("eatNum", eatChessNum);
        this.tools?.text("otherCoinNum", coin);
        this.view["avator"].setScale(Vec3.ONE);

        let spHas = TopAreaMgr.instance.spMap.get(portrait);
        if (spHas) {
            this.setAvator(spHas, portrait, data);
        } else {
            loadRemoteImg(portrait).then((sp) => {
                this.setAvator(sp, portrait, data);
            })
        }
        // console.info("加载完顶部得分面板Item（共有4个），" + new Date().getTime());
    }

    setAvator(sp, portrait, data) {
        if (this.view?.["avator"]) {
            if (sp) {
                this.tools.adaptImg("avator", sp)
                // commonUtil.setSpriteFrameAndAdapteHeight(this.view["avator"].getComponent(Sprite), sp)
                TopAreaMgr.instance.spMap.set(portrait, sp);
            }
            else this.tools?.loadImgResAndAdapt('image/defaultImg/spriteFrame', "avator")
        }
        this.tools?.visible("item_gameOver_teamA", data?.teamName == "A")
        this.tools?.visible("item_gameOver_teamB", data?.teamName == "B")
    }

    setLabActive(bool: boolean) {
        this.tools?.visible("multipleNum", bool);
        this.tools?.visible("eatNum", bool);
        this.tools?.visible("otherCoinNum", bool);
    }

    setMultipleLabActive(bool: boolean) {
        this.tools?.visible("multipleNum", bool);
    }

    setEatLabActive(bool: boolean) {
        this.tools?.visible("eatNum", bool);
    }

    setCoinLabActive(bool: boolean) {
        this.tools?.visible("otherCoinNum", bool);
    }

    /**
     * 获取位置和fontsize
     */
    getPosAndFontSize() {
        const coinIconPos = commonUtil.convertToWorldSpaceARToZero(this.view["otherCoin"]) ?? null;
        const multiplePos = commonUtil.convertToWorldSpaceARToZero(this.view["multipleNum"]) ?? null;
        const multipleFontSize = this.view["multipleNum"]?.getComponent(Label).fontSize ?? 0;
        const eatPos = commonUtil.convertToWorldSpaceARToZero(this.view["eatNum"]) ?? null;
        const eatFontSize = this.view["eatNum"]?.getComponent(Label).fontSize ?? 0;
        const coinPos = commonUtil.convertToWorldSpaceARToZero(this.view["otherCoinNum"]) ?? null;
        const coinFontSize = this.view["otherCoinNum"]?.getComponent(Label).fontSize ?? 0;
        return { coinIconPos, multiplePos, multipleFontSize, eatPos, eatFontSize, coinPos, coinFontSize }
    }
}
