import { _decorator, Component, director } from 'cc';
import { GAME_STAGE } from '../network/constants';
import globalData from '../globalData';
import ViewManager from './view';
import { gameLocalServer } from './gameLocalServer';
import GameConfig from '../gameConfig';
import { PlayerOpStatus } from '../type/index';

const { ccclass } = _decorator;

/**
 * 本地游戏管理器 - 专门处理离线单机游戏
 */
@ccclass('LocalGameMgr')
export class LocalGameMgr extends Component {
    private static _instance: LocalGameMgr = null;
    
    public static get instance(): LocalGameMgr {
        return LocalGameMgr._instance;
    }
    
    // 本地游戏配置
    private _localConfig = GameConfig.LOCAL_GAME_CONFIG;
    private _currentPlayerIndex: number = 1;
    private _gameTimer: number = null;
    private _aiThinkTime: number = 2000; // AI思考时间(毫秒)
    
    onLoad() {
        LocalGameMgr._instance = this;
        console.log('🎮 本地游戏管理器已初始化');
    }
    
    onDestroy() {
        this.stopLocalGame();
        LocalGameMgr._instance = null;
    }
    
    /**
     * 启动本地游戏
     */
    public startLocalGame(): void {
        console.log('🚀 启动本地游戏模式');
        
        // 1. 初始化游戏配置
        this.initLocalGameConfig();
        
        // 2. 创建本地玩家
        this.createLocalPlayers();
        
        // 3. 初始化棋子
        this.initLocalChess();
        
        // 4. 设置游戏状态
        this.setGameState();
        
        // 5. 启动游戏界面
        this.initGameUI();
        
        // 6. 开始第一轮
        this.scheduleOnce(() => {
            this.startFirstRound();
        }, 1);
    }
    
    /**
     * 初始化本地游戏配置
     */
    private initLocalGameConfig(): void {
        // 设置全局游戏信息
        globalData.gameInfo.gameMode = this._localConfig.gameMode;
        globalData.gameInfo.endMode = this._localConfig.endMode;
        globalData.gameInfo.gameStatus = GAME_STAGE.GAMEING;
        
        // 设置本地服务器
        const localServer = gameLocalServer.instance;
        localServer.gameMode = this._localConfig.gameMode;
        localServer.opIndex = 1;
        localServer.selfOpIdx = 1;
        localServer.obstacleInfo = [];
        localServer.diceNumArray = [];
        
        console.log('✅ 本地游戏配置已初始化');
    }
    
    /**
     * 创建本地玩家
     */
    private createLocalPlayers(): void {
        const playerList = [
            {
                userId: "local_player_1",
                name: "玩家1",
                portrait: "https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=P1",
                userIndex: 1,
                isAuto: false, // 人类玩家
                portraitMask: "",
                status: 1
            },
            {
                userId: "local_player_2", 
                name: "AI玩家2",
                portrait: "https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=AI2",
                userIndex: 2,
                isAuto: true, // AI玩家
                portraitMask: "",
                status: 2
            },
            {
                userId: "local_player_3",
                name: "AI玩家3", 
                portrait: "https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=AI3",
                userIndex: 3,
                isAuto: true, // AI玩家
                portraitMask: "",
                status: 3
            },
            {
                userId: "local_player_4",
                name: "AI玩家4",
                portrait: "https://via.placeholder.com/100x100/F7DC6F/FFFFFF?text=AI4", 
                userIndex: 4,
                isAuto: true, // AI玩家
                portraitMask: "",
                status: 5
            }
        ];
        
        // 设置玩家信息
        globalData.gameInfo.playerInfo = playerList;
        globalData.myUserId = "local_player_1";
        globalData.currentPlayerIndex = 1;
        globalData.currentOpUserId = "local_player_1";
        globalData.isMineInGame = true;
        
        console.log('✅ 本地玩家已创建:', playerList.length, '个玩家');
    }
    
    /**
     * 初始化棋子位置
     */
    private initLocalChess(): void {
        const chessData = [];
        
        // 为每个玩家创建4个棋子，初始位置都在起始点(0)
        for (let player = 1; player <= 4; player++) {
            for (let chess = 1; chess <= 4; chess++) {
                chessData.push({
                    chessId: `${player}-${chess}`,
                    chessIndex: 0 // 0表示在起始位置
                });
            }
        }
        
        // 设置棋子信息
        globalData.gameInfo.chessInfo = chessData;
        gameLocalServer.instance.setUseAllChessInfo(chessData);
        
        console.log('✅ 棋子位置已初始化:', chessData.length, '个棋子');
    }
    
    /**
     * 设置游戏状态
     */
    private setGameState(): void {
        globalData.gameInfo.gameStatus = GAME_STAGE.GAMEING;
        globalData.canTouchDice = true;
        globalData.noticeDice = false;
        globalData.gameHosting = false;
        
        console.log('✅ 游戏状态已设置');
    }
    
    /**
     * 初始化游戏界面
     */
    private initGameUI(): void {
        const playerInfo = globalData.gameInfo.playerInfo;
        const chessInfo = globalData.gameInfo.chessInfo;
        const endMode = globalData.gameInfo.endMode;
        const obstacleInfo = gameLocalServer.instance.obstacleInfo;
        
        // 初始化UI
        ViewManager.initUiInfo(
            globalData.currentPlayerIndex,
            playerInfo,
            chessInfo,
            endMode,
            obstacleInfo
        );
        
        console.log('✅ 游戏界面已初始化');
    }
    
    /**
     * 开始第一轮游戏
     */
    private startFirstRound(): void {
        console.log('🎲 开始第一轮游戏');
        this.nextPlayerTurn();
    }
    
    /**
     * 下一个玩家回合
     */
    public nextPlayerTurn(): void {
        const currentPlayer = globalData.gameInfo.playerInfo.find(
            (p: any) => p.userIndex === this._currentPlayerIndex
        );
        
        if (!currentPlayer) {
            console.error('❌ 找不到当前玩家');
            return;
        }
        
        // 更新当前操作玩家
        globalData.currentOpUserId = currentPlayer.userId;
        globalData.currentPlayerIndex = currentPlayer.userIndex;
        gameLocalServer.instance.opIndex = currentPlayer.userIndex;
        
        console.log(`🎯 轮到玩家 ${currentPlayer.name} (${currentPlayer.userIndex})`);
        
        // 如果是AI玩家，自动执行
        if (currentPlayer.isAuto) {
            this.scheduleOnce(() => {
                this.executeAITurn(currentPlayer);
            }, this._aiThinkTime / 1000);
        } else {
            // 人类玩家，等待操作
            this.waitForPlayerAction();
        }
    }
    
    /**
     * 等待玩家操作
     */
    private waitForPlayerAction(): void {
        console.log('⏳ 等待玩家操作...');

        // 确保当前玩家是"我"（通过设置myUserId来控制isMyRound）
        globalData.myUserId = globalData.currentOpUserId;

        // 设置玩家可以摇骰子
        globalData.canTouchDice = true;
        globalData.noticeDice = true;
        globalData.playerOpStatus = PlayerOpStatus.Other;

        console.log('🔧 状态设置:', {
            canTouchDice: globalData.canTouchDice,
            noticeDice: globalData.noticeDice,
            isMyRound: globalData.isMyRound,
            currentOpUserId: globalData.currentOpUserId,
            myUserId: globalData.myUserId
        });

        // 显示骰子提示
        ViewManager.notifyPlayerOpDice(
            globalData.currentOpUserId,
            globalData.currentPlayerIndex,
            30000, // 倒计时30秒(毫秒)
            [] // 骰子数组
        );

        // 监听骰子点击事件
        this.setupDiceClickListener();
    }

    /**
     * 设置骰子点击监听
     */
    private setupDiceClickListener(): void {
        // 监听骰子点击事件
        director.on('dice-clicked', this.onDiceClicked, this);
    }

    /**
     * 骰子点击处理
     */
    private onDiceClicked(): void {
        console.log('🎲 玩家点击了骰子');
        console.log('🔍 状态检查:', {
            canTouchDice: globalData.canTouchDice,
            isMyRound: globalData.isMyRound,
            currentOpUserId: globalData.currentOpUserId,
            myUserId: globalData.myUserId,
            noticeDice: globalData.noticeDice
        });

        if (!globalData.canTouchDice || !globalData.isMyRound) {
            console.log('❌ 当前不能摇骰子');
            return;
        }

        // 禁用骰子点击
        globalData.canTouchDice = false;
        globalData.noticeDice = false;

        // 摇骰子
        this.rollDiceForPlayer();
    }

    /**
     * 玩家摇骰子
     */
    private rollDiceForPlayer(): void {
        const diceResult = Math.floor(Math.random() * 6) + 1;
        gameLocalServer.instance.diceNumArray = [diceResult];

        console.log(`🎲 玩家摇出骰子: ${diceResult}`);

        // 显示骰子结果
        ViewManager.notifyDiceResult(
            globalData.currentOpUserId,
            globalData.currentPlayerIndex,
            diceResult,
            [diceResult]
        ).then(() => {
            // 骰子动画完成后，显示可移动棋子
            this.scheduleOnce(() => {
                this.showAvailableChesses(diceResult);
            }, 0.5);
        });
    }

    /**
     * 显示可移动的棋子
     */
    private showAvailableChesses(diceResult: number): void {
        const currentPlayerIndex = globalData.currentPlayerIndex;
        const availableChesses = this.getAvailableChessesForPlayer(currentPlayerIndex, diceResult);

        if (availableChesses.length === 0) {
            console.log(`🚫 玩家无棋可走，跳过回合`);
            this.scheduleOnce(() => {
                this.endPlayerTurn();
            }, 1);
            return;
        }

        // 构建可移动棋子映射
        const canMoveChessMap = new Map<string, number[]>();
        availableChesses.forEach(chessId => {
            canMoveChessMap.set(chessId, [diceResult]);
        });

        // 设置全局可移动棋子数据
        globalData.canMoveChessMap = canMoveChessMap;

        // 显示可移动棋子
        ViewManager.notifyPlayerSelectDice(canMoveChessMap);

        console.log(`♟️ 显示可移动棋子:`, availableChesses);

        // 监听棋子点击
        this.setupChessClickListener();
    }

    /**
     * 设置棋子点击监听
     */
    private setupChessClickListener(): void {
        // 监听棋子移动完成事件
        director.on('chess-move-finished', this.onChessMoveFinished, this);
    }

    /**
     * 棋子移动完成处理
     */
    private onChessMoveFinished(): void {
        console.log('♟️ 棋子移动完成');

        // 清理监听器
        director.off('chess-move-finished', this.onChessMoveFinished, this);
        director.off('dice-clicked', this.onDiceClicked, this);

        // 结束当前玩家回合
        this.scheduleOnce(() => {
            this.endPlayerTurn();
        }, 1);
    }
    
    /**
     * 执行AI回合
     */
    private executeAITurn(aiPlayer: any): void {
        console.log(`🤖 AI玩家 ${aiPlayer.name} 开始思考...`);
        
        // 1. AI摇骰子
        const diceResult = this.rollDiceForAI();
        
        // 2. AI选择移动
        this.scheduleOnce(() => {
            this.aiSelectMove(aiPlayer, diceResult);
        }, 0.5);
    }
    
    /**
     * AI摇骰子
     */
    private rollDiceForAI(): number {
        const diceResult = Math.floor(Math.random() * 6) + 1;
        gameLocalServer.instance.diceNumArray = [diceResult];
        
        console.log(`🎲 AI摇出骰子: ${diceResult}`);
        
        // 显示骰子结果
        ViewManager.notifyDiceResult(
            globalData.currentOpUserId,
            globalData.currentPlayerIndex,
            diceResult,
            [diceResult]
        );
        
        return diceResult;
    }
    
    /**
     * AI选择移动
     */
    private aiSelectMove(aiPlayer: any, diceResult: number): void {
        // 获取AI可移动的棋子
        const availableChesses = this.getAvailableChessesForPlayer(aiPlayer.userIndex, diceResult);
        
        if (availableChesses.length === 0) {
            console.log(`🤖 AI玩家 ${aiPlayer.name} 无棋可走`);
            this.endPlayerTurn();
            return;
        }
        
        // AI随机选择一个棋子移动
        const randomIndex = Math.floor(Math.random() * availableChesses.length);
        const selectedChess = availableChesses[randomIndex];
        
        console.log(`🤖 AI玩家 ${aiPlayer.name} 选择移动棋子: ${selectedChess}`);
        
        // 执行移动
        this.executeChessMove(selectedChess, diceResult);
    }
    
    /**
     * 获取玩家可移动的棋子
     */
    private getAvailableChessesForPlayer(playerIndex: number, diceResult: number): string[] {
        const availableChesses = [];
        
        // 检查该玩家的所有棋子
        for (let i = 1; i <= 4; i++) {
            const chessId = `${playerIndex}-${i}`;
            const movePath = gameLocalServer.instance.getMoveInfoList(chessId, diceResult);
            
            if (movePath && movePath.length > 0) {
                availableChesses.push(chessId);
            }
        }
        
        return availableChesses;
    }
    
    /**
     * 执行棋子移动
     */
    private executeChessMove(chessId: string, diceResult: number): void {
        const localServer = gameLocalServer.instance;
        
        // 计算移动路径
        const movePath = localServer.getMoveInfoList(chessId, diceResult);
        const eatOff = localServer.getEatOffList(movePath, chessId);
        const finalChess = localServer.getFinalChessesList(movePath, chessId, eatOff);
        const obsList = localServer.getObsList(eatOff);
        const restDiceNumArray = localServer.getResetDiceNumList(diceResult);
        
        console.log(`♟️ 执行移动: ${chessId}, 路径:`, movePath);
        
        // 执行移动动画
        ViewManager.chessMove(
            chessId,
            movePath,
            eatOff,
            finalChess,
            obsList,
            [], // userCoinInfo (本地模式不需要)
            localServer.opIndex,
            restDiceNumArray,
            false // roundEnd
        );
        
        // 移动完成后，切换到下一个玩家
        this.scheduleOnce(() => {
            this.endPlayerTurn();
        }, 2); // 等待动画完成
    }
    
    /**
     * 结束当前玩家回合
     */
    private endPlayerTurn(): void {
        console.log(`🔄 玩家 ${globalData.currentOpUserId} 回合结束`);

        // 清理状态
        globalData.canMoveChessMap = null;
        globalData.canTouchDice = false;
        globalData.noticeDice = false;
        globalData.playerOpStatus = PlayerOpStatus.Other;

        // 重置myUserId，这样isMyRound会变为false
        globalData.myUserId = "";

        // 关闭骰子对话框
        ViewManager.closeDiceDialog();
        ViewManager.closeDiceWave();

        // 切换到下一个玩家
        this._currentPlayerIndex++;
        if (this._currentPlayerIndex > 4) {
            this._currentPlayerIndex = 1;
        }

        // 检查游戏是否结束
        if (this.checkGameEnd()) {
            this.endGame();
            return;
        }

        // 继续下一轮
        this.scheduleOnce(() => {
            this.nextPlayerTurn();
        }, 1);
    }
    
    /**
     * 检查游戏是否结束
     */
    private checkGameEnd(): boolean {
        // 简单的胜利条件检查：某个玩家的所有棋子都到达终点
        const finalIndexes = [105, 115, 125, 135];
        const chessInfo = globalData.gameInfo.chessInfo;
        
        for (let player = 1; player <= 4; player++) {
            let playerFinishedCount = 0;
            const playerFinalIndex = finalIndexes[player - 1];
            
            for (let chess = 1; chess <= 4; chess++) {
                const chessId = `${player}-${chess}`;
                const chessData = chessInfo.find((c: any) => c.chessId === chessId);
                
                if (chessData && chessData.chessIndex === playerFinalIndex) {
                    playerFinishedCount++;
                }
            }
            
            if (playerFinishedCount === 4) {
                console.log(`🏆 玩家 ${player} 获胜！`);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 结束游戏
     */
    private endGame(): void {
        console.log('🎉 游戏结束！');
        globalData.gameInfo.gameStatus = GAME_STAGE.END;
        
        // 这里可以显示游戏结束界面
        // ViewManager.showGameOverDialog();
    }
    
    /**
     * 停止本地游戏
     */
    public stopLocalGame(): void {
        if (this._gameTimer) {
            clearInterval(this._gameTimer);
            this._gameTimer = null;
        }
        
        console.log('⏹️ 本地游戏已停止');
    }
    
    /**
     * 重新开始游戏
     */
    public restartLocalGame(): void {
        this.stopLocalGame();
        this.scheduleOnce(() => {
            this.startLocalGame();
        }, 0.5);
    }
}
