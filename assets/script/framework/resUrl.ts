import { Prefab } from "cc"

/**
 * @content 资源地址统一管理地址
 * <AUTHOR>
 */
const __dialog = "prefab/dialog/"
const __prefabsUrl = "prefab/"
const __anim = "prefab/anim/"
const __2v2 = "prefab/2v2/"

export class ResUrl {
    public static allResInfo: { [key: string]: object } = {
        settlementItem: { url: __prefabsUrl + "settlementItem", type: Prefab, uuid: "", name: "settlementItem", isLoad: true },
        gameOver: { url: __dialog + "gameOver", type: Prefab, uuid: "", name: "gameOver", isLoad: true },
        numEffect: { url: __anim + "numEffect", type: Prefab, uuid: "", name: "numEffect", isLoad: true },
        gameOver_2v2: { url: __2v2 + "gameOver_2v2", type: Prefab, uuid: "", name: "gameOver_2v2", isLoad: true },
        settlementItem_2v2: { url: __2v2 + "settlementItem_2v2", type: Prefab, uuid: "", name: "settlementItem_2v2", isLoad: true },
    }
}