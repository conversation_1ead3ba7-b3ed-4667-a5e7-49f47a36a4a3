
/**
 * 游戏当前所处环境
 * 分别为: 开发环境下，预发环境，正式环境下
 * 正式环境下：所有日志将会被屏蔽
 */
export enum Evn {
    Dev,
    Release,
    Formal,
}

/** log等级 */
export enum LogLevel {
    /** 开发者。 */
    Dev,
    /** 正常。 */
    Normal,
    /** 警告信息。发生了一些错误，一般不会导致游戏崩溃。 */
    Warn,
    /** 错误信息。发生了一些错误，可能会导致游戏崩溃。 */
    Error,
    /** 重大错误信息。一般需要重启游戏。 */
    ImportantError,
}

/** 界面优先级  */
export const PRIORITY = {
    ZERO: 0,            // 最底层
    NORMAL: 10,         // 普通界面
    DIALOG: 100,        // 弹窗的Z序
    REWARD: 200,        // 奖励的弹窗
    LOADING: 300,       // 加载界面
    WAITING: 400,       // 等待界面弹窗
    TIPS: 500,          // 提示
}

/**
 * panel 开启和关闭动画
 */
 export interface IPanelAnim {
    openIsAnim: boolean,
    openAnimType: number,
    closeIsAnim: boolean,
    closeAnimType: number,
}