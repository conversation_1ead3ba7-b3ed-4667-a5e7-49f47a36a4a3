import { _decorator, Component, Prefab, director, find, Node, view, dynamicAtlasManager, macro, tween, game, Game } from 'cc';
import { PoolMgr } from '../framework/poolMgr';
import { cs } from '../framework/log';
import { Utils } from '../framework/frameworkUtils';
import { CachePrefabs } from '../framework/cachePrefabs';
import { UIMgr } from '../framework/uiMgr';
import GameConfig from '../gameConfig';
import globalData from '../globalData';
import GameMgr from '../manager/gameMgr';
import { AudioMgr } from '../manager/audioMgr';
import ViewManager from '../manager/view';
import BaseComponentManager from '../prefabs/baseComponent';
import ExportAPI from '../seal/exportAPI';
import { EventType } from '../type/events';
import { commonUtil } from '../utils/commonUtil';
import { getUrlParam1 } from '../utils/utils';
import { testGame } from '../debug/ludo';
import { LocalGameMgr } from '../manager/localGameMgr';
import '../utils/gameModeSwitch'; // 导入游戏模式切换工具
import '../utils/localGameDebug'; // 导入本地游戏调试工具

macro.CLEANUP_IMAGE_CACHE = false;
dynamicAtlasManager.enabled = true;
const { ccclass, property } = _decorator;

@ccclass
export default class Ludo extends Component {

  @property(Node)
  gameContainer: Node = null; // 游戏节点

  @property(Prefab)
  chessPrefab: Prefab = null; // 棋子资源

  @property(Prefab)
  playerPrefab: Prefab = null; // 玩家资源

  @property(Node)
  baseComponentLyer: Node = null;  // 基础组件层 (因为只有一个场景，层级关系，可以采用这种方式进行维护)

  @property(Node)
  popUpLayer: Node = null;  // 弹窗层

  @property(Node)
  loadingLayer: Node = null // 加载层

  @property(Node)
  tipNode: Node = null;

  gridContainer: Node

  chessBoardNode: Node

  userGroupNode: Node

  chessGroupNode: Node

  aniBoxNode: Node

  obstacleNode: Node

  baseComponentManager: BaseComponentManager

  private exportAPI: ExportAPI = null;

  private startPreLoadTime: number = 3;

  private gameMgr: GameMgr = new GameMgr()

  private _isDisplayGameApp = true // display game view

  protected async onLoad() {
    console.log("game version", GameConfig.version)
    this.loadGameNode()
    ViewManager.gameInstance = this

    this.exportAPI = new ExportAPI();
    this.exportAPI.init();
    PoolMgr.instance.init();
    AudioMgr.instance.init(this.node.getChildByName('soundLayer'))
    this.addEventListener()
    this.gameMgr.startGame()
  }

  start() {
    // ========== 强制本地游戏模式 ==========
    if (GameConfig.FORCE_LOCAL_MODE) {
      console.log('🎮 强制启动本地游戏模式');
      this.startLocalGameMode();
    } else {
      // 原有的网络游戏逻辑
      if (getUrlParam1('debug')) testGame(this.node)
    }

    Utils.timeOut(this, this.startPreLoadTime).then(() => {
      CachePrefabs.instance.loadAllPrefabs();
      cs.log("load finished!");
    })
    this.adapterGameScene()
  }

  /**
   * 启动本地游戏模式
   */
  private startLocalGameMode(): void {
    console.log('🚀 初始化本地游戏...');

    // 添加本地游戏管理器组件
    const localGameMgr = this.node.addComponent(LocalGameMgr);

    // 延迟启动，确保所有组件都已初始化
    this.scheduleOnce(() => {
      localGameMgr.startLocalGame();
    }, 2); // 等待2秒确保界面加载完成
  }

  protected onDestroy(): void {
    this.removeEventListener()
    this.exportAPI.onDestroy()
    this.gameMgr.stopGame()
  }

  private addEventListener() {
    game.on(Game.EVENT_SHOW, this.onGameShow, this)
    game.on(Game.EVENT_HIDE, this.onGameHide, this)
    director.on(EventType.CHANGE_VIEW, this.adapterGameScene, this)
  }

  private removeEventListener() {
    game.off(Game.EVENT_SHOW, this.onGameShow, this)
    game.off(Game.EVENT_HIDE, this.onGameHide, this)
    director.off(EventType.CHANGE_VIEW, this.adapterGameScene, this)
  }

  /**
   * @zh 游戏切入后台 @en switch app into background
   */
  private onGameHide() {
    this._isDisplayGameApp = false
    ViewManager.toggleVisiableResultParticle(false)
    AudioMgr.instance.setAudioMute(true)
  }

  /**
   * @zh 显示App @en disaplay game app
   */
  private onGameShow() {
    if (!this._isDisplayGameApp) {
      this._isDisplayGameApp = true
      ViewManager.toggleVisiableResultParticle(true)
    }
    AudioMgr.instance.setAudioMute(false)
  }

  private loadGameNode() {
    // 格子容器，容器里面装了15x15个格子
    this.chessBoardNode = this.gameContainer.getChildByName('ChessBoard')
    this.gridContainer = this.chessBoardNode.getChildByName('GridContainer')
    this.aniBoxNode = this.gameContainer.getChildByName('AniBox')
    this.userGroupNode = this.gameContainer.getChildByName('UserGroup')
    this.chessGroupNode = this.gameContainer.getChildByName('ChessGroup')
    this.obstacleNode = this.chessBoardNode.getChildByName('ObstacleBox')
    this.baseComponentManager = this.baseComponentLyer.getComponent(BaseComponentManager)
    UIMgr.instance.showDialog('prefab/component/toast', null, this.popUpLayer);
  }

  private adapterGameScene() {
    console.log('adapterGameScene start', JSON.stringify(globalData.roomInfo))
    // 1200 1640
    if (!globalData.roomInfo || typeof globalData.roomInfo !== 'object' || !Object.keys(globalData.roomInfo).length) return
    const { topPartHeight, middlePartHeight, bottomPartHeight } = globalData.roomInfo
    const presentCenterRate = commonUtil.getUITransform(this.gameContainer).height / commonUtil.getUITransform(this.node).height
    //  2/3
    const newCenterRate = middlePartHeight / (topPartHeight + middlePartHeight + bottomPartHeight)
    // 2
    const rate = newCenterRate / presentCenterRate
    if (typeof rate === 'number' && rate > 0 && rate <= 1) {
      globalData.gameScaleRate = rate || 1
      this.gameContainer.setScale(commonUtil.getEqualVec3(globalData.gameScaleRate))
    }
    console.log('adapterGameScene', topPartHeight, middlePartHeight, bottomPartHeight)
    this.adapterTopAraeView()
  }

  private adapterTopAraeView() {
    let handleView = () => {
      if (ViewManager.topAreaScript) {
        handleView = null
        return ViewManager.topAreaScript.updateWidget()
      }
      setTimeout(handleView, 2000)
    }
    handleView()
  }

  showCompleteTips() {
    return new Promise(async (resolve) => {
      this.tipNode.active = true;
      tween(commonUtil.getUIOpacity(this.tipNode))
        .set({ opacity: 0 })
        .to(0.5, { opacity: 255 })
        .delay(2)
        .to(0.5, { opacity: 0 })
        .call(() => {
          this.tipNode.active = false;
          resolve('');
        })
        .start();
    })
  }
}
