import { I18n } from "../framework/i18n";
import { cs } from "../framework/log";
import { UIMgr } from "../framework/uiMgr";
import globalData from "../globalData";
import { END_MODE, GAME_MODE } from "../network/constants";
import { getStoreLocalData, getUserInfo } from "../seal/bridge";
import { getUrlParam1 } from "../utils/utils";
import { GameModeMgr } from "./gameModeMgr";

export interface IResultCoinInfo {
    /*********************************** 公共*********************/
    userId: string,  // 用户ID
    portrait: string, // 头像
    portraitMask: string,  // 头像外框
    name: string, // 名称

    totalBalance: number,//应收总收益

    actuallyTotalBalance: number, // 总收益
    eatChessAntes: number, // 吃棋底注
    eatMultiple: number, // 吃棋倍率
    eatChess: number, // 吃棋子个数
    eatChessBrokeAmount: number,//吃棋破产玩家数
    actuallyEatChessGainBalanceNotBroke: number, // 吃棋实际获得金额数(玩家未破产)
    actuallyEatChessGainBalanceBroke: number, // 吃棋实际获得金额数(玩家破产)

    eatChessChargeBalance: number,//吃棋-平台收费收益
    eatChessGainBalance: number,//吃棋应获得金币数

    beEatMultiple: number, //被吃棋倍数 (倍数 ) 
    beEatChess: number, // 被吃棋子个数
    actuallyBeEatChessGainBalance: number, //  实际扣减金额数

    eatChessChargeRate: number, // 收费比例 1-100 吃棋-平台收费比例 1-100
    eatChessActuallyChargeBalance: number, // 吃棋-平台实际收费收益
    isWinner: number  //是否为赢家:1:输，2:赢，3:平局
    isBroke: number   //是否破产  是否破产: 0-未破产, 1-局中破产, 2-局结束破产
    from: number,     // 1局中 2结算
    /*********************************** 公共*********************/

    /******* win *********/
    winnerEatChess: number;  //赢家吃棋数
    winnerEatMultiple: number;  //赢家吃棋倍数
    gameSettleBrokeAmount: number;  //游戏破产玩家数
    actuallyWinnerSettleAmountNotBroke: number; //与赢家实际结算金额(针对未破产玩家)
    actuallyWinnerSettleAmountBroke: number;  //与赢家实际结算金额(针对破产玩家) // 使用这个字段
    chargeRate: number;   //游戏结算-平台收费比例 1-100
    chargeBalance: number;  //游戏结算-平台收费收益
    actuallyChargeBalance;  //游戏结算-平台实际收费收益
    /******* win *********/
    winnerSettleAmount: number,//与赢家应结算金额

    /****** 新增结算 ******/
    gameEscapeUserAmount: number; // 游戏逃跑玩家数
    escapeBalance: number;  //逃跑玩家需支出的金币数（理论）
    profitFromEscapeBalance: number;  // 非逃跑玩家收到的金币数（理论）
    actuallyEscapeBalance: number;  //逃跑玩家需支出的金币数(实际，考虑用户余额不足情况)
    actuallyProfitFromEscapeBalance: number; //非逃跑玩家收到的金币数(实际，考虑用户余额不足情况)
    isEscape: number;  //是否逃跑: 0-未逃跑, 1-已逃跑

    teamName: string;//队伍信息：A，B,
    profitFromEscapeActuallyChargeBalance: number
}


// 5237977668502160428

export class GameOverMgr {
    private static _ins: GameOverMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new GameOverMgr();
        return this._ins;
    }

    public coinInfo: any = null;
    public userId: string = "";
    public data: any = null;

    // private testData = [{ "actuallyBeEatChessGainBalance": 300, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 115, "actuallyEatChessGainBalanceNotBroke": 600, "actuallyEscapeBalance": 0, "actuallyProfitFromEscapeBalance": 168, "actuallyTotalBalance": 548, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 135, "chargeRate": 5, "eatChess": 3, "eatChessActuallyChargeBalance": 35, "eatChessAntes": 300, "eatChessBrokeAmount": 1, "eatChessChargeBalance": 45, "eatChessChargeRate": 5, "eatChessGainBalance": 900, "eatMultiple": 2, "escapeBalance": 0, "gameAntes": 300, "gameEscapeUserAmount": 2, "gameSettleBrokeAmount": 3, "isBroke": 0, "isEscape": 0, "isWinner": 2, "profitFromEscapeBalance": 168, "totalBalance": 3288, "userId": 5270460871234027564, "winnerEatChess": 3, "winnerEatMultiple": 0, "winnerSettleAmount": 2700, from: 2 }, { "actuallyBeEatChessGainBalance": 0, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyEscapeBalance": 200, "actuallyProfitFromEscapeBalance": 0, "actuallyTotalBalance": -200, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 0, "beEatChessGainBalance": 0, "beEatMultiple": 0, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 0, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "escapeBalance": 200, "gameAntes": 0, "gameEscapeUserAmount": 0, "gameSettleBrokeAmount": 0, "isBroke": 1, "isEscape": 1, "isWinner": 1, "profitFromEscapeBalance": 0, "totalBalance": -200, "userId": 5241566141591071276, "winnerEatChess": 0, "winnerEatMultiple": 0, "winnerSettleAmount": 0, from: 2 }, { "actuallyBeEatChessGainBalance": 0, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyEscapeBalance": 200, "actuallyProfitFromEscapeBalance": 66, "actuallyTotalBalance": -134, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 0, "beEatChessGainBalance": 0, "beEatMultiple": 0, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 0, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "escapeBalance": 200, "gameAntes": 0, "gameEscapeUserAmount": 1, "gameSettleBrokeAmount": 0, "isBroke": 1, "isEscape": 1, "isWinner": 1, "profitFromEscapeBalance": 66, "totalBalance": -134, "userId": 5240380346215701036, "winnerEatChess": 0, "winnerEatMultiple": 0, "winnerSettleAmount": 0, from: 2 }, { "actuallyBeEatChessGainBalance": 715, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 300, "actuallyEscapeBalance": 0, "actuallyProfitFromEscapeBalance": 0, "actuallyTotalBalance": -415, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 3, "beEatChessGainBalance": 900, "beEatMultiple": 3, "chargeBalance": 0, "chargeRate": 0, "eatChess": 1, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 15, "eatChessChargeRate": 5, "eatChessGainBalance": 300, "eatMultiple": 1, "escapeBalance": 0, "gameAntes": 0, "gameEscapeUserAmount": 0, "gameSettleBrokeAmount": 0, "isBroke": 1, "isEscape": 0, "isWinner": 1, "profitFromEscapeBalance": 0, "totalBalance": -615, "userId": 5111809718754516140, "winnerEatChess": 0, "winnerEatMultiple": 0, "winnerSettleAmount": 0, from: 2 }]
    private testData =
        [{ "actuallyBeEatChessGainBalance": 0, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 40, "actuallyEscapeBalance": 0, "actuallyProfitFromEscapeBalance": 0, "actuallyTotalBalance": 43, "actuallyWinnerSettleAmountBroke": 5, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 0, "beEatChessGainBalance": 0, "beEatMultiple": 0, "chargeBalance": 3, "chargeRate": 5, "eatChess": 4, "eatChessActuallyChargeBalance": 2, "eatChessAntes": 10, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 2, "eatChessChargeRate": 5, "eatChessGainBalance": 40, "eatMultiple": 4, "escapeBalance": 0, "from": 2, "gameEscapeUserAmount": 0, "gameSettleBrokeAmount": 2, "isBroke": 0, "isEscape": 0, "isWinner": 2, "name": "user3343979", "portrait": "https://cdnoffice.lizhi.fm/user/2020/12/29/2847574727060536834.png", "portraitMask": "", "profitFromEscapeBalance": 0, "teamName": "B", "totalBalance": 105, "userId": "5113152715523891756", "winnerEatChess": 7, "winnerEatMultiple": 2, "winnerSettleAmount": 70 }
            , { "actuallyBeEatChessGainBalance": 30, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 30, "actuallyEscapeBalance": 0, "actuallyProfitFromEscapeBalance": 0, "actuallyTotalBalance": 4, "actuallyWinnerSettleAmountBroke": 5, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 3, "beEatChessGainBalance": 30, "beEatMultiple": 3, "chargeBalance": 3, "chargeRate": 5, "eatChess": 3, "eatChessActuallyChargeBalance": 1, "eatChessAntes": 10, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 1, "eatChessChargeRate": 5, "eatChessGainBalance": 30, "eatMultiple": 3, "escapeBalance": 0, "from": 2, "gameEscapeUserAmount": 0, "gameSettleBrokeAmount": 2, "isBroke": 0, "isEscape": 0, "isWinner": 2, "name": "user2803445", "portrait": "https://cdnoffice.lizhi.fm/user/2022/07/11/2951249759354289154.jpg", "portitMask": "", "profitFromEscapeBalance": 0, "teamName": "B", "totalBalance": 66, "userId": "5237594338376025644", "winnerEatChess": 7, "winnerEatMultiple": 2, "winnerSettleAmount": 70 }
            , { "actuallyBeEatChessGainBalance": 20, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 10, "actuallyEscapeBalance": 0, "actuallyProfitFromEscapeBalance": 0, "actuallyTotalBalance": -20, "actuallyWinnerSettleAmountBroke": 10, "actuallyWinnerSettleAmountNotBroke": 10, "beEatChess": 2, "beEatChessGainBalance": 20, "beEatMultiple": 2, "chargeBalance": 0, "chargeRate": 0, "eatChess": 1, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 10, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 10, "eatMultiple": 1, "escapeBalance": 0, "from": 2, "gameEscapeUserAmount": 0, "gameSettleBrokeAmount": 2, "isBroke": 2, "isEscape": 0, "isWinner": 1, "name": "1256🙉", "porrait": "https://cdnoffice.lizhi.fm/user/2020/05/26/2807309702636112386.jpg", "portraitMask": "", "profitFromEscapeBalance": 0, "teamName": "A", "totalBalance": -80, "userId": "5236517478397395500", "winnerEatChess": 7, "winnerEatMultiple": 2, "winnerSettleAmount": 70 }
            , { "actuallyBeEatChessGainBalance": 30, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyEscapeBalance": 0, "actuallyProfitFromEscapeBalance": 0, "actuallyTotalBalance": -30, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 3, "beEatChessGainBalance": 30, "beEatMultiple": 3, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 10, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "escapeBalance": 0, "from": 2, "gameEscapeUserAmount": 0, "gameSettleBrokeAmount": 0, "isBroke": 1, "isEscape": 0, "isWinner": 1, "name": "22222😽", "portrait": "https://cdnoffice.izhi.fm/user/2020/05/27/2807450092232133122.jpg", "portraitMask": "", "profitFromEscapeBalance": 0, "teamName": "A", "totalBalance": -30, "userId": "5111840305364163756", "winnerEatChess": 7, "winnerEatMultiple": 2, "winnerSettleAmount": 0 }]

    //  [{ teamName: "B", portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", portraitMask: "", name: "ربحت", "actuallyBeEatChessGainBalance": 0, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 704, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": 669, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 0, "beEatChessGainBalance": 0, "beEatMultiple": 0, "chargeBalance": 135, "chargeRate": 5, "eatChess": 3, "eatChessActuallyChargeBalance": 35, "eatChessAntes": 300, "eatChessBrokeAmount": 3, "eatChessChargeBalance": 45, "eatChessChargeRate": 5, "eatChessGainBalance": 900, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 2, "isBroke": 0, "isWinner": 1, "totalBalance": 3285, "userId": "5236517478397395500", "winnerEatChess": 3, "winnerEatMultiple": 0, "winnerSettleAmount": 2565, gameEscapeUserAmount: 1, escapeBalance: 300, profitFromEscapeBalance: 100, actuallyEscapeBalance: 300, actuallyProfitFromEscapeBalance: 100, isEscape: 0, },
    // {teamName: "A", portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", portraitMask: "", name: 'مضاعف', "actuallyBeEatChessGainBalance": 205, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": -205, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 2, "isWinner": 1, "totalBalance": -1200, "userId": "5113152715523891756", "winnerEatChess": 3, "winnerEatMultiple": 1, "winnerSettleAmount": 900, gameEscapeUserAmount: 1, escapeBalance: 300, profitFromEscapeBalance: 100, actuallyEscapeBalance: 300, actuallyProfitFromEscapeBalance: 100, isEscape: 0, },
    // {teamName: "B", portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", portraitMask: "", name: "111123456223", "actuallyBeEatChessGainBalance": 241, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": -241, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 2, "isWinner": 1, "totalBalance": -1200, "userId": "5237594338376025644", "winnerEatChess": 3, "winnerEatMultiple": 1, "winnerSettleAmount": 900, gameEscapeUserAmount: 1, escapeBalance: 300, profitFromEscapeBalance: 100, actuallyEscapeBalance: 300, actuallyProfitFromEscapeBalance: 100, isEscape: 0, },
    // {teamName: "A", portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", portraitMask: "", name: "111123456224", "actuallyBeEatChessGainBalance": 258, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": -258, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 2, "isWinner": 1, "totalBalance": -1200, "userId": "5111840305364163756", "winnerEatChess": 3, "winnerEatMultiple": 1, "winnerSettleAmount": 900, gameEscapeUserAmount: 1, escapeBalance: 300, profitFromEscapeBalance: 100, actuallyEscapeBalance: 300, actuallyProfitFromEscapeBalance: 100, isEscape: 1, }]
    private testFunc() {
        this.data = { "coinInfo": [{ "actuallyBeEatChessGainBalance": 0, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 704, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": 669, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 0, "beEatChessGainBalance": 0, "beEatMultiple": 0, "chargeBalance": 135, "chargeRate": 5, "eatChess": 3, "eatChessActuallyChargeBalance": 35, "eatChessAntes": 300, "eatChessBrokeAmount": 3, "eatChessChargeBalance": 45, "eatChessChargeRate": 5, "eatChessGainBalance": 900, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 0, "isWinner": 2, "totalBalance": 3285, "userId": "5237977668502160428", "winnerEatChess": 3, "winnerEatMultiple": 0, "winnerSettleAmount": 2565 }, { "actuallyBeEatChessGainBalance": 205, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": -205, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 2, "isWinner": 1, "totalBalance": -1200, "userId": "5112984037157282860", "winnerEatChess": 3, "winnerEatMultiple": 1, "winnerSettleAmount": 900 }, { "actuallyBeEatChessGainBalance": 241, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": -241, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 2, "isWinner": 1, "totalBalance": -1200, "userId": "5113170366262411308", "winnerEatChess": 3, "winnerEatMultiple": 1, "winnerSettleAmount": 900 }, { "actuallyBeEatChessGainBalance": 258, "actuallyChargeBalance": 0, "actuallyEatChessGainBalanceBroke": 0, "actuallyEatChessGainBalanceNotBroke": 0, "actuallyTotalBalance": -258, "actuallyWinnerSettleAmountBroke": 0, "actuallyWinnerSettleAmountNotBroke": 0, "beEatChess": 1, "beEatChessGainBalance": 300, "beEatMultiple": 1, "chargeBalance": 0, "chargeRate": 0, "eatChess": 0, "eatChessActuallyChargeBalance": 0, "eatChessAntes": 300, "eatChessBrokeAmount": 0, "eatChessChargeBalance": 0, "eatChessChargeRate": 5, "eatChessGainBalance": 0, "eatMultiple": 0, "from": 2, "gameSettleBrokeAmount": 3, "isBroke": 2, "isWinner": 1, "totalBalance": -1200, "userId": "5113286746922058796", "winnerEatChess": 3, "winnerEatMultiple": 1, "winnerSettleAmount": 900 }], "userId": "5237977668502160428" }
        this.userId = '5237977668502160428';
    }

    public async setData(data) {
        this.userId = globalData.myUserId
        this.coinInfo = data
        if (getUrlParam1('debug')) {
            this.userId = '5113152715523891756';
            // this.coinInfo = this.testData
        }
    }

    public async getData() {
        return new Promise((resolve, reject) => {
            getStoreLocalData("localdata").then((data: any) => {
                cs.log("getStoreLocalData", data);
                this.data = data;
                let { coinInfo, userId, language, soundState, isIos } = data;
                // SoundMgr.instance.soundState = soundState;
                globalData.isIos = isIos
                this.coinInfo = coinInfo;
                this.userId = userId;
                I18n.languageState = language;
                resolve("");
            }).catch(() => {
                this.coinInfo = [];
                this.userId = "";
                reject("");
            })
        })
    }

    /**
     *  0 破产 1 lose 2 win  
     *  默认返回失败结算
     */
    public getType() {
        return new Promise((resolve) => {
            let rankInfo = (this.coinInfo ? this.coinInfo : this.data?.rankInfo) ?? [];
            let type = 1, retType = 1;
            for (let i = 0; i < rankInfo.length; i++) {
                if (rankInfo[i].userId == this.userId) {
                    let isWinner = rankInfo[i].isWinner;
                    let isBroke = rankInfo[i].isBroke;
                    let from = rankInfo[i].from;
                    type = this.getTypeByData(from, isWinner);
                }
            }
            retType = this.resetType(type);
            resolve(retType);
        })
    }

    /**
     * isWinner: number  是否为赢家:1:输，2:赢，3:平局
     * isBroke: number   是否破产  是否破产: 0-未破产, 1-局中破产, 2-局结束破产
     * from: number,     1局中 2结算
     * @param from 
     * @param isWinner 
     * @returns 
     */
    public getTypeByData(from, isWinner) {
        let type = 0;
        if (from == 2) {//2结算
            if (isWinner === 2) {
                type = 2;
            } else if (isWinner === 1) {
                type = 1;
            } else {
                type = 1;
            }
        } else if (from == 1) { //1局中 
            type = 0;
        } else {
            type = 1;
        }
        return type;
    }

    public resetType(type) {
        let openState = 0;
        switch (type) {
            case 0:
                openState = 2;
                break;
            case 1:
                openState = 1;
                break;
            case 2:
                openState = 0;
                break;
            default:
                break;
        }
        cs.log('resetType:', openState);
        return openState;
    }

    /**
     * 打开结算界面
     * @param data 
     */
    public openGameOver(data: any) {
        GameOverMgr.instance.setData(data);
        cs.log('------------- game open game over!');
        GameOverMgr.instance.getType().then((type) => {
            cs.log("result cur type:", type)
            cs.log("gameMode:", globalData?.gameInfo?.gameMode)
            cs.log("endMode:", globalData?.gameInfo?.endMode)
            if (globalData?.gameInfo?.gameMode == GAME_MODE.TWO_VS_TWO) {
                UIMgr.instance.showDialog("prefab/2v2/gameOver_2v2", { type });
            } else if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
                UIMgr.instance.showDialog("prefab/classicMode/classicGameOver", { type });
            } else {
                UIMgr.instance.showDialog("prefab/dialog/gameOver", { type });
            }
        });
    }
}



// 通过websork获取服务端的信息
// 通过发射事件 监听来修改对应的信息