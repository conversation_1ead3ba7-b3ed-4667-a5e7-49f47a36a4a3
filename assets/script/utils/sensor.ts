/**
 * @description record action data
 */
import globalData from "../globalData"
import { traceReport } from "../seal/bridge"

export const sensorClick = (params: Record<any, any>) => {
  if (!globalData.gameRoundId) return
  traceReport("$AppClick", {
    page_business_id: globalData.gameSubType,
    element_business_type: globalData.gameConfigId,//configId
    element_business_id: globalData.gameRoundId,
    ...params
  })
}

export const sensorAppViewScreen = (params: Record<any, any>) => {
  if (!globalData.gameRoundId) return
  traceReport("$AppViewScreen", {
    page_business_id: globalData.gameRoundId,
    page_business_type: globalData.gameConfigId,//configId
    page_status: globalData.gameSubType,
    ...params
  })
}

export const sensorViewScreen = (params: Record<any, any>) => {
  if (!globalData.gameRoundId) return
  traceReport("ViewScreen", {
    page_business_id: globalData.gameSubType,
    element_business_type: globalData.gameConfigId,//configId
    element_business_id: globalData.gameRoundId,
    ...params
  })
}

export const sensorResult = (params: Record<any, any>) => {
  if (!globalData.gameRoundId) return
  traceReport("ResultBack", {
    page_business_id: globalData.gameRoundId,
    business_type: globalData.gameSubType,
    element_business_type: globalData.gameConfigId,//configId
    ...params
  })
}

let isFinal = false
export const recordFinalResult = (params: Record<any, any>) => {
  if (isFinal) return
  sensorResult(params)
  isFinal = true
}
