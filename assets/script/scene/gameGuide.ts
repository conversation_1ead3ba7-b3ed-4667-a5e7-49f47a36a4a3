import { _decorator, Node, Component, tween, v3, JsonAsset, UITransform, UIOpacity } from "cc";
import { convertPrefabToNode, isInScene, loadLoaclResource, loadRemoteImg } from "../utils/utils";
import { GameGuideStep, PlayerPos, Scene } from "../type/index";
import { Utils } from "../framework/frameworkUtils";
import { getAppInfo, getGameModePageInfo, getUserInfo, notifyGameGuideFinish, notifyLeaveGame } from "../seal/bridge";
import GuideDialog from "../prefabs/guideDialog";
import globalData from "../globalData";
import ViewManager from "../manager/view";
import { END_MODE } from "../network/constants";
import TopArea from "../prefabs/topArea";
import GuideResultManager from "../prefabs/guideResult";
import { commonUtil } from "../utils/commonUtil";
import { LoadMgr } from "../framework/loadMgr";


const { ccclass } = _decorator;


@ccclass
export default class Guide extends Component {

  guideDialogNode: Node

  guideDialogManager: GuideDialog = null!;

  gusterNode: Node

  guideResultNode: Node

  guideMaskNode: Node

  gameGuideStep = GameGuideStep.Null

  private language = 'en'

  private labelDatas

  private isGuiding = true

  protected async onLoad() {
    if (!isInScene(Scene.Guide)) return
    globalData.roomInfo = await getGameModePageInfo()
    await this.loadLanguageLabel()
    this.guideDialogNode = await LoadMgr.instance.create('prefab/guide/guideDialog', this.node);
    // await convertPrefabToNode('prefab/guide/guideDialog', this.node)
    this.guideResultNode = await LoadMgr.instance.create('prefab/guide/guideResult', this.node);
    // await convertPrefabToNode('prefab/guide/guideResult', this.node)
    this.gusterNode = await LoadMgr.instance.create('prefab/anim/guster', this.node);
    // await convertPrefabToNode('prefab/anim/guster', this.node)
    this.guideMaskNode = await LoadMgr.instance.create('prefab/guide/guideMask', this.node);
    // await convertPrefabToNode('prefab/guide/guideMask', this.node)


    this.guideMaskNode.on(Node.EventType.TOUCH_START, this.handleGameGuideProcess, this)
    this.guideDialogManager = this.guideDialogNode.getComponent(GuideDialog)
    this.initScene()
    this.isGuiding = false
    this.handleGameGuideProcess()
  }

  protected onDestroy() {
    this.guideMaskNode?.off(Node.EventType.TOUCH_START, this.handleGameGuideProcess, this)
  }

  private async initScene() {
    let { userId, nickName, portrait } = await getUserInfo()
    globalData.currentOpUserId = globalData.myUserId = userId
    let playerList = [
      { userId: "111", name: this.language === 'en' ? 'Samirah' : "جميلة", portrait: 'https://cdnimg.cqpiwan.com/sociality/2022/09/19/2964275465155109948.png', userIndex: 1, isAuto: false, portraitMask: "", status: 1 },
      { userId: "222", name: this.language === 'en' ? 'Radko' : "محمد", portrait: 'https://cdnimg.cqpiwan.com/sociality/2022/09/19/2964275443678174780.png', userIndex: 2, isAuto: false, portraitMask: "", status: 1 },
      { userId, name: nickName, portrait: portrait, userIndex: 3, isAuto: false, portraitMask: "", status: 1 },
      { userId: "444", name: this.language === 'en' ? 'Akio' : "فارس", portrait: "https://cdnimg.cqpiwan.com/sociality/2022/09/19/2964275488775331900.png", userIndex: 4, isAuto: false, portraitMask: "", status: 1 },
    ]
    globalData.gameInfo.playerInfo = playerList
    const chessDatas = `[{"chessId":"1-1","chessIndex":1},{"chessId":"1-2","chessIndex":1},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":14},{"chessId":"2-2","chessIndex":14},{"chessId":"2-3","chessIndex":0},{"chessId":"2-4","chessIndex":0},{"chessId":"3-1","chessIndex":27},{"chessId":"3-2","chessIndex":27},{"chessId":"3-3","chessIndex":0},{"chessId":"3-4","chessIndex":0},{"chessId":"4-1","chessIndex":40},{"chessId":"4-2","chessIndex":40},{"chessId":"4-3","chessIndex":0},{"chessId":"4-4","chessIndex":0}]`
    // 29
    globalData.gameInfo.userCoinInfo = [
      { antes: 500, multiple: 0, userIndex: 1, userId: '111', eatChessNum: 0, coin: 95021, balance: 0, portrait: 'https://cdnimg.cqpiwan.com/user/2021/09/01/2893088020720802306.jpg' },
      { antes: 500, multiple: 0, userIndex: 2, userId: '222', eatChessNum: 0, coin: 10582, balance: 0, portrait: 'https://cdnimg.cqpiwan.com/user/2021/09/01/2893088020720802306.jpg' },
      { antes: 500, multiple: 0, userIndex: 3, userId: userId, eatChessNum: 0, coin: 59499, balance: 0, portrait },
      { antes: 500, multiple: 0, userIndex: 4, userId: '444', eatChessNum: 0, coin: 60124, balance: 0, portrait: 'https://cdnimg.cqpiwan.com/user/2021/09/01/2893088020720802306.jpg' },
    ]


    ViewManager.initUiInfo(3, playerList, JSON.parse(chessDatas), END_MODE.QUICK, [100, 110, 120, 130])
    this.guideResultNode
      .getComponent(GuideResultManager)
      .initGameResultView(
        this.language === 'en',
        nickName,
        await loadRemoteImg(portrait)
      )
    return
  }


  private async loadLanguageLabel() {
    let appInfo = await getAppInfo()
    this.language = appInfo.language
    const data: JsonAsset = await loadLoaclResource(`i18n/${appInfo.language}`, JsonAsset)
    if (!data) return
    this.labelDatas = data.json
  }

  private playVanishAnimation(node: Node, isVisiable: boolean) {
    if (!isVisiable) {
      commonUtil.setOpacity(node, 0);
      return
    }
    console.log("node:", node, ViewManager.gameInstance.chessBoardNode);
    const { x, y } = node.getPosition()
    const opacityComponent = commonUtil.getOpacity(node);
    node.setPosition(v3(x, y + 40))
    tween(node)
      .to(.5, { position: v3(x, y, 0) })
      .call(() => { })
      .start()
    tween(opacityComponent)
      .to(.5, { opacity: isVisiable ? 255 : 0 })
      .call(() => { })
      .start()
  }

  private toggleGuideSceneVisiable(isDialogVisiable: boolean, isGusterVisiable: boolean) {
    this.playVanishAnimation(this.gusterNode, isGusterVisiable)
    this.playVanishAnimation(this.guideDialogNode, isDialogVisiable)
  }

  handleGameGuideProcess() {
    switch (this.gameGuideStep) {
      case GameGuideStep.Null: {
        this.handleNextGuideStep(GameGuideStep.Initial)
        break
      }
      case GameGuideStep.Initial: {
        this.handleNextGuideStep(GameGuideStep.RollDice)
        break
      }
      case GameGuideStep.RollDice: {
        this.handleNextGuideStep(GameGuideStep.ClickChess)
        break
      }
      case GameGuideStep.ClickChess: {
        this.handleNextGuideStep(GameGuideStep.ReClickChess)
        break
      }
      case GameGuideStep.ReClickChess:
      case GameGuideStep.End: {
        this.handleNextGuideStep(GameGuideStep.End)
        break
      }
      default: break
    }
  }

  private async handleNextGuideStep(gameStep: GameGuideStep) {
    if (this.isGuiding) return
    this.isGuiding = true
    switch (gameStep) {
      case GameGuideStep.Initial: {
        if (GameGuideStep.Null !== this.gameGuideStep) return
        this.toggleGuideSceneVisiable(false, false)

        this.guideDialogManager.setDialogLabel(this.labelDatas.gameGuide['step1'])
        await Utils.timeOut(this, 1)
        this.guideDialogNode.setPosition(v3(-40, 35, 0))

        this.gusterNode.setPosition(v3(-70, -600, 0))
        this.toggleGuideSceneVisiable(true, true)

        ViewManager.notifyPlayerOpDice('', PlayerPos.LeftBottom, 6000, [])
        ViewManager.notifyPlayerResetBtnView(3, 'free')
        await Utils.timeOut(this, 1.5)
        ViewManager.topAreaScript.updateAllInfo()
        this.gameGuideStep = GameGuideStep.Initial
        break
      }
      case GameGuideStep.RollDice: {
        if (GameGuideStep.Initial !== this.gameGuideStep) return
        ViewManager.notifyDiceResult('3', 3, 3, [3])
        await Utils.timeOut(this, 1)
        this.toggleGuideSceneVisiable(false, false)
        await Utils.timeOut(this, 0.5)
        this.guideDialogManager.setDialogLabel(this.labelDatas.gameGuide['step2'])
        this.guideDialogNode.setPosition(v3(-40, 180, 0))
        this.gusterNode.setPosition(v3(100, -300, 0))
        await Utils.timeOut(this, 0.3)
        this.toggleGuideSceneVisiable(true, true)
        ViewManager
          .chessMove(
            '3-1',
            [22],
            [],
            JSON.parse('[{"chessId":"1-1","chessIndex":25},{"chessId":"1-2","chessIndex":0},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":14},{"chessId":"2-2","chessIndex":14},{"chessId":"2-3","chessIndex":0},{"chessId":"2-4","chessIndex":0},{"chessId":"3-1","chessIndex":22},{"chessId":"3-2","chessIndex":27},{"chessId":"3-3","chessIndex":0},{"chessId":"3-4","chessIndex":0},{"chessId":"4-1","chessIndex":40},{"chessId":"4-2","chessIndex":40},{"chessId":"4-3","chessIndex":0},{"chessId":"4-4","chessIndex":0}]'),
            [100, 110, 120, 130],
            []
          )
        await Utils.timeOut(this, 0.4)
        ViewManager.notifyPlayerSelectDice(new Map([['3-1', [4]]]))
        this.gameGuideStep = GameGuideStep.RollDice
        break
      }
      case GameGuideStep.ClickChess: {
        if (GameGuideStep.RollDice !== this.gameGuideStep) return
        await Utils.timeOut(this, 0.4)
        commonUtil.setOpacity(this.gusterNode, 0);
        commonUtil.setOpacity(this.guideDialogNode, 0);
        this.guideDialogNode.setPosition(v3(-40, 400, 0))

        ViewManager
          .chessMove(
            '3-1',
            [23, 24, 25],
            ['1-1'],
            JSON.parse('[{"chessId":"1-1","chessIndex":0},{"chessId":"1-2","chessIndex":0},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":14},{"chessId":"2-2","chessIndex":14},{"chessId":"2-3","chessIndex":0},{"chessId":"2-4","chessIndex":0},{"chessId":"3-1","chessIndex":120},{"chessId":"3-2","chessIndex":27},{"chessId":"3-3","chessIndex":0},{"chessId":"3-4","chessIndex":0},{"chessId":"4-1","chessIndex":40},{"chessId":"4-2","chessIndex":40},{"chessId":"4-3","chessIndex":0},{"chessId":"4-4","chessIndex":0}]'),
            [100, 110, 130],
            [
              { antes: 500, multiple: 1, userIndex: 1, userId: '111', eatChessNClickChessum: 2, coin: 2222, balance: -500 },
              { antes: 500, multiple: 1, userIndex: 2, userId: 123, eatChessNum: 2, coin: 2222, balance: 0 },
              { antes: 500, multiple: 3, userIndex: 3, userId: globalData.myUserId, eatChessNum: 1, coin: 59999, balance: 500 },
              { antes: 500, multiple: 1, userIndex: 4, userId: 123, eatChessNum: 2, coin: 2222, balance: 0 },
            ])
        await Utils.timeOut(this, 1)
        this.guideDialogManager.setDialogLabel(this.labelDatas.gameGuide['step3'])
        this.toggleGuideSceneVisiable(true, false)
        await Utils.timeOut(this, 2)
        this.toggleGuideSceneVisiable(false, false)
        this.guideDialogNode.setPosition(v3(-40, 320, 0))
        await Utils.timeOut(this, 1)
        this.guideDialogManager.setDialogLabel(this.labelDatas.gameGuide['step4'])
        this.toggleGuideSceneVisiable(true, false)
        await Utils.timeOut(this, 3)
        this.toggleGuideSceneVisiable(false, false)

        ViewManager.notifyDiceResult(globalData.myUserId, 3, 5, [5])
        ViewManager.notifyPlayerSelectDice(new Map([['3-1', [5]]]))

        await Utils.timeOut(this, 1)
        this.gusterNode.setPosition(v3(40, -340, 0))
        this.guideDialogManager.setDialogLabel(this.labelDatas.gameGuide['step5'])
        this.toggleGuideSceneVisiable(true, true)
        await Utils.timeOut(this, 1)
        this.gameGuideStep = GameGuideStep.ClickChess
        break
      }
      case GameGuideStep.ReClickChess: {
        if (GameGuideStep.ClickChess !== this.gameGuideStep) return
        ViewManager
          .chessMove(
            '3-1',
            [120, 121, 122, 123, 124, 125],
            [],
            JSON.parse('[{"chessId":"1-1","chessIndex":0},{"chessId":"1-2","chessIndex":0},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":14},{"chessId":"2-2","chessIndex":14},{"chessId":"2-3","chessIndex":0},{"chessId":"2-4","chessIndex":0},{"chessId":"3-1","chessIndex":125},{"chessId":"3-2","chessIndex":27},{"chessId":"3-3","chessIndex":0},{"chessId":"3-4","chessIndex":0},{"chessId":"4-1","chessIndex":40},{"chessId":"4-2","chessIndex":40},{"chessId":"4-3","chessIndex":0},{"chessId":"4-4","chessIndex":0}]'),
            [100, 110, 130],
            []
          )
        this.toggleGuideSceneVisiable(false, false)
        await Utils.timeOut(this, 3)
        commonUtil.setActive(this.guideResultNode, true)
        notifyGameGuideFinish()
        await Utils.timeOut(this, 1)
        this.gameGuideStep = GameGuideStep.ReClickChess
        break
      }
      case GameGuideStep.End: {
        notifyLeaveGame()
        this.gameGuideStep = GameGuideStep.End
        break
      }
      default: break
    }
    this.isGuiding = false
  }

}
