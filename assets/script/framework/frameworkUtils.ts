/**
 * @content 工具类
 * <AUTHOR>
 */

import { cs } from "./log";

export class Utils {
    /**
     * 延时计时器 
     * @param thisArg 
     * @param time 时间秒 
     * @returns 
     */
    public static async timeOut(thisArg, time: number) {
        return new Promise((resolve) => {
            thisArg.scheduleOnce(() => {
                resolve("");
            }, time);
        })
    }

    /**
     * 延时执行
     * @param time 时间ms
     * @returns 
     */
    public static async timeOut1(time: number) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve("");
            }, time);
        })
    }

    /**
     * 打乱数组
     * @param arr 
     * @returns 
     */
    static scrambleArray(arr: Array<any>) {
        return arr.sort(function () {
            return Math.random() > 0.5 ? -1 : 1;
        });
    }

    /**
     * @url 解析url到obj
     * @returns 
     */
    static analysisUrlToObj(url: string) {
        let obj = {};
        let str = url?.split("?")?.[1]?.split("&");
        if (!str || !str.length) return obj;
        for (let i = 0; i < str.length; i++) {
            let a = str[i].split('=');
            obj[a[0]] = a[1];
        }
        cs.log(obj);
        return obj;
    }

    /**
     * 文本自动添加省略号
     * @param word      需要添加省略号的文本
     * @param length    超过指定长度后面添加省略号
     */
    static ellipsis(word: string, length: number, ellipsisStr: string = "..."): string {
        return word.length <= length ? word : word.substr(0, length) + ellipsisStr;
    }

    /**
     * 数字转换 保留小数不四舍五入
     * @param value 
     * @returns 
     */
    static bigNumberTransform(value: number) {
        let fr = 1000, num = 3;
        while (value / fr >= 1) { fr *= 10; num += 1; }
        let str = "";
        if (num <= 6) {
            str = String(value);
        } else if (num > 6 && num < 10) {
            str = (value / 1000000).toFixed(2) + "M"
        } else if (num >= 10) {
            str = (value / 1000000000).toFixed(2) + "B"
        }
        return str;
    }

    /**
     * 大数字转换成万
     * @param value 
     * @returns 
     */
    static bigNumberTransform1(value: number) {
        cs.log("bigNumberTransform:", value);
        const newValue = ['', '', '']
        let fr = 1000, num = 3, text1 = '', fm = 1;
        while (value / fr >= 1) { fr *= 10; num += 1; }
        if (num <= 4) {
            return String(value);
        } else if (num <= 8) {
            text1 = (num - 4) / 3 > 1 ? 'kw' : 'w';
            fm = text1 === 'w' ? 10000 : 10000000;
            newValue[0] = value % fm === 0 ? (value / fm) + '' : (value / fm).toFixed(0) + '';
            newValue[1] = text1;
        } else if (num <= 16) {
            text1 = (num - 8) / 3 > 1 ? '千亿' : '亿'
            text1 = (num - 8) / 4 > 1 ? '万亿' : text1
            text1 = (num - 8) / 7 > 1 ? '千万亿' : text1
            fm = 1
            if (text1 === '亿') {
                fm = 100000000
            } else if (text1 === '千亿') {
                fm = 100000000000
            } else if (text1 === '万亿') {
                fm = 1000000000000
            } else if (text1 === '千万亿') {
                fm = 1000000000000000
            }
            newValue[0] = value % fm === 0 ? (value / fm) + '' : (value / fm).toFixed(0) + '';
            newValue[1] = text1
        }
        if (value < 1000) {
            newValue[0] = value + ''
            newValue[1] = ''
        }
        return newValue.join('')
    }

    /**
     * 获取时间戳转换成时间
     * @param date 
     * @returns string
     */
    static formatDateTime(date: Date) {
        if (!(date instanceof Date)) return "";
        let y = date.getFullYear();
        let m = date.getMonth() + 1;
        let mTime = m < 10 ? ('0' + m) : m;
        let d = date.getDate();
        let dTime = d < 10 ? ('0' + d) : d;
        let h = date.getHours();
        let hTime = h < 10 ? ('0' + h) : h;
        let minute = date.getMinutes();
        let minuteTime = minute < 10 ? ('0' + minute) : minute;
        let second = date.getSeconds();
        let secondTime = second < 10 ? ('0' + second) : second;
        return y + '-' + mTime + '-' + dTime + ' ' + hTime + ':' + minuteTime + ':' + secondTime;
    };

    /**
     * copy数组
     * @param arr 
     */
    public static copyArr(arr: Array<any>) {
        return arr.filter(function (item) { return item })
    }

    /**
     * 打乱数组
     * @param arr 
     * @returns 
     */
    public static arrRandomly(arr: Array<any>) {
        var len = arr.length;
        for (var i = len - 1; i >= 0; i--) {
            var randomIndex = Math.floor(Math.random() * (i + 1));
            var itemIndex = arr[randomIndex];
            arr[randomIndex] = arr[i];
            arr[i] = itemIndex;
        }
        return arr;
    }

    /**
     * 获取是否是今天
     */
    public static getTodayState() {
        let date = new Date();
        let state = 1;
        if (date.getHours() >= 0 && date.getHours() < 12) {
            state = 1;
        } else if (date.getHours() >= 12 && date.getHours() < 24) {
            state = 2;
        }
        return state;
    }

    /**
     * 只判断日月
     * @param str 
     * @returns 
     */
    public static isToday(str: number) {
        const time = new Date(Number(str));
        const timeDay = time.getDate();
        const timeMonth = time.getMonth()
        const today = new Date();
        const todayDay = today.getDate();
        const todayMonth = today.getMonth();
        let ret = false;
        if (timeDay == todayDay && timeMonth == todayMonth) ret = true;
        return ret;
    }
}
