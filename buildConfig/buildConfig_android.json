{"name": "android-native", "platform": "android", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "none", "replaceSplashScreen": false, "startScene": "d19bd984-71ec-4ae3-9f86-0d28ce03c6c5", "outputName": "android", "scenes": [{"url": "db://assets/scene/ludo.scene", "uuid": "21936b10-d44a-483b-89b9-c96e70b90a08", "inBundle": false}, {"url": "db://assets/scene/gameHall.scene", "uuid": "d19bd984-71ec-4ae3-9f86-0d28ce03c6c5", "inBundle": false}], "android": [], "packages": {"android": {"packageName": "game.ludo.test", "orientation": {"portrait": true, "upsideDown": false, "landscapeRight": false, "landscapeLeft": false}, "apiLevel": 30, "appABIs": ["arm64-v8a", "armeabi-v7a"], "useDebugKeystore": true, "keystorePath": "", "keystorePassword": "", "keystoreAlias": "", "keystoreAliasPassword": "", "appBundle": false, "androidInstant": false, "remoteUrl": "", "sdkPath": "", "ndkPath": "", "renderBackEnd": {"vulkan": false, "gles3": true, "gles2": false}, "swappy": false, "__version__": "1.0.1"}, "cocos-service": {"configID": "34f2fd", "services": [], "__version__": "3.0.4"}, "native": {"encrypted": false, "xxteaKey": "c7V5JbW55FcpxiZu", "compressZip": false, "JobSystem": "none", "__version__": "1.0.2"}}, "__version__": "1.3.3"}