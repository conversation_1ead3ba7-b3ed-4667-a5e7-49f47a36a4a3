import { Component, Node, ProgressBar, Sprite, Sprite<PERSON><PERSON><PERSON>, _decorator } from "cc";
import globalData from "../globalData";
import { sealBridge } from "../seal/SealBridge";
import { commonUtil } from "../utils/commonUtil";
import { sensorClick } from "../utils/sensor";
import { loadLoaclResource } from "../utils/utils";

const { ccclass, property } = _decorator;

@ccclass("VoiceManager")
export default class VoiceManager extends Component {

    @property(Sprite)
    micIconSp: Sprite = null; // 麦克风按钮

    @property(ProgressBar)
    micProgressComp: ProgressBar = null; // 音浪波纹控制

    @property(Node)
    micWaveIconNode: Node = null; // 音浪图标节点

    private userInfo: any

    private micAtlas = null // 麦克风图集

    private micTimmer = null // 麦克风波纹Timer

    private maxTimmer = null // 播放波纹最长调用Timmer

    private isMicMode = true // 开启麦标示

    protected async onLoad() {
        this.micAtlas = await loadLoaclResource('image/micAtlas', SpriteAtlas)
        this.toggleMicView(globalData.myUserId !== this.node.name)
    }

    protected onDestroy(): void {
        clearInterval(this.micTimmer)
        clearTimeout(this.maxTimmer)
    }

    protected onClickMicEvent() {
        const that = this
        globalData.cancelHosting(that.node.name)
        sensorClick({
            $title: 'ludo游戏房页',
            page_type: 'ludo',
            $element_name: `${(globalData.myUserId == this.node.name) ? "开关麦" : "静音"}`,
            // userId: that.node.name
        })
        sealBridge.call(
            'setSpeakerMode',
            {
                userId: that.node.name,
                status: !that.isMicMode
            }, function (ret: any) {
                // 处理android数据不兼容问题
                try {
                    ret = JSON.parse(ret)
                } catch (e) { } finally {
                    console.log('onClickMicEvent', JSON.stringify(ret))
                    if (ret?.status === "success") {
                        that.toggleMicView(!!ret.isOn)
                    }
                }
            })
    }

    public toggleMicView(isSound?: boolean) {
        this.isMicMode = !!isSound
        commonUtil.setActive(this.micWaveIconNode, this.isMicMode)
        this.micIconSp.spriteFrame = this.micAtlas.getSpriteFrame(
            this.isMicMode ? 'mic' : 'mic-close'
        )
    }

    // 播放麦克风音浪
    public playMicWave() {
        try {
            if (!this.micTimmer) {
                clearInterval(this.micTimmer)
                this.micTimmer = setInterval(() => {
                    if (!this?.micProgressComp) {
                        clearInterval(this.micTimmer)
                        this.micTimmer = null
                    }
                    this.micProgressComp.progress = Math.random()
                }, 100)
            }
            // 防抖
            if (this.maxTimmer) clearTimeout(this.maxTimmer)
            this.maxTimmer = setTimeout(() => {
                clearInterval(this.micTimmer)
                this.micProgressComp.progress = 0
                this.maxTimmer = null
                this.micTimmer = null
            }, 1000)
        } catch (e) {
            clearInterval(this.micTimmer)
            clearTimeout(this.maxTimmer)
            this.maxTimmer = null
            this.micTimmer = null
        }
    }
}
