import { _decorator, director } from 'cc'
import Toast from '../components/toast';
import { ACTIONS, GAME_MODE, GAME_STAGE, SOCKET_TYPE } from '../network/constants'
import globalData from '../globalData';
import ViewManager from './view';
import { sealBridge } from '../seal/SealBridge';
import { sensorAppViewScreen, sensorResult } from '../utils/sensor';
import { commonUtil } from '../utils/commonUtil';
import ChessManager from './chess';
import { GameStatusType, PlayerType } from '../seal/types';
import { NotifyGameStatus, NotifyPlayerStatus } from '../seal/bridge';
import { GameOverMgr } from './gameOverMgr';
import SyncCommand from './syncCommand';
import { gameLocalServer } from './gameLocalServer';
import { cs } from '../framework/log';

enum EResponseCode {
  RCODE_SUCCESS = 0, // 成功
  RCODE_FAIL = 1, // 失败
  RCODE_INVALID_PARAM = 2, // 无效参数
  RCODE_PLAYER_NOT_FOUND = 100, // 玩家未找到
  RCODE_PLAYER_ROUND_CAN_NOT_OP = 101, // 当前回合不属于自己
  RCODE_PLAYER_USER_ROUND_CAN_NOT_OP = 102, // 本回合已完成，不可重复操作
  RCODE_COMMAND_NOT_FOUND = 404, // 命令未找到
  RCODE_PLAYER_STATUS_LEAVE = 103, // 玩家离开
  RCODE_DICE_FAIL = 2001, // 摇骰子失败
  RCODE_MOVE_FAIL = 2002, // 移动失败
  RCODE_RESET_DICE_CAN_NOT_OP = 3001, // 不允许重置骰子
  RCODE_RESET_DICE_CAN_NOT_OP_FOR_TIME_OUT = 3002, // 超时 不允许重置骰子
  RCODE_RESET_DICE_CAN_NOT_OP_BUSINESS_ERRORS = 100001, // 业务错误,不允许重置骰子
  RCODE_RESET_DICE_CAN_NOT_OP_INVALID_PARAM = 100002, // 参数错误,不允许重置骰子
  RCODE_RESET_DICE_CAN_NOT_OP_FOR_NOT_SUFFICIENT_FUNDS = 100003, // 余额不足，不允许重置骰子
  RCODE_RESET_DICE_CAN_NOT_OP_ROUND_NOT_EXIST = 100004, // 局不存在，不允许重置骰子
  RCODE_RESET_DICE_CAN_NOT_OP_FOR_COUNT = 100005 // 重置次数限制，不允许重置骰子
}

// 指令map
const getSysncCommandMap = () => {
  return (
    new Map([
      [ACTIONS.NOTIFY_CHESS_MOVE, new SyncCommand()],
      [ACTIONS.NOTIFY_PLAYER_DICE, new SyncCommand()],
      [ACTIONS.NOTIFY_DICE_RESULT, new SyncCommand()],
      [ACTIONS.NOTIFY_MOVE_SELECT, new SyncCommand()],
      [ACTIONS.NOTIFY_PLAYERSTATUS_UPDATE, new SyncCommand()],
      [ACTIONS.NOTIFY_RESET_INFO, new SyncCommand()],
      [ACTIONS.NOTIFY_BUST_PLAYER_RESULT, new SyncCommand()],
      [ACTIONS.NOTIFY_RESET_COUNTDOWN, new SyncCommand()],
    ])
  )
}

export default class GameMgr {
  private _commandMap: Map<ACTIONS, SyncCommand> // 指令map

  constructor() {
    ChessManager.chessMap = new Map()
    ViewManager.playerNodeMap = new Map()
  }

  /**
   * @zh 开始游戏 @en start game
   */
  public startGame() {
    this._commandMap = getSysncCommandMap()
    director.on('initWebSocket', this.handleInitEvents, this);
    director.on('handleGameStage', this.handleGameStage, this);
    director.on('handleInvalidMessage', this.handleInvalidMessage, this);
  }

  /**
   * @zh 停止游戏 @en stop game
   */
  public stopGame() {
    director.off('initWebSocket', this.handleInitEvents, this);
    director.off('handleGameStage', this.handleGameStage, this);
    director.off('handleInvalidMessage', this.handleInvalidMessage, this);
  }


  /**
   * 初始化链接回调事件
   */
  private handleInitEvents(h5callbackId?: string) {
    commonUtil.setActive(ViewManager.gameInstance.loadingLayer, false)
    // globalData.socketSend(SOCKET_TYPE.CREATE_GAME, {});
    // globalData.socketSend(SOCKET_TYPE.LOGIN, {});
    globalData.socketSend(SOCKET_TYPE.RE_JOIN, {});
    globalData.socketSend(SOCKET_TYPE.GAME_INFO, {});
  }

  private async handleInvalidMessage(type, rCodeNumber, errorMessage) {
    // console.log("handleInvalidMessage", rCodeNumber, errorMessage)
    if ([
      EResponseCode.RCODE_RESET_DICE_CAN_NOT_OP_BUSINESS_ERRORS,
      EResponseCode.RCODE_RESET_DICE_CAN_NOT_OP_INVALID_PARAM,
      EResponseCode.RCODE_RESET_DICE_CAN_NOT_OP_FOR_NOT_SUFFICIENT_FUNDS,
      EResponseCode.RCODE_RESET_DICE_CAN_NOT_OP_ROUND_NOT_EXIST,
      EResponseCode.RCODE_RESET_DICE_CAN_NOT_OP_FOR_COUNT
    ].includes(rCodeNumber)) {
      if (rCodeNumber === EResponseCode.RCODE_RESET_DICE_CAN_NOT_OP_FOR_COUNT) {
        return Toast.show(window.cocosI18n?.resetLimitTip, 2000)
      }
      sensorResult({
        result_type: 'LudoResetDice',
        page_business_type: '',
        element_business_id: rCodeNumber,
        is_success: 'fail',
        element_business_content: Date.now() - globalData.clickResetDiceTime
      })
      sealBridge.call('notifyResetDiceResult', {
        code: rCodeNumber,
        btnRect: ViewManager.getResetButtonInfo()
      })
      ViewManager.closePlayerResetCountdownEffect()
    }
  }

  /**
   * 处理游戏状态
   */
  private handleGameStatus(message: any) {
    const { gameId, winner, gameRoundId, status, gameMode, playerInfo, chessInfo, endMode } = message
    gameLocalServer.instance.gameMode = gameMode;
    if (globalData.gameInfo.gameStatus != status) {
      if ((globalData.gameInfo.gameStatus === GAME_STAGE.INIT && status === GAME_STAGE.JOIN) || (globalData.gameInfo.gameStatus === GAME_STAGE.END && status === GAME_STAGE.JOIN)) {
        // this.toggleDebugModal(playerInfo.length === 0 ? DebugGameStatus.Ready : DebugGameStatus.Match)
        playerInfo.forEach(player => {
          NotifyPlayerStatus(PlayerType.SEAL_PLAYER_STATUS_JOINED, player.userId.toString(), { playerStatusList: playerInfo })
        });
        if ((gameMode === GAME_MODE.ONE_VS_ONE && playerInfo.length === 2) || (gameMode === GAME_MODE.FOUR_TEAM && playerInfo.length === 4)) {
          NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_READY)
        } else {
          NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_IDLE)
        }
      }

      if (status === GAME_STAGE.GAMEING) {
        sensorAppViewScreen({
          "$title": 'ludo游戏房页',
          "page_type": "ludo"
        })
        sensorResult({
          result_type: 'LudoStartLoading',
        })
        console.info("GAME_STAGE.GAMEING，" + new Date().getTime())
        // this.toggleDebugModal()
        gameLocalServer.instance.obstacleInfo = message?.obstacleInfo;
        ViewManager.initUiInfo(globalData.currentPlayerIndex, playerInfo, chessInfo, endMode, message?.obstacleInfo)
        playerInfo.forEach(player => {
          NotifyPlayerStatus(PlayerType.SEAL_PLAYER_STATUS_PLAYING, `${player.userId}`, { playerStatusList: playerInfo })
        });
        // 状态回调兜底
        setTimeout(() => {
          NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_PLAYING)
        }, 300)
      } else if (status === GAME_STAGE.END) {
        console.info("GAME_STAGE.END" + new Date().getTime())
        const isAudioFinished = ChessManager.isAudioFinished();
        // 产品要求：进入结算页有礼花和音效，等待特效结束后才调用cb
        const cb = () => {
          // globalData.resetGlobalData();
          // console.log("玩家结算数据", JSON.stringify(message?.rankInfo?.coinInfo))
          GameOverMgr.instance.openGameOver(message?.rankInfo?.coinInfo ?? [])
          NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_END, {
            gameId: gameId.toString(),
            gameRoundId: gameRoundId.toString(),
            results: winner
          })
          // 同步状态给业务，将所有玩家的状态改为idle状态
          playerInfo.forEach(player => {
            NotifyPlayerStatus(PlayerType.SEAL_PLAYER_STATUS_IDLE, player.userId.toString(), { playerStatusList: [] })
          });
        }

        if (isAudioFinished) {
          console.info("isAudioFinished," + new Date().getTime())
          cb();
        } else {
          // setTimeout(() => {
          //   cb();
          // }, time * 1000);
          console.info("ChessManager.setEndCallBack" + new Date().getTime())
          ChessManager.setEndCallBack(cb);
        }
      }
    } else {
      // 游戏状态没发生改变
      let copyOldPlayerInfo = globalData.gameInfo.playerInfo.map(item => item);
      if (JSON.stringify(playerInfo) != JSON.stringify(copyOldPlayerInfo)) {
        let addMembers = playerInfo.filter(player => {
          if (!copyOldPlayerInfo.some(p => p.userId.toString() === player.userId.toString())) {
            return player
          }
        })
        let rmMembers = copyOldPlayerInfo.filter(player => {
          if (!playerInfo.some(p => p.userId.toString() === player.userId.toString())) {
            return player
          }
        })
        if (status === GAME_STAGE.JOIN) {
          // this.toggleDebugModal(playerInfo.length === 0 ? DebugGameStatus.Ready : DebugGameStatus.Match)
          if ((gameMode === GAME_MODE.ONE_VS_ONE && playerInfo.length === 2) || (gameMode === GAME_MODE.FOUR_TEAM && playerInfo.length === 4)) {
            NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_READY)
          } else {
            NotifyGameStatus(GameStatusType.SEAL_GAME_STATUS_IDLE)
          }
          // 待加入状态
          rmMembers.length && rmMembers.forEach(player => {
            NotifyPlayerStatus(PlayerType.SEAL_PLAYER_STATUS_IDLE, player.userId.toString(), { playerStatusList: playerInfo })
          })
          addMembers.length && addMembers.forEach(player => {
            NotifyPlayerStatus(PlayerType.SEAL_PLAYER_STATUS_JOINED, player.userId.toString(), { playerStatusList: playerInfo })
          });
        } else if (status == GAME_STAGE.GAMEING) {
          // 游戏中
          rmMembers.length && rmMembers.forEach(player => {
            NotifyPlayerStatus(PlayerType.SEAL_PLAYER_STATUS_IDLE, player.userId.toString(), { playerStatusList: playerInfo })
          })
        }
      }
    }
  }


  /**
   * 处理onmessage事件
   * @param type 
   * @param message 
   */
  private handleGameStage(type: SOCKET_TYPE, message: any) {
    switch (type) {
      case SOCKET_TYPE.GAME_INFO: {
        const { status, currentPlayerUserId, playerInfo, chessInfo, gameMode, endMode, opIndex, userCoinInfo, gameRoundId } = message
        // 最新游戏信息存入全局状态中
        globalData.gameRoundId = gameRoundId
        globalData.gameInfo.gameMode = gameMode;
        globalData.gameInfo.endMode = endMode;
        gameLocalServer.instance.userCoinInfoList = userCoinInfo;
        // 设置全部棋子位置信息
        gameLocalServer.instance.setUseAllChessInfo(chessInfo);
        // 当前操作玩家
        globalData.currentOpUserId = currentPlayerUserId;
        globalData.currentPlayerIndex = playerInfo.find(item => item.userId === globalData.myUserId)?.userIndex || -1;;
        globalData.isMineInGame = playerInfo.some(item => item.userId === globalData.myUserId)
        gameLocalServer.instance.selfOpIdx = globalData.currentPlayerIndex;
        this.handleGameStatus(message)
        Object.assign(globalData.gameInfo, message, { gameStatus: status })
        gameLocalServer.instance.setGameModeData(globalData.gameInfo.modeData)
        ViewManager.updateAllPlayerStatus()
        break
      }
      case SOCKET_TYPE.BROADCAST: {
        const commands = this._commandMap?.get(message?.commandKey)
        commands?.addCommand(message)
        commands?.execCommand()
        break
      }
      default: break
    }
  }
}
