import ActionManager from "./action"
import { getTextureFrame } from "../utils/utils";
import { Component, Node, ParticleSystem2D, Sprite, Texture2D, _decorator } from "cc";
import { notifyDisplayPlayerIcon } from "../seal/bridge";
import { commonUtil } from "../utils/commonUtil";
import { AudioMgr } from "./audioMgr";
import globalData from '../globalData';
import { SOCKET_TYPE } from "../network/constants";
import { cs } from "../framework/log";

const { ccclass, property } = _decorator;

@ccclass("DiceManager")
export default class DiceManager extends Component {
  @property(Node)
  iconDiceNode: Node = null

  @property(Node)
  rollDiceNode: Node = null

  @property(Node)
  sliceNode: Node = null

  @property(ParticleSystem2D)
  bilingShineComp: ParticleSystem2D = null

  @property(Texture2D)
  defaultDiceTexture2D

  @property(Texture2D)
  defaultDiceAnimTexture2D

  private diceTexture2D: Texture2D

  private diceAnimTexture2D: Texture2D

  private diceShineTexture2D: Texture2D

  /**
   * @zh 设置骰子皮肤 @en set dice skins included dice、 diceAnim and shineAnim
   */
  public setCustomDiceSkin(diceTexture: Texture2D, diceAnimTexture?: Texture2D, diceShineTexture?: Texture2D) {
    this.diceTexture2D = diceTexture
    this.diceAnimTexture2D = diceAnimTexture
    this.diceShineTexture2D = diceShineTexture
  }

  /**
   * @description 设置骰子视图
   */
  public async setDiceView(diceNum: number, isShine?: boolean) {
    if (+diceNum === 6 && isShine) {
      if (this.diceShineTexture2D) {
        notifyDisplayPlayerIcon(this.node.name, false)
        commonUtil.setActive(this.sliceNode, true)
        await ActionManager.playSpriteFrameAnimation(
          this.sliceNode,
          this.diceShineTexture2D,
          220,
          1000 / 16,
          1
        )
        notifyDisplayPlayerIcon(this.node.name, true)
        setTimeout(() => {
          commonUtil.setActive(this.sliceNode, false)
        }, 200)
      } else {
        this.bilingShineComp.resetSystem()
      }
    }
    this.iconDiceNode.getComponent(Sprite).spriteFrame = getTextureFrame(this.diceTexture2D || this.defaultDiceTexture2D, diceNum * 112, 112, 112)
  }

  /**
   * @name 播放骰子动画
   * @param chessType 棋子类型
   * @returns 
   */
  public playDiceResult(diceNum: number) {
    return new Promise(async (reslove) => {
      commonUtil.setActive(this.sliceNode, false)
      commonUtil.setActive(this.iconDiceNode, false)
      AudioMgr.instance.play('audio/sound_useDice')
      this.rollDiceNode.getComponent(Sprite).spriteFrame = null
      commonUtil.setActive(this.rollDiceNode, true)
      let fps = 1000 / 40;
      let playTime = 2;
      await ActionManager.playSpriteFrameAnimation1(this.rollDiceNode,
        this.diceAnimTexture2D || this.defaultDiceAnimTexture2D,
        112,
        fps,
        playTime)
      if (diceNum == 6) {
        AudioMgr.instance.play('audio/sound_rock6');
      }
      commonUtil.setActive(this.rollDiceNode, false)
      commonUtil.setActive(this.iconDiceNode, true)
      this.setDiceView(diceNum, true)
      reslove('')
    })
  }


  /** 当前摇骰子的次数 */
  private curDiceNum: number = 0;

  /**
   * @name 立刻播放骰子动画
   * @returns 
   */
  public nowPlayDiceResult() {
    return new Promise(async (reslove) => {
      commonUtil.setActive(this.sliceNode, false)
      commonUtil.setActive(this.iconDiceNode, false)
      AudioMgr.instance.play('audio/sound_useDice')
      this.rollDiceNode.getComponent(Sprite).spriteFrame = null
      commonUtil.setActive(this.rollDiceNode, true)
      let fps = 1000 / 40;
      let playTime = 2;
      this.curDiceNum++;
      await ActionManager.playSpriteFrameAnimation1(this.rollDiceNode,
        this.diceAnimTexture2D || this.defaultDiceAnimTexture2D,
        112, fps, playTime);
      if (this.curDiceNum > 3) {
        this.curDiceNum = 0;
        // this.setDiceView(0);
        this.showDiceResult(0);
        reslove('');
      } else {
        if (globalData.diceResultInfo) {
          this.showDiceResult(globalData.diceResultInfo);
          this.curDiceNum = 0;
          reslove('');
        } else {
          this.nowPlayDiceResult();
        }
      }
    })
  }

  /**
   * 展示骰子结果
   * @param diceNum 
   */
  private showDiceResult(diceNum: number) {
    if (diceNum == 6) {
      AudioMgr.instance.play('audio/sound_rock6');
    }
    commonUtil.setActive(this.rollDiceNode, false);
    commonUtil.setActive(this.iconDiceNode, true);
    this.setDiceView(diceNum, true);
    globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand: { commandKey: 'finishDiceMoving' } })
    globalData.canTouchDice = true;
    globalData.diceResultInfo = null;
  }


}
