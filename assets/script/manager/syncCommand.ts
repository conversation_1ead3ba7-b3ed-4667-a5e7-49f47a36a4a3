import globalData from "../globalData"
import ChessManager from "../manager/chess"
import ViewManager from "../manager/view"
import { ACTIONS, SOCKET_TYPE } from "../network/constants"
import { notifyBustPlayerResult } from "../seal/bridge"
import { PlayerOpStatus, PlayerStatus } from "../type"
import { sensorResult } from "../utils/sensor"
import { gameLogMgr } from "../manager/gameLogMgr"
import { GameOverMgr } from "../manager/gameOverMgr"
import { Utils } from "../framework/frameworkUtils"
import { AudioMgr } from "./audioMgr"
import { gameLocalServer } from "./gameLocalServer"
import { MessageQueue } from "./MessageQueue"
import { GameModeMgr } from "./gameModeMgr"
import Toast from "../components/toast"
import { EventMgr } from "../framework/eventMgr"
import { EventName } from "../framework/eventName"

/**
 * 同步指令队列队列处理
 */
interface ICommandMessage {
  commandKey: ACTIONS,
  gameData: Record<string, any>,
  callbackData?: any,
}

export default class SyncCommand {
  private _isSync = false
  private _commandQueue: any[] = [] // 指令队列

  /**
   * @zh 新增指令函数 @en add new command with function
   */
  public addCommand(data: any) {
    this._commandQueue.push(data)
  }

  /**
   * @zh 执行最新状态指令 @en exec new command
   */
  public async execCommand() {
    if (!this._isSync && this._commandQueue.length) {
      this._execCommand(this._commandQueue.pop(), this._commandQueue.pop())
      this._commandQueue = []
    }
  }

  private async _execCommand(message: ICommandMessage, lastMessage?: ICommandMessage | undefined) {
    this._isSync = true
    const { commandKey, gameData } = message
    const { opIndex } = gameData;
    if (opIndex && opIndex > 0) gameLocalServer.instance.extraOpIdx = opIndex;
    switch (commandKey) {
      case ACTIONS.NOTIFY_PLAYER_DICE: {
        await this._handlePlayerDiceCommand(gameData, message)
        break
      }
      case ACTIONS.NOTIFY_DICE_RESULT: {
        gameLogMgr.instance.handleDateToLog(message.callbackData)
        const { callbackData } = message;
        await this._handleDiceResultCommand(gameData, callbackData, message)
        break
      }
      case ACTIONS.NOTIFY_MOVE_SELECT: {
        await this._handleSelectionCommand(gameData, message)
        break
      }
      case ACTIONS.NOTIFY_CHESS_MOVE: {
        // 设置全部棋子位置信息
        const { finalChesses } = gameData;
        gameLocalServer.instance.setUseAllChessInfo(finalChesses);
        //
        gameLogMgr.instance.handleDateToLog(message.callbackData)
        await this._handleChessMoveCommand(gameData, lastMessage?.gameData, message)
        break
      }
      case ACTIONS.NOTIFY_PLAYERSTATUS_UPDATE: {
        await this._handlePlayerViewCommand(gameData)
        break
      }
      case ACTIONS.NOTIFY_RESET_INFO: {
        await this._handleResetInfoCommand(gameData)
        break
      }
      case ACTIONS.NOTIFY_BUST_PLAYER_RESULT: {
        // console.log("玩家破产", JSON.stringify(coinInfo))
        GameOverMgr.instance.openGameOver(gameData.coinInfo)
        notifyBustPlayerResult()
        break
      }
      case ACTIONS.NOTIFY_RESET_COUNTDOWN: {
        await this._handleResetCountdownCommand(gameData)
        break
      }
      default: break
    }
    this._isSync = false
    this.execCommand()
  }

  /**
   * @zh 处理展示骰子 @en display dice view
   */
  private async _handlePlayerDiceCommand(gameData, message) {
    const { opUserId, opIndex, countDown, diceNumArray, status } = gameData
    globalData.currentOpUserId = opUserId
    globalData.currentPlayerIndex = opIndex
    globalData.playerOpStatus = PlayerOpStatus.Other

    const { commandKey, userRound, round } = message;
    MessageQueue.enqueueMessage(commandKey, userRound, round);

    // ViewManager.checkIsSelfRound();

    // 本轮第一次摇骰子时提醒音效
    if (diceNumArray.length === 0) {
      if (globalData.isMyRound) {
        AudioMgr.instance.play('audio/sound_iMove')
      } else {
        AudioMgr.instance.play('audio/sound_otherMove')
      }
    }
    ViewManager.notifyPlayerOpDice(opUserId, opIndex, countDown * 1000, diceNumArray)
    if (status == PlayerStatus.Hosting && globalData.isMyRound) {
      ViewManager.notifyResetDiceCountdown(opIndex, 0)
    }
    ViewManager.notifyPlayerDiceList(opIndex, diceNumArray)
    if (opUserId == globalData.myUserId && status != PlayerStatus.Hosting) {
      globalData.noticeDice = true;
    }
    return
  }

  /**
   * @zh 处理展示摇骰子结果 @en display dice view
   */
  private async _handleDiceResultCommand(gameData, callbackData, message) {
    const { opUserId, opIndex, diceNum, diceNumArray, status } = gameData;
    globalData.gameHosting = status == PlayerStatus.Hosting ? true : false;
    const { preCmdKey } = callbackData;
    globalData.diceResultInfo = diceNum;
    globalData.diceList = diceNumArray;
    if (opUserId == globalData.myUserId && !globalData.gameHosting && !(preCmdKey == "resetDiceNum")) {
    } else {
      const awaitTime = 100 + (diceNum === 6 ? 500 : 0) + (globalData.isAllChessInHome(opIndex) ? 460 : 0)
      globalData.currentOpUserId = opUserId;
      globalData.currentPlayerIndex = opIndex;
      globalData.playerOpStatus = PlayerOpStatus.Roll
      // ViewManager.checkIsSelfRound();

      globalData.diceResultInfo = null;
      globalData.diceList = [];

      const { commandKey, userRound, round } = message;
      MessageQueue.enqueueMessage(commandKey, userRound, round);

      ViewManager.closeDiceWave()
      ViewManager.closeDiceDialog()
      ViewManager.closePlayerCountdownEffect()
      if (!globalData.isPlaying && !globalData.isMyRound) {
        // console.log("--------------处理展示摇骰子结果")
        ChessManager.scaleMulChess(globalData.gameInfo?.chessInfo)
      }
      await ViewManager.notifyDiceResult(opUserId, opIndex, diceNum, diceNumArray)

      globalData.playerOpStatus = PlayerOpStatus.Other
      if (globalData.isMyRound) {
        await Utils.timeOut1(awaitTime)
        globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand: { commandKey: 'finishDiceMoving' } })
        globalData.noticeDice = diceNum === 6
      } else {
        globalData.noticeDice = false
      }
      return
    }
  }

  /**
   * @zh 处理选择棋子 @en handle chess selection
   */
  private async _handleSelectionCommand(gameData, message) {
    const { opUserId, opIndex, diceNumArray, canMoveChesses, countDown } = gameData
    globalData.currentOpUserId = opUserId
    globalData.currentPlayerIndex = opIndex
    globalData.playerOpStatus = PlayerOpStatus.Select
  //  ViewManager.checkIsSelfRound();
    const { commandKey, userRound, round } = message;
    MessageQueue.enqueueMessage(commandKey, userRound, round);

    gameLocalServer.instance.opIndex = opIndex;
    gameLocalServer.instance.diceNumArray = diceNumArray;

    // ViewManager.checkIsSelfRound();

    let canMoveChesses2 = canMoveChesses.map(item => [item.chessId, item.canDiceNum])
    let canMoveChessMap: Map<string, number[]> = new Map(canMoveChesses2)
    ViewManager.notifyPlayerDiceList(opIndex, diceNumArray)
    if (globalData.isMyRound) ViewManager.notifyPlayerSelectDice(canMoveChessMap)
    ViewManager.showPlayerCountdownEffect(opIndex, countDown * 1000)
    return
  }

  /**
   * @zh 处理棋子移动 @en handle chess move
   */
  private async _handleChessMoveCommand(gameData, lastMessage, message) {
    const { opUserId, opIndex, chessId, movePath, eatOff, finalChesses, restDiceNumArray, obstacleInfo,
      userCoinInfo, roundEnd, status, eatChessFlag, teamName, reachDestCost, teammateStatus, extraData } = gameData
    gameLocalServer.instance.userCoinInfoList = userCoinInfo
    if (extraData?.combo) gameLocalServer.instance.setUserComboValue(opIndex, extraData.combo)
    if (extraData?.userExp) gameLocalServer.instance.setUserExpValue(opIndex, extraData.userExp)
    if (extraData?.userIndex1st) gameLocalServer.instance.setUserIndex1st(extraData.userIndex1st)
    globalData.currentOpUserId = opUserId;
    globalData.currentPlayerIndex = opIndex;
    globalData.playerOpStatus = PlayerOpStatus.Move;
    if (extraData?.exp === -1 && opUserId === globalData.myUserId) {
      console.log("====提醒啊")
      Toast.show(window.cocosI18n?.expTip || 'تم الحصول اليوم على (خبرة) مكافأة أكل القطعة ', 2000)
    }
    // ViewManager.checkIsSelfRound();

    const { commandKey, userRound, round } = message;
    MessageQueue.enqueueMessage(commandKey, userRound, round);

    gameLocalServer.instance.obstacleInfo = obstacleInfo;
    if (opUserId == globalData.myUserId && status != PlayerStatus.Hosting && gameLocalServer.instance.isCanGameMode()) {
      EventMgr.dispatchEvent(EventName.EVENT_PLAYER_RENDER_1ST, extraData?.userIndex1st)
    } else {
      // globalData.currentOpUserId = opUserId;
      // globalData.currentPlayerIndex = opIndex;
      // globalData.playerOpStatus = PlayerOpStatus.Move;
      ViewManager.closePlayerCountdownEffect();
      ViewManager.closeDiceWave();
      if (lastMessage?.finalChesses?.length) {
        // 走棋前先恢复正确的位置
        if (!globalData.isPlaying) ChessManager.scaleMulChess(lastMessage.finalChesses)
        await Utils.timeOut1(50)
      }
      if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
        console.error("server====chessMove====", chessId, eatOff?.length, opIndex, JSON.stringify(extraData))
        await ViewManager.classicChessMove(chessId, movePath, eatOff, finalChesses, obstacleInfo, extraData, opIndex, restDiceNumArray, roundEnd, teamName, reachDestCost, teammateStatus);
      } else {
        await ViewManager.chessMove(chessId, movePath, eatOff, finalChesses, obstacleInfo, userCoinInfo,
          opIndex, restDiceNumArray, roundEnd, teamName, reachDestCost, teammateStatus);
      }
      if (userCoinInfo && userCoinInfo.length > 0) globalData.gameInfo.userCoinInfo = userCoinInfo
      return
    }
  }

  /**
   * @zh 处理玩家状态视图指令 @en handle player status
   */
  private async _handlePlayerViewCommand(gameData) {
    const { userId, opIndex, status, autoScene, userCoinInfo, finalAllChesses } = gameData
    if (userId == globalData.myUserId) {
      globalData.gameHosting = status == PlayerStatus.Hosting ? true : false;
    }
    if (Number(autoScene) > 0) {
      sensorResult({
        result_type: 'LudoTrusteeship',
        page_business_type: autoScene
      })
    }
    // console.log("服务端playPlayerStatus=====", status)
    ViewManager.notifyPlayerStatus(opIndex, status);
    if (status === PlayerStatus.Quit) {
      if (userCoinInfo) ViewManager.playPlayerBalanceEffect(userCoinInfo)
      // TODO 更新金币  更新标识
      finalAllChesses.forEach(element => {
        ViewManager.rediscoverPhotography(element?.cid);
      });
    }
    globalData.gameInfo.playerInfo.forEach(item => {
      if (item.userId == userId) {
        item.status = status
      }
    })
    return
  }

  /**
   * @zh 处理重置信息 @en handle reset info
   */
  private async _handleResetInfoCommand(gameData) {
    const { opIndex, freeCount, price, resetType } = gameData;
    sensorResult({
      result_type: 'LudoResetDice',
      page_business_type: resetType, // free pay
      is_success: 'success',
      element_business_content: Date.now() - globalData.clickResetDiceTime
    })
    ViewManager.notifyPlayerResetBtnView(
      opIndex,
      freeCount > 0 ? 'free' : `${price}`
    )
    return
  }

  /**
   * @zh 处理重置倒计时 @en handle reset countdown 
   */
  private async _handleResetCountdownCommand(gameData) {
    const { opIndex, resetCountDown } = gameData
    if (globalData.isMyRound && resetCountDown > 0) {
      ViewManager.notifyResetDiceCountdown(opIndex, resetCountDown * 1000)
    }
    return
  }

}
