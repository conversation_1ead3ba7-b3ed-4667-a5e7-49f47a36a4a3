import { _decorator, Component, Sprite, Texture2D, Label } from 'cc';
import globalData from '../../globalData';
import ActionManager from '../../manager/action';
import { notifyDisplayPlayerIcon, notifyGameViewState } from '../../seal/bridge';
import { ENativeLayer } from '../../type/enum';
import { getTextureFrame } from '../../utils/utils';
// import { BasePanel } from '../../framework/basePanel';
const { ccclass, property } = _decorator;

@ccclass('Reconnect')
export class Reconnect extends Component {

    private isPauseAnim: boolean = true

    private _isRender = false
 
    @property(Texture2D)
    showTexture2D

    @property(Texture2D)
    connectTexture2D

    @property(Label)
    tipLabal: Label

    async init() {
        if (this._isRender) return
        this.isPauseAnim = false
        this._isRender = true
        globalData.gameInfo?.playerInfo?.map(i => i.userId)?.forEach(userId => {
            notifyDisplayPlayerIcon(userId, false)
        })
        notifyGameViewState(ENativeLayer.BottomArea, false)
        const iconNode = this.node.getChildByName('icon')
        this._playReconnectLabelAnim()
        await ActionManager.playSpriteFrameAnimation(iconNode, this.showTexture2D, 360, 1000 / 24, 1)
        await ActionManager.playSpriteFrameAnimation(iconNode, this.connectTexture2D, 360, 1000 / 24, 10)
        iconNode.getComponent(Sprite).spriteFrame = getTextureFrame(this.connectTexture2D, 360, 360, 360)
    }

    /**
     * 动画关闭
     */
    animClose = () => {
        this._isRender = false
        notifyGameViewState(ENativeLayer.BottomArea, true)
        globalData.gameInfo?.playerInfo?.map(i => i.userId)?.forEach(userId => {
            notifyDisplayPlayerIcon(userId, true)
        })
        this.isPauseAnim = true
        this.node.destroy()
    }

    /**
     * 播放重连文案动画
     */
    private _playReconnectLabelAnim() {
        const callback = (length = 1) => {
            if (this.isPauseAnim) return
            if (length > 3) length = 1
            const desc = Array.from({length}, () => '.').join('')
            this.scheduleOnce(() => {
                if (this.tipLabal) {
                    this.tipLabal.string = (window.cocosI18n?.reconnectingTip || 'Reconnecting') + desc
                }
                callback(++length)
            }, .5)
        }
        callback()
    }
    
}
