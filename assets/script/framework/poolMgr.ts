import { Component, Prefab, Node } from "cc";
import { cs } from "./log";

export enum PoolKey {
    Coin = "Coin",          // 飞金币 ，金币界面
}

export enum PoolLen {
    "Coin" = 16,            // 飞金币的长度
}

/**
 * 采用简单的对象池来处理
 * 对象池
 * <AUTHOR>
 */
export class PoolMgr {
    private static _ins: PoolMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new PoolMgr();
        return this._ins;
    }

    private pool: { [name: string]: any } = {};

    public init() {
        this.pool = {};
        this.pool[PoolKey.Coin] = [];
    }


    public push(key: string, value: Node | Prefab) {
        if (this.pool[key].length > PoolLen[key]) {
            cs.log("Maximum length exceeded");
            return;
        }
        this.pool[key].push(value);
    }

    public get(key: string): Node {
        if (this.pool[key].length <= 0) {
            return null;
        }
        return this.pool[key].shift();
    }


    public clearAll() {
        for (const key in this.pool) {
            if (Object.prototype.hasOwnProperty.call(this.pool, key)) {
                const element = this.pool[key];
                for (let i = 0; i < element.length; i++) {
                    element[i].destroy();
                }
                this.pool[key] = [];
            }
        }
        this.pool = {};
    }

}