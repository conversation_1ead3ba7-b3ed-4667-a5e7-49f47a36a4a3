
import { Prefab, instantiate, director, Node } from "cc"
import globalData from "../globalData"
import { GameOverMgr } from "../manager/gameOverMgr"
import ViewManager from "../manager/view"
import { END_MODE, GAME_MODE, GAME_STAGE } from "../network/constants"
import { sealBridge } from "../seal/SealBridge"
import { DebugGameStatus, Scene } from "../type/index"
import { getUrlParam1, isInScene, loadLoaclResource } from "../utils/utils"
import { testGameOver } from "./gameOver"

export default function debug<T extends { new(...args: any[]): {} }>(constructor: T) {
  const preOnload = constructor.prototype.onLoad
  const testData = {
    "roundEnd": false,
    "eatOff": ["2-1"],
    "movePath": [15, 16],
    "eatChessFlag": true,
    "obstacleInfo": [110, 120],
    "restDiceNumArray": [],
    "finalChesses": [{
      "chessId": "1-1",
      "chessIndex": 14
    }, {
      "chessId": "1-2",
      "chessIndex": 1
    }, {
      "chessId": "1-3",
      "chessIndex": 0
    }, {
      "chessId": "1-4",
      "chessIndex": 0
    }, {
      "chessId": "2-1",
      "chessIndex": 0
    }, {
      "chessId": "2-2",
      "chessIndex": 0
    }, {
      "chessId": "2-3",
      "chessIndex": 0
    }, {
      "chessId": "2-4",
      "chessIndex": 14
    }, {
      "chessId": "3-1",
      "chessIndex": 36
    }, {
      "chessId": "3-2",
      "chessIndex": 27
    }, {
      "chessId": "3-3",
      "chessIndex": 0
    }, {
      "chessId": "3-4",
      "chessIndex": 0
    }, {
      "chessId": "4-1",
      "chessIndex": 45
    }, {
      "chessId": "4-2",
      "chessIndex": 42
    }, {
      "chessId": "4-3",
      "chessIndex": 41
    }, {
      "chessId": "4-4",
      "chessIndex": 41
    }],
    "userCoinInfo": [{
      "isEatenChessNum": 0,
      "userIndex": 1,
      "multiple": 1,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2020/05/26/2807309702636112386.jpg",
      "userId": "5113152715523891756",
      "balance": 0,
      "eatChessNum": 1,
      "teamId": 5288321675268461695,
      "name": "1256🙉",
      "antes": 10,
      "coin": 50,
      "teamName": "A"
    }, {
      "isEatenChessNum": 5,
      "userIndex": 2,
      "multiple": 5,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2020/12/29/2847574727060536322.png",
      "userId": "5237594338376025644",
      "balance": -10,
      "eatChessNum": 0,
      "teamId": 5766278482510254080,
      "name": "funbox8050",
      "antes": -10,
      "coin": 99991225,
      "teamName": "B"
    }, {
      "isEatenChessNum": 0,
      "userIndex": 4,
      "multiple": 5,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2022/05/16/2940891298963727362.jpg",
      "userId": "5236517478397395500",
      "balance": 10,
      "eatChessNum": 5,
      "teamId": 5766278482510254080,
      "name": "8048",
      "antes": 10,
      "coin": 65866,
      "teamName": "B"
    }, {
      "isEatenChessNum": 1,
      "userIndex": 3,
      "multiple": 1,
      "portraitMask": "",
      "portrait": "https://cdnoffice.lizhi.fm/user/2020/05/19/2805997298069438978.jpg",
      "userId": "5111840305364163756",
      "balance": 0,
      "eatChessNum": 2,
      "teamId": 5288321675268461695,
      "name": "123456756",
      "antes": 10,
      "coin": 40,
      "teamName": "A"
    }],
    "opIndex": 1,
    "opUserId": "5113152715523891756",
    "chessId": "1-1"
  };

  const initView = () => {
    // name: "ال مقدار اكل الفائز",
    //  " مقدار اكل الفائز"  
    // name: " مقدار اكل الفائز",
    // " مقدار اكل الفائز",
    let playerList = [
      { userId: "5113152715523891756", teamName: "A", name: "jungo", portrait: 'https://cdnoffice.lizhi.fm/user/2020/05/26/2807309702636112386.jpg', userIndex: 1, isAuto: false, portraitMask: "", status: 5 },
      { userId: "5237594338376025644", teamName: "B", name: "ال مقدار اكل الفائز", portrait: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSOdn9kgri-0dtvdcyU1F0vnSj1sUhCMtg75A&usqp=CAU", userIndex: 3, isAuto: false, portraitMask: "", status: 3 },
      { userId: "5236517478397395500", teamName: "B", name: "ال مقدار اكل الفائز", portrait: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSsAaUQqpRQ1hYDzZiceDJt3QiLHFucgzYwCQ&usqp=CAU", userIndex: 4, isAuto: false, portraitMask: "", status: 4 },
      { userId: "5111840305364163756", teamName: "A", name: 'رشاد ابو فارس', portrait: "https://cdnoffice.lizhi.fm/user/2020/05/19/2805997298069438978.jpg", userIndex: 2, isAuto: false, portraitMask: "", status: 2 },
    ]//5073029512386800684
    globalData.gameInfo.gameMode = GAME_MODE.CLASSIC_FOUR_TEAM;
    globalData.gameInfo.endMode = END_MODE.CLASSIC;
    globalData.gameInfo.playerInfo = playerList
    globalData.myUserId = "5113152715523891756";

    const { opUserId, opIndex, chessId, movePath, eatOff, finalChesses, restDiceNumArray, obstacleInfo, userCoinInfo, roundEnd, eatChessFlag } = testData;
    globalData.gameInfo.userCoinInfo = userCoinInfo
    GameOverMgr.instance.coinInfo = [
      {
        "teamName": "B",
        "eatChessChargeRate": 5,
        "actuallyTotalBalance": 43,
        "isEscape": 0,
        "profitFromEscapeBalance": 45,
        "actuallyChargeBalance": 0,
        "winnerEatChess": 2,
        "beEatMultiple": 1,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChessGainBalance": 10,
        "actuallyBeEatChessGainBalance": 10,
        "eatChessBrokeAmount": 0,
        "gameSettleBrokeAmount": 1,
        "escapeBalance": 0,
        "from": 2,
        "chargeBalance": 1,
        "eatChessAntes": 10,
        "gameEscapeUserAmount": 2,
        "eatChessChargeBalance": 0,
        "actuallyEscapeBalance": 0,
        "totalBalance": 62,
        "actuallyProfitFromEscapeBalance": 45,
        "portraitMask": "",
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 10,
        "eatMultiple": 1,
        "isBroke": 0,
        "portrait": "https://cdnoffice.lizhi.fm/user/2022/11/11/2974084028145365506.jpg",
        "userId": "5236743288469332012",
        "eatChess": 1,
        "actuallyWinnerSettleAmountBroke": 0,
        "winnerEatMultiple": 2,
        "chargeRate": 5,
        "isWinner": 2,
        "eatChessActuallyChargeBalance": 0,
        "eatChessGainBalance": 10,
        "beEatChess": 1,
        "name": "0002",
        "winnerSettleAmount": 20
      },
      {
        "teamName": "B",
        "eatChessChargeRate": 5,
        "actuallyTotalBalance": 0,
        "isEscape": 0,
        "profitFromEscapeBalance": 0,
        "actuallyChargeBalance": 0,
        "winnerEatChess": 2,
        "beEatMultiple": 1,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChessGainBalance": 10,
        "actuallyBeEatChessGainBalance": 10,
        "eatChessBrokeAmount": 0,
        "gameSettleBrokeAmount": 0,
        "escapeBalance": 0,
        "from": 2,
        "chargeBalance": 0,
        "eatChessAntes": 10,
        "gameEscapeUserAmount": 0,
        "eatChessChargeBalance": 0,
        "actuallyEscapeBalance": 0,
        "totalBalance": 0,
        "actuallyProfitFromEscapeBalance": 0,
        "portraitMask": "https://cdnoffice.lizhi.fm/sociality/emotion/2020/06/16/2811205792253231676.zip",
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 10,
        "eatMultiple": 1,
        "isBroke": 1,
        "portrait": "https://cdnoffice.lizhi.fm/user/2022/04/25/2936990274658939394.jpg",
        "userId": "5240208042865199148",
        "eatChess": 1,
        "actuallyWinnerSettleAmountBroke": 0,
        "winnerEatMultiple": 2,
        "chargeRate": 0,
        "isWinner": 2,
        "eatChessActuallyChargeBalance": 0,
        "eatChessGainBalance": 10,
        "beEatChess": 1,
        "name": "0005",
        "winnerSettleAmount": 0
      },
      {
        "teamName": "A",
        "eatChessChargeRate": 5,
        "actuallyTotalBalance": -5,
        "isEscape": 1,
        "profitFromEscapeBalance": 15,
        "actuallyChargeBalance": 0,
        "winnerEatChess": 2,
        "beEatMultiple": 1,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChessGainBalance": 10,
        "actuallyBeEatChessGainBalance": 10,
        "eatChessBrokeAmount": 0,
        "gameSettleBrokeAmount": 0,
        "escapeBalance": 30,
        "from": 2,
        "chargeBalance": 0,
        "eatChessAntes": 10,
        "gameEscapeUserAmount": 1,
        "eatChessChargeBalance": 1,
        "actuallyEscapeBalance": 30,
        "totalBalance": -6,
        "actuallyProfitFromEscapeBalance": 15,
        "portraitMask": "https://cdnoffice.lizhi.fm/sociality/emotion/2021/07/20/2885235970280895036.svga",
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 20,
        "eatMultiple": 2,
        "isBroke": 1,
        "portrait": "https://cdnoffice.lizhi.fm/user/2022/06/08/2945171032012008962.jpg",
        "userId": "5236743288469349932",
        "eatChess": 2,
        "actuallyWinnerSettleAmountBroke": 0,
        "winnerEatMultiple": 2,
        "chargeRate": 0,
        "isWinner": 1,
        "eatChessActuallyChargeBalance": 0,
        "eatChessGainBalance": 20,
        "beEatChess": 1,
        "name": "znnm",
        "winnerSettleAmount": 0
      },
      {
        "teamName": "A",
        "eatChessChargeRate": 5,
        "actuallyTotalBalance": -40,
        "isEscape": 1,
        "profitFromEscapeBalance": 0,
        "actuallyChargeBalance": 0,
        "winnerEatChess": 2,
        "beEatMultiple": 1,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChessGainBalance": 10,
        "actuallyBeEatChessGainBalance": 10,
        "eatChessBrokeAmount": 0,
        "gameSettleBrokeAmount": 0,
        "escapeBalance": 30,
        "from": 2,
        "chargeBalance": 0,
        "eatChessAntes": 10,
        "gameEscapeUserAmount": 0,
        "eatChessChargeBalance": 0,
        "actuallyEscapeBalance": 30,
        "totalBalance": -40,
        "actuallyProfitFromEscapeBalance": 0,
        "portraitMask": "",
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 0,
        "eatMultiple": 0,
        "isBroke": 1,
        "portrait": "https://cdnoffice.lizhi.fm/user/2019/04/02/2729371800153756674.jpg",
        "userId": "5270476590814331436",
        "eatChess": 0,
        "actuallyWinnerSettleAmountBroke": 0,
        "winnerEatMultiple": 2,
        "chargeRate": 0,
        "isWinner": 1,
        "eatChessActuallyChargeBalance": 0,
        "eatChessGainBalance": 0,
        "beEatChess": 1,
        "name": "user6211682",
        "winnerSettleAmount": 0
      }
    ]
    const finalChessDatas = '[{"chessId":"1-1","chessIndex": 0},{"chessId":"1-2","chessIndex":45},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":115},{"chessId":"2-2","chessIndex":115},{"chessId":"2-3","chessIndex":115},{"chessId":"2-4","chessIndex":115},{"chessId":"3-1","chessIndex":125},{"chessId":"3-2","chessIndex":125},{"chessId":"3-3","chessIndex":125},{"chessId":"3-4","chessIndex":125},{"chessId":"4-1","chessIndex":0},{"chessId":"4-2","chessIndex":135},{"chessId":"4-3","chessIndex":135},{"chessId":"4-4","chessIndex":135}]'
    const chessDatas = `[{"chessId":"1-1","chessIndex":14},{"chessId":"1-2","chessIndex":20},{"chessId":"1-3","chessIndex":0},{"chessId":"1-4","chessIndex":0},{"chessId":"2-1","chessIndex":14},{"chessId":"2-2","chessIndex":0},{"chessId":"2-3","chessIndex":0},{"chessId":"2-4","chessIndex":0},{"chessId":"3-1","chessIndex":0},{"chessId":"3-2","chessIndex":0},{"chessId":"3-3","chessIndex":0},{"chessId":"3-4","chessIndex":0},{"chessId":"4-1","chessIndex":0},{"chessId":"4-2","chessIndex":0},{"chessId":"4-3","chessIndex":0},{"chessId":"4-4","chessIndex":0}]`

    setTimeout(() => {
      ViewManager.initUiInfo(3, playerList, JSON.parse(chessDatas), 2, [100, 110])
    }, 1000)
    setTimeout(async () => {
      // ViewManager.notifyPlayerStatus(1, PlayerStatus.Hosting)
      // 显示可以移动棋子的波纹效果
      let canMoveChesses: any = [
        ['2-1', [6, 5]],
        ['2-2', [6]],
        ['2-3', [6]],
        ['2-4', [6]],
      ];
      globalData.canMoveChessMap = canMoveChesses
      let canMoveChessMap: Map<string, number[]> = new Map(canMoveChesses);
      ViewManager.notifyPlayerSelectDice(canMoveChessMap)

      const { opUserId, opIndex, chessId, movePath, eatOff, finalChesses, restDiceNumArray, obstacleInfo, userCoinInfo, roundEnd, eatChessFlag } = testData;

      ViewManager.chessMove(chessId, movePath, eatOff, finalChesses, obstacleInfo, userCoinInfo, { exp: 30, combo: 5, userIndex1st: 3 }, opIndex, restDiceNumArray, roundEnd);
      // const manager = new GameInterFaceManager
      // GameOverMgr.instance.openGameOver(GameOverMgr.instance.coinInfo)
      // ViewManager.chessMove('1-2', [45, 46], ['1-1'], JSON.parse(finalChessDatas), [100, 110], [{
      //   "isEatenChessNum": 0,
      //   "teamName": "A",
      //   "userIndex": 1,
      //   "multiple": 1,
      //   "portraitMask": "",
      //   "portrait": "https://cdnoffice.lizhi.fm/user/2020/05/26/2807309702636112386.jpg",
      //   "userId": "5113152715523891756",
      //   "balance": 10,
      //   "eatChessNum": 1,
      //   "teamId": 5288321675268461695,
      //   "name": "1256🙉",
      //   "antes": 10,
      //   "coin": 50
      // }, {
      //   "isEatenChessNum": 1,
      //   "teamName": "A",
      //   "userIndex": 3,
      //   "multiple": 1,
      //   "portraitMask": "",
      //   "portrait": "https://cdnoffice.lizhi.fm/user/2020/05/19/2805997298069438978.jpg",
      //   "userId": "5111840305364163756",
      //   "balance": 0,
      //   "eatChessNum": 0,
      //   "teamId": 5288321675268461695,
      //   "name": "123456756",
      //   "antes": 10,
      //   "coin": 40
      // }, {
      //   "isEatenChessNum": 5,
      //   "teamName": "B",
      //   "userIndex": 2,
      //   "multiple": 5,
      //   "portraitMask": "",
      //   "portrait": "https://cdnoffice.lizhi.fm/user/2020/12/29/2847574727060536322.png",
      //   "userId": "5237594338376025644",
      //   "balance": -10,
      //   "eatChessNum": 0,
      //   "teamId": 5766278482510254080,
      //   "name": "funbox8050",
      //   "antes": 10,
      //   "coin": 99991225
      // }, {
      //   "isEatenChessNum": 0,
      //   "teamName": "B",
      //   "userIndex": 4,
      //   "multiple": 5,
      //   "portraitMask": "",
      //   "portrait": "https://cdnoffice.lizhi.fm/user/2022/05/16/2940891298963727362.jpg",
      //   "userId": "5236517478397395500",
      //   "balance": 0,
      //   "eatChessNum": 5,
      //   "teamId": 5766278482510254080,
      //   "name": "8048",
      //   "antes": 10,
      //   "coin": 65866
      // }])
      // GameOverMgr.instance.openGameOver(globalData.gameInfo.userCoinInfo ?? [])
      // ViewManager.chessMove('1-2', [45], ['4-1'], JSON.parse(finalChessDatas), [100, 110], [
      //   { antes: 500, multiple: 1, userIndex: 1, userId: '111', eatChessNum: 2, coin: 2222, balance: -500 },
      //   { antes: 500, multiple: 13, userIndex: 3, userId: '333', eatChessNum: 20, coin: 2222, balance: 500 },
      // ])

      // ViewManager.baseComponentManager?.playNumEffectAnimation('111', `+${600}`, `+${5000}`,`${200}`)
      // NotifyUpdateGameSkin()
      // ViewManager.notifyDiceResult(1, 1, 6, [6, 3])
      // ViewManager.notifyPlayerOpDice('111', 1, 5 * 1000, [6, 3])
      // ViewManager.notifyResetDiceCountdown(1, 0)
      // ViewManager.notifyPlayerDiceList(1, [6, 3])
      // ViewManager.notifyResetDiceCountdown(1, 5 * 1000)
      // setTimeout(() => {
      //   ViewManager.notifyDiceResult('1', 1, 6, [6, 3])
      // }, 3000)
    }, 10000)
  }


  let debugNode: Node

  constructor.prototype.onLoad = function () {
    preOnload?.call(this)
    if (getUrlParam1("scene") && !isInScene(Scene.Game)) return
    switch (getUrlParam1('debug')) {
      case 'true': {
        loadLoaclResource('prefab/debug', Prefab)
          .then((debugPrefab: Prefab) => {
            debugNode = instantiate(debugPrefab)
            debugNode.parent = this.node
            if ([GAME_STAGE.JOIN, GAME_STAGE.INIT].includes(globalData.gameInfo.gameStatus) && globalData.gameInfo?.playerInfo?.length === 0) {
              this.toggleDebugModal(DebugGameStatus.Ready)
            } else if ([GAME_STAGE.GAMEING].includes(globalData.gameInfo.gameStatus)) {
              this.toggleDebugModal(DebugGameStatus.Gaming)
            } else {
              this.toggleDebugModal(DebugGameStatus.Match)
            }
          })
        break
      }
      case 'test': {
        initView()
        break
      }
    }
  }

  constructor.prototype.toggleDebugModal = function (gameState?: DebugGameStatus) {
    if (!debugNode) return
    if (sealBridge.debug) {
      // 待加入状态，显示待加入页面、更新玩家列表信息
      switch (gameState) {
        case DebugGameStatus.Match: {
          debugNode.active = true
          debugNode.getChildByName('ReadyModal').active = false
          debugNode.getChildByName('MatchingModal').active = true
          director.emit('updateMatchingModalUI')
          break
        }
        case DebugGameStatus.Ready: {
          debugNode.active = true
          debugNode.getChildByName('ReadyModal').active = true
          debugNode.getChildByName('MatchingModal').active = false
          break
        }
        case DebugGameStatus.Gaming:
        default: {
          debugNode.active = false
        }
      }
    } else {
      debugNode.active = false
    }
  }

}




