import { cs } from "../framework/log";
import globalData from "../globalData";
import { rdsTraceReport } from "../seal/bridge";
import { sealBridge } from "../seal/SealBridge";

interface ILogType {
    gameLogId: string,      // log id
    operateTime: number,    // 开始的时间戳
}

export enum LogSendType {
    ChessMove = 1,
    SelectDice = 2
}
/**
 * 游戏log
 */
export class gameLogMgr {
    private static _ins: gameLogMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new gameLogMgr();
        return this._ins;
    }
    private _id: number = 0;

    public clear() {
        this._id = 0;
    }

    public increasingId() {
        this._id++;
    }

    public get id() {
        return this._id;
    }

    /**
     * 获取当前信息
     * @returns 
     */
    public getCurIdAndTime(type: LogSendType): ILogType {
        let time = new Date().getTime();
        this.increasingId();
        return {
            gameLogId: globalData.gameRoundId + "_" + this.id + "_" + type + "_" + globalData.userId,
            operateTime: time
        }
    }

    public handleDateToLog(data: any) {
        if (!data) return;
        const { gameLogId, operateTime, serverCostTime, commandKey, receiveTime, sendTime } = data;
        if (!gameLogId) return;
        const userId = gameLogId?.split("_")[3];
        if (String(userId) != String(globalData.userId)) return;
        let time = new Date().getTime();
        let allTime = time - operateTime;
        let first = receiveTime - operateTime;
        let two = time - sendTime;
        let serverTime = sendTime - serverCostTime;
        sealBridge.log(JSON.stringify({
            gameLogId, allTime, first, two, serverTime
        }));
    }

    //  针对于棋子移动操作的日志
    public curTouchTime: number = 0;

    public setTouchTime() {
        this.curTouchTime = new Date().getTime();
    }

    public sumbitLog() {
        if (this.curTouchTime == 0) return;
        const costTime = new Date().getTime() - this.curTouchTime;
        rdsTraceReport('EVENT_GAME_USER_DICEMOVE', {
            costTime: costTime,
        })
        this.curTouchTime = 0;
    }

    public selectChessTime: number = 0;

    public setSelectTime() {
        this.selectChessTime = new Date().getTime();
    }

    public submitChessLog() {
        if (this.selectChessTime == 0) return;
        const costTime = new Date().getTime() - this.selectChessTime;
        rdsTraceReport('EVENT_GAME_USER_CHESSMOVE', {
            costTime: costTime,
        })
        this.selectChessTime = 0;
    }

}

