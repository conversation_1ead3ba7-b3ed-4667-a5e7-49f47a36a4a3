import { color, Layout, Sprite, SpriteFrame, v3, _decorator } from "cc";
import { BasePanel } from "../../framework/basePanel";
import { EventMgr } from "../../framework/eventMgr";
import { EventName } from "../../framework/eventName";
import { I18n } from "../../framework/i18n";
import { Lab } from "../../framework/lab";
import { cs } from "../../framework/log";
import { IResultCoinInfo } from "../../manager/gameOverMgr";
import { commonUtil } from "../../utils/commonUtil";
import { loadRemoteImg } from "../../utils/utils";

const { ccclass, property } = _decorator;


/**
 * 节点说明
 * winItemOtherBg 针对于胜利的第一个展示情况
 * winItemOtherBg1 针对于失败的item展示
 * bankruptcyArea1 破产展示内容，有吃棋等操作
 * bankruptcyArea2 破产金币为0的tips
 */
@ccclass("settlementItem_2v2")
export default class settlementItem_2v2 extends BasePanel {
    private curNodeState: boolean = false;
    // idx 
    private idx: number = -1;
    //data IResultCoinInfo
    private itemData: IResultCoinInfo = null;
    // 0 win 1 fail 2 bankruptcy  3 新手引导
    private openState: number = 0;
    // 当前item状态 赢 和  输
    private itemState: boolean = false;

    /**
      * itemState 节点状态 boolean 
      * idx 下标 win fail  无论输赢都是 0 展示赢
      * data 数据
      * openState 打开gameover的状态  暂时无用
      */
    init(data: any): void {
        super.init(data);
        cs.log('settlement:', data);
        this.idx = this.myData?.idx;
        this.itemData = this.myData?.data;
        this.openState = this.myData?.openState;
        this.itemState = this.myData?.itemState;
        this.updateUi();
    }

    async updateUi() {
        // let isWin = this.myData?.idx == 0 ? true : false;
        let isWin = this.itemData?.isWinner == 2 ? true : false
        // 如果是破产，展示false
        if (this.openState == 2) isWin = false;
        commonUtil.setHeight(this.view["area"], isWin ? 144 : 120);
        // this.view["area"].height = isWin == true ? 144 : 120;
        this.tools.visible("winBg", isWin);
        this.tools.visible("failBg", !isWin);
        this.tools.visible("right", isWin);
        commonUtil.setColor(this.view["name"], isWin ? color(156, 89, 28, 255) : color(255, 255, 255, 255));
        // this.view["name"].color = isWin == true ? color(156, 89, 28, 255) : color(255, 255, 255, 255);
        // 头像边框

        const portrait = this.itemData?.portrait ?? "";
        let portraitSp = await loadRemoteImg(portrait) as SpriteFrame;
        if (portraitSp) {
            if (this.view["icon"] && portraitSp) commonUtil.setSpriteFrameAndAdapteHeight(this.view["icon"].getComponent(Sprite), portraitSp);
        }
        const portraitMask = this.itemData?.portraitMask ?? "";
        let portraitMaskSp = await loadRemoteImg(portraitMask) as SpriteFrame;
        if (this.view["avatorMask"]) this.view["avatorMask"].getComponent(Sprite).spriteFrame = portraitMaskSp;
        // 昵称和赢输
        let name = this.itemData?.name ?? "";
        let actuallyTotalBalance: number | string = this.itemData?.actuallyTotalBalance ?? 0;
        actuallyTotalBalance = actuallyTotalBalance >= 0 ? (Lab.plus + actuallyTotalBalance) : actuallyTotalBalance;
        if (I18n.languageState == 2) {
            name = name + Lab.space + Lab.space;
        }
        this.tools.text("name", name)
        this.tools.text("score", actuallyTotalBalance);

        // 逃跑 和 破产
        const isEscape = this.itemData?.isEscape == 1 ? true : false;
        if (isEscape) {
            this.tools.visible("escape", isEscape);
            this.tools.visible("bankruptcy", false);
        } else {
            const isBroke = this.itemData?.isBroke > 0 ? true : false;
            this.tools.visible("bankruptcy", isBroke);
            this.tools.visible("escape", false);
        }

        this.tools.visible("gameOver_teamA", this.itemData?.teamName == "A");
        this.tools.visible("gameOver_teamB", this.itemData?.teamName == "B");

        // 内部数据
        this.tools.visible("winArea", false);
        this.tools.visible("winItemOtherBg1", false);
        this.tools.visible("bankruptcyArea1", false);
        this.tools.visible("bankruptcyArea2", false);
        this.updateContentUi();
    }

    /**
     * 更新item下面的内容区域ui
     */
    updateContentUi() {
        switch (this.openState) {
            case 0:  // win fail
            case 1:
            case 3:
                if (this.itemData?.isWinner == 2) {
                    this.updateWinItemShow();
                } else {
                    this.updateFailItemShow();
                }
                break;
            case 2: // 破产
                this.updateBankruptcyItemShow();
                break;
            default:
                break;
        }
    }

    private updateWinItemShow() {
        //破产
        const isEscape = this.itemData?.isEscape == 1 ? true : false;
        const isBroke = this.itemData?.isBroke > 0 ? true : false;
        if (isEscape) {
            this.tools.visible("winOut", isEscape);
            this.tools.visible("winBankruptcy", false);
        } else {
            this.tools.visible("winBankruptcy", isBroke);
            this.tools.visible("winOut", false);
        }

        this.tools.visible("winMultiplier", (!isBroke && !isEscape));

        // 破产提示
        const eatChessChargeRate = this.itemData?.eatChessChargeRate ?? 0;
        const gameSettleBrokeAmount = this.itemData?.gameSettleBrokeAmount ?? 0;
        const gameEscapeUserAmount = this.itemData?.gameEscapeUserAmount ?? 0;
        this.tools.visible("winCol2", (gameSettleBrokeAmount > 0 || gameEscapeUserAmount > 0));
        if (gameSettleBrokeAmount > 0 || gameEscapeUserAmount > 0) {
            let bankruptcyNumStr = I18n.str("bankruptcyNum")(gameSettleBrokeAmount, gameEscapeUserAmount);
            this.tools.text("winBankruptcyPeople", bankruptcyNumStr);
            // const actuallyProfitFromEscapeBalance = this.itemData?.actuallyProfitFromEscapeBalance ?? 0;
            let actuallyWinnerSettleAmountBroke = Lab.plus + Number(this.itemData?.actuallyWinnerSettleAmountBroke ?? 0);
            this.tools.text("winBankruptcyCoinNum", actuallyWinnerSettleAmountBroke);
        }

        // 上层部分
        const teamName = I18n.str("teamName")(this.itemData?.teamName);
        this.tools.text("teamName", teamName);

        const winnerEatChess = this.itemData?.winnerEatChess;
        const winnerEatChessStr = I18n.str("endWinCatchChess2v2")(winnerEatChess);
        this.tools.text("teamwinEat", winnerEatChessStr);

        const antes = this.itemData?.eatChessAntes;
        this.tools.text("winAnteNum", antes);

        const winnerEatMultiple = this.itemData?.winnerEatMultiple;
        const winnerEatMultipleStr = winnerEatChess + Lab.x + winnerEatMultiple + "/2";
        this.tools.text("winMultiplier", winnerEatMultipleStr);

        if (isEscape) {
            const actuallyEscapeBalance = this.itemData?.actuallyEscapeBalance;
            this.tools.text("winMultiplierNum", Lab.reduce + actuallyEscapeBalance);
        } else {
            if (isBroke) {
                const actuallyBeEatChessGainBalance = this.itemData?.actuallyBeEatChessGainBalance;
                this.tools.text("winMultiplierNum", Lab.reduce + actuallyBeEatChessGainBalance);
            } else {
                const winNotBrush = this.itemData?.actuallyWinnerSettleAmountNotBroke;
                const winNotBrushStr = winNotBrush >= 0 ? (Lab.plus + winNotBrush) : winNotBrush;
                this.tools.text("winMultiplierNum", winNotBrushStr);
            }
        }

        const platformChargesStr = I18n.str("platformCharges");
        this.tools.text("winPlatformTips", platformChargesStr);

        const chargeRate = this.itemData?.chargeRate ?? 0;
        this.tools.text("winRatio", chargeRate + Lab.percent);
        const chargeBalance = this.itemData?.actuallyChargeBalance ?? 0;
        this.tools.text("winPlatformCoinNum", Lab.reduce + chargeBalance);

        //
        const winTips1Str = I18n.str("endWinTips2v2")(100 - chargeRate);
        const winTips2Str = I18n.str("catchChessTips");

        this.tools.text("winTips1", winTips1Str);
        this.tools.text("winTips2", winTips2Str);

        // 吃棋数量
        const eatChess = this.itemData?.eatChess ?? 0;
        const winSelfEatStr = I18n.str("catchChessNum")(eatChess);
        this.tools.text("winSelfEat", winSelfEatStr);
        const eatMultiple = this.itemData?.eatMultiple ?? 0;
        this.tools.text("winAnteNum1", antes);
        this.tools.text("winAnteNum2", antes);
        //, eatChess + Lab.x + eatMultiple
        this.tools.text("winMultiplier1", Lab.x + eatMultiple);

        const realCoinNotBroke = this.itemData?.actuallyEatChessGainBalanceNotBroke ?? 0;
        const realCoinNotBrokeStr = realCoinNotBroke >= 0 ? (Lab.plus + realCoinNotBroke) : realCoinNotBroke;
        this.tools.text("winMultiplierNum1", realCoinNotBrokeStr);

        // 破产展示
        const eatChessBrokeAmount = this.itemData?.eatChessBrokeAmount ?? 0;
        this.tools.visible("winCol7", (eatChessBrokeAmount > 0 || gameEscapeUserAmount > 0));
        if (eatChessBrokeAmount > 0 || gameEscapeUserAmount > 0) {
            let bankruptcyNumStr = I18n.str("bankruptcyNum")(eatChessBrokeAmount, gameEscapeUserAmount);
            this.tools.text("winBankruptcyPeople1", bankruptcyNumStr);
            const actuallyProfitFromEscapeBalance = this.itemData?.actuallyProfitFromEscapeBalance ?? 0;
            let actuallyEatChessGainBalanceBroke = Lab.plus + (Number(actuallyProfitFromEscapeBalance) + Number(this.itemData?.actuallyEatChessGainBalanceBroke ?? 0));
            this.tools.text("winBankruptcyCoinNum1", actuallyEatChessGainBalanceBroke);
        }

        // 费率
        this.tools.text("winPlatformTips1", platformChargesStr);
        const eatChessActuallyChargeBalance = this.itemData?.eatChessActuallyChargeBalance ?? 0;
        const profitFromEscapeActuallyChargeBalance = this.itemData?.profitFromEscapeActuallyChargeBalance ?? 0;
        const winPlatformCoinNum = eatChessActuallyChargeBalance + profitFromEscapeActuallyChargeBalance;
        this.tools.text("winRatio1", eatChessChargeRate + Lab.percent);
        this.tools.text("winPlatformCoinNum1", Lab.reduce + winPlatformCoinNum);

        const winBeEatStr = I18n.str("beCatchChess");
        this.tools.text("winBeEat", winBeEatStr);

        const beEatChess = this.itemData?.beEatChess ?? 0;
        this.tools.text("winMultiplier2", Lab.x + beEatChess);

        const actuallyBeEatChessGainBalance = this.itemData?.actuallyBeEatChessGainBalance;
        this.tools.text("winMultiplierNum2", Lab.reduce + actuallyBeEatChessGainBalance);

        this.tools.visible("winCol3", chargeBalance > 0);
        this.tools.visible("winCol5", (eatChessActuallyChargeBalance > 0 || profitFromEscapeActuallyChargeBalance > 0));
    }

    private updateFailItemShow() {
        const isEscape = this.itemData?.isEscape == 1 ? true : false;
        const isBroke = this.itemData?.isBroke > 0 ? true : false;
        if (isEscape) {
            this.tools.visible("failOut", isEscape);
            this.tools.visible("failBankruptcy", false);
        } else {
            this.tools.visible("failBankruptcy", isBroke);
            this.tools.visible("failOut", false);
        }

        this.tools.visible("failMultiplier", (!isBroke && !isEscape));

        //提示语
        const winTips1Str = I18n.str("catchChessTips");
        const winTips2Str = isEscape ? I18n.str("escape") : I18n.str("failEndTips2v2");
        this.tools.text("failTips2", winTips1Str);
        this.tools.text("failTips1", winTips2Str);

        // 赢家吃棋
        const teamName = I18n.str("teamName")(this.itemData?.teamName == "A" ? "B" : "A");
        this.tools.text("failteamName", teamName);

        const winnerEatChess = this.itemData?.winnerEatChess;
        const winnerEatChessStr = isEscape ? I18n.str("escapeVerification") : I18n.str("endWinCatchChess2v2")(winnerEatChess);
        this.tools.text("failteamwinEat", winnerEatChessStr);
        this.tools.visible("failCoinIcon", !isEscape)
        this.tools.visible("failMultiple", !isEscape)
        this.tools.visible("failAntes", !isEscape)

        // 平台收取
        const platformChargesStr = I18n.str("platformCharges");
        this.tools.text("failPlatformTips", platformChargesStr);
        const eatChessChargeRate = this.itemData?.eatChessChargeRate ?? 0;
        this.tools.text("failRatio", eatChessChargeRate + Lab.percent);
        const profitFromEscapeActuallyChargeBalance = this.itemData?.profitFromEscapeActuallyChargeBalance ?? 0;
        const eatChessActuallyChargeBalance = this.itemData?.eatChessActuallyChargeBalance ?? 0;
        const winPlatformCoinNum = eatChessActuallyChargeBalance + profitFromEscapeActuallyChargeBalance;
        this.tools.text("failPlatformCoinNum", Lab.reduce + winPlatformCoinNum);

        this.tools.text("failPlatformTips", platformChargesStr);

        const chargeRate = this.itemData?.chargeRate ?? 0;
        this.tools.text("failRatio1", chargeRate + Lab.percent);
        const chargeBalance = this.itemData?.actuallyChargeBalance ?? 0;
        this.tools.text("failPlatformCoinNum1", Lab.reduce + chargeBalance);


        this.tools.visible("failItem4", (eatChessActuallyChargeBalance > 0 || profitFromEscapeActuallyChargeBalance > 0));//
        this.tools.visible("failCol3", chargeBalance > 0);

        const antes = this.itemData?.eatChessAntes;
        this.tools.text("failAnteNum", antes);
        this.tools.text("failAntes1", antes);
        this.tools.text("failAntes2", antes);

        const actuallyWinnerSettleAmountNotBroke = this.itemData?.actuallyWinnerSettleAmountNotBroke ?? 0;
        const actuallyEscapeBalance = this.itemData?.actuallyEscapeBalance ?? 0;
        const gameBrokeStr = isEscape ? actuallyEscapeBalance : actuallyWinnerSettleAmountNotBroke;
        this.tools.text("failMultiplierNum", Lab.reduce + gameBrokeStr);
        this.tools.text("failMultiplier", Lab.x + winnerEatChess);

        // 吃棋数
        const eatChess = this.itemData?.eatChess;
        const eatMultiple = this.itemData?.eatMultiple;
        const failEatOtherStr = I18n.str("catchChessNum")(eatChess);
        this.tools.text("failEatOther", failEatOtherStr);
        const actuallyEatChessGainBalanceNotBroke = this.itemData?.actuallyEatChessGainBalanceNotBroke ?? 0;
        this.tools.text("failTotal1", Lab.plus + actuallyEatChessGainBalanceNotBroke);
        this.tools.text("failMultiple1", Lab.x + eatMultiple);
        this.tools.visible("failItem2", actuallyEatChessGainBalanceNotBroke > 0);
        // 被吃棋数
        const beCatchChessStr = I18n.str("beCatchChess");
        this.tools.text("failEated", beCatchChessStr);
        const beEatChess = this.itemData?.beEatChess ?? 0;
        this.tools.text("failMultiple2", Lab.x + beEatChess);
        const actuallyBeEatChessGainBalance = this.itemData?.actuallyBeEatChessGainBalance ?? 0;
        this.tools.text("failTotal2", Lab.reduce + actuallyBeEatChessGainBalance);
        // 破产玩家数量

        const eatChessBrokeAmount = this.itemData?.eatChessBrokeAmount ?? 0;
        const gameEscapeUserAmount = this.itemData?.gameEscapeUserAmount ?? 0;
        const gameSettleBrokeAmount = this.itemData?.gameSettleBrokeAmount ?? 0;
        this.tools.visible("failCol2", false);//(gameSettleBrokeAmount > 0 || gameEscapeUserAmount > 0)
        this.tools.visible("failCol7", (eatChessBrokeAmount > 0 || gameEscapeUserAmount > 0));// 
        if (eatChessBrokeAmount > 0 || gameEscapeUserAmount > 0) {
            let bankruptcyNumStr = I18n.str("bankruptcyNum")(eatChessBrokeAmount, gameEscapeUserAmount);
            this.tools.text("failBankruptcyPeople1", bankruptcyNumStr);
            const actuallyProfitFromEscapeBalance = this.itemData?.actuallyProfitFromEscapeBalance ?? 0;
            let actuallyEatChessGainBalanceBroke = Number(actuallyProfitFromEscapeBalance) + Number(this.itemData?.actuallyEatChessGainBalanceBroke ?? 0);
            this.tools.text("failBankruptcyCoinNum1", Lab.plus + actuallyEatChessGainBalanceBroke);
        }

        if (gameSettleBrokeAmount > 0 || gameEscapeUserAmount > 0) {
            let bankruptcyNumStr = I18n.str("bankruptcyNum")(gameSettleBrokeAmount, gameEscapeUserAmount);
            this.tools.text("failBankruptcyPeople", bankruptcyNumStr);
            // const actuallyProfitFromEscapeBalance = this.itemData?.actuallyProfitFromEscapeBalance ?? 0;
            let actuallyWinnerSettleAmountBroke = Lab.plus + Number(this.itemData?.actuallyWinnerSettleAmountBroke ?? 0);
            this.tools.text("failBankruptcyCoinNum", actuallyWinnerSettleAmountBroke);
        }
    }

    private updateBankruptcyItemShow() {
        const isBroke = this.itemData?.isBroke > 0 ? true : false;
        if (isBroke) {
            this.tools.visible("bankruptcyArea2", !this.curNodeState);
        }
        this.tools.visible("bankruptcyArea1", this.curNodeState);
        //
        const eatChess = this.itemData?.eatChess ?? 0;
        const eatChessAntes = this.itemData?.eatChessAntes;
        const beEatChess = this.itemData?.beEatChess ?? 0;

        const winTips1Str = I18n.str("catchChessTips");
        this.tools.text("bankruptcyTips1", winTips1Str);
        //
        this.tools.text("bankruptcyAntes", eatChessAntes);
        this.tools.text("bankruptcyAntes1", eatChessAntes);
        // 平台收取
        const platformChargesStr = I18n.str("platformCharges");
        this.tools.text("bankruptcyPlatformTips", platformChargesStr);
        const eatChessChargeRate = this.itemData?.eatChessChargeRate ?? 0;
        this.tools.text("bankruptcyRatio", eatChessChargeRate + Lab.percent);
        const eatChessActuallyChargeBalance = this.itemData?.eatChessActuallyChargeBalance ?? 0;
        this.tools.text("bankruptcyPlatformCoinNum", Lab.reduce + eatChessActuallyChargeBalance);

        this.tools.visible("bankruptcyItem3", eatChessActuallyChargeBalance > 0);
        // 吃棋数
        const bankruptcyEatStr = I18n.str("catchChessNum")(eatChess);
        this.tools.text("bankruptcyEat", bankruptcyEatStr);
        const actuallyEatChessGainBalanceNotBroke = this.itemData?.actuallyEatChessGainBalanceNotBroke ?? 0;
        this.tools.text("bankruptcyTotal", Lab.plus + actuallyEatChessGainBalanceNotBroke);
        const eatMultiple = this.itemData?.eatMultiple ?? 0;
        this.tools.text("bankruptcyMultiple", Lab.x + eatMultiple);
        // 被吃棋数
        const beCatchChessStr = I18n.str("beCatchChess");
        const actuallyBeEatChessGainBalance = this.itemData?.actuallyBeEatChessGainBalance ?? 0;
        this.tools.text("bankruptcyEated", beCatchChessStr);
        this.tools.text("bankruptcyMultiple1", Lab.x + beEatChess);
        this.tools.text("bankruptcyTotal1", Lab.reduce + actuallyBeEatChessGainBalance);
        // 破产信息
        const eatChessBrokeAmount = this.itemData?.eatChessBrokeAmount ?? 0;
        const gameEscapeUserAmount = this.itemData?.gameEscapeUserAmount ?? 0;
        this.tools.visible("bankruptcyCol7", (eatChessBrokeAmount > 0 || gameEscapeUserAmount > 0))//
        if (eatChessBrokeAmount > 0 || gameEscapeUserAmount > 0) {
            let bankruptcyNumStr = I18n.str("bankruptcyNum")(eatChessBrokeAmount, gameEscapeUserAmount);
            this.tools.text("bankruptcyPeople2", bankruptcyNumStr);
            const actuallyProfitFromEscapeBalance = this.itemData?.actuallyProfitFromEscapeBalance ?? 0;
            let actuallyEatChessGainBalanceBroke = Number(this.itemData?.actuallyEatChessGainBalanceBroke ?? 0) + Number(actuallyProfitFromEscapeBalance);
            this.tools.text("bankruptcyCoinNum5", Lab.plus + actuallyEatChessGainBalanceBroke);
        }
    }

    protected onMessageEvent(): void {
        super.onMessageEvent();
        if (this.view["area"]) this.view["area"].on("click", this.clickNode, this);
    }

    protected offMessageEvent(): void {
        super.offMessageEvent();
        if (this.view["area"]) this.view["area"].off("click", this.clickNode, this);
    }

    private clickNode() {
        this.curNodeState = !this.curNodeState;
        const scaleX = this.view["arrow"].scale.x;
        const scaleZ = this.view["arrow"].scale.z;
        this.view["arrow"].scale = v3(scaleX, -this.view["arrow"].scale.y, scaleZ);
        // this.view["arrow"].scaleY = - this.view["arrow"].scaleY;
        if (this.openState == 0 || this.openState == 1 || this.openState == 3) {
            if (this.itemData?.isWinner == 2) {
                this.tools.visible("winArea", this.curNodeState);
            } else {
                this.tools.visible("failArea", this.curNodeState);
            }
            this.view["failAreaChild1"].getComponent(Layout).updateLayout();
            this.view["failAreaChild2"].getComponent(Layout).updateLayout();
            this.view["winAreaChild2"].getComponent(Layout).updateLayout();
            this.view["failArea"].getComponent(Layout).updateLayout();
            this.view["winAreaChild1"].getComponent(Layout).updateLayout();
            this.view["winArea"].getComponent(Layout).updateLayout();
        } else {
            const isBroke = this.itemData?.isBroke > 0 ? true : false;
            if (isBroke) {
                this.tools.visible("bankruptcyArea2", !this.curNodeState);
            }
            this.tools.visible("bankruptcyArea1", this.curNodeState);
            this.view["bankruptcyArea1"].getComponent(Layout).updateLayout();
        }
        this.node.getComponent(Layout).updateLayout();
        EventMgr.dispatchEvent(EventName.EVENT_GAMEOVER_LAYOUT);
    }


    public leaveWindowArea() {
        commonUtil.setOpacity(this.node, 0);
        // this.node.opacity = 0;
    }

    public enterWindowArea() {
        commonUtil.setOpacity(this.node, 255);
        // this.node.opacity = 255;
    }
}
