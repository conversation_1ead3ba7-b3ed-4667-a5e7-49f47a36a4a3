import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tween, Twe<PERSON>, v3, Vec3, _decorator, is<PERSON><PERSON><PERSON>, Sprite, Label, view, director } from "cc";
import { BasePanel } from "../framework/basePanel";
import { EventMgr } from "../framework/eventMgr";
import { EventName } from "../framework/eventName";
import { Utils } from "../framework/frameworkUtils";
import { I18n } from "../framework/i18n";
import { Lab } from "../framework/lab";
import globalData from "../globalData";
import { TopAreaMgr } from "../manager/topAreaMgr";
import ViewManager from "../manager/view";
import { GAME_MODE } from "../network/constants";
import { sealBridge, sealClick } from "../seal/SealBridge";
import { PlayerStatus } from "../type";
import { EventType } from "../type/events";
import { commonUtil } from "../utils/commonUtil";
import { sensorClick } from "../utils/sensor";
import { loadRemoteImg, throttleFunc } from "../utils/utils";
import TopItem from "./topItem";

const { ccclass } = _decorator;

interface IItemInfo {
    name: string,
    script: TopItem,
    worldPos: Vec3,
    id: string,
    w: number,
    h: number,
}

@ccclass("TopArea")
export default class TopArea extends BasePanel {
    // 默认为false，fasle 点击之后  变成true 
    private animState: boolean = false;
    private animMoving: boolean = false;
    // 飞金币状态
    public flyCoinState: boolean = false;
    // 维护item和id的映射
    private itemList: Array<IItemInfo> = [];
    // 调用
    private playCloseFlyCb: any = null;
    // 等待动画完成时间
    private waitTime: number = 3.5;
    // 自己的头像
    private avatorSp: SpriteFrame = null;
    // 动画1
    tweenAnim0: Tween<Node> = null;
    tweenAnim1: Tween<import("cc").UIOpacity> = null;
    tweenAnim6: Tween<import("cc").UIOpacity> = null;
    tweenAnim5: Tween<import("cc").UIOpacity> = null;
    tweenAnim2: Tween<import("cc").UITransform> = null;
    tweenAnim3: Tween<Node> = null;
    tweenAnim4: Tween<Node> = null;
    tweenAnim8: Tween<import("cc").UITransform> = null;

    private _onClickRule = throttleFunc(this.showRules.bind(this), 1000)    

    init(data: any): void {
        super.init(data);
    }

    protected onEnable(): void {
        super.onEnable();
        TopAreaMgr.instance.spMap = new Map();
        this.preHandleShow();
        this.updateWidget();
        ViewManager.topAreaScript = this;
        this.keepItem();
        this.flyCoinState = false;
        this.animState = false;
        this.tools?.visible("topOtherPeople", false);
        this.updateAllInfo();
        EventMgr.on(EventName.EVENT_RENDER_BASE_COMPONENT, this, this.updateAllInfo)
    }

    onDisable() {
        super.onDisable();
        TopAreaMgr.instance.spMap = null;
    }

    private preHandleShow() {
        let multiple: string = '0';
        if (I18n.languageState == 1) {
            multiple = Lab.x_1 + multiple;
        } else if (I18n.languageState == 2) {
            multiple = multiple + Lab.x_1;
        }
        this.tools?.text("selfMultipleNum", multiple);
    }

    private keepItem() {
        const itemScript = this.view["topOtherItem"] && this.view["topOtherItem"].getComponent(TopItem);
        const itemScript1 = this.view["topOtherItem1"] && this.view["topOtherItem1"].getComponent(TopItem);
        const itemScript2 = this.view["topOtherItem2"] && this.view["topOtherItem2"].getComponent(TopItem);
        itemScript && itemScript.init();
        itemScript1 && itemScript1.init();
        itemScript2 && itemScript2.init();
        this.itemList = [
            { name: "item", script: itemScript, worldPos: v3(-518.368, -1207.073), w: 42, h: 42, id: "", },
            { name: "item1", script: itemScript1, worldPos: v3(-518.368, -1148.998), w: 42, h: 42, id: "", },
            { name: "item2", script: itemScript2, worldPos: v3(-518.368, -1089.498), w: 42, h: 42, id: "", }
        ]

    }

    protected onMessageEvent(): void {
        super.onMessageEvent();
        if (this.view["iconBg"]) this.view["iconBg"].on("click", this.clickDropDownBtn, this);
        if (this.view["questionMark"]) this.view["questionMark"].on("click", this.clickQuestionMark, this);
        if (this.view["coinbg"]) this.view["coinbg"].on("click", this.clickRecharge, this);
        if (this.view["topOtherPeople"]) this.view["topOtherPeople"].on("click", this.clickDropDownBtn, this);
        if (this.view['illustrate']) this.view['illustrate'].on('click', this.showPromptBox, this)
        if (this.view['Exit']) this.view['Exit'].on('click', this.handleExit, this)
        if (this.view['Rules']) this.view['Rules'].on('click', this._onClickRule, this)

        // 遮罩层
        if (this.view['classicTopAreaMask']) this.view['classicTopAreaMask'].on('click', this.hideClassicTopAreaMask, this)

        if (this.view['illu'])
            director.on(EventType.CHANGE_VIEW, this.updateWidget, this);

    }

    protected offMessageEvent(): void {
        super.offMessageEvent();
        if (this.view["iconBg"]) this.view["iconBg"].off("click", this.clickDropDownBtn, this);
        if (this.view["questionMark"]) this.view["questionMark"].off("click", this.clickQuestionMark, this);
        if (this.view["coinbg"]) this.view["coinbg"].off("click", this.clickRecharge, this);
        if (this.view["topOtherPeople"]) this.view["topOtherPeople"].off("click", this.clickDropDownBtn, this);
        if (this.view['illustrate']) this.view['illustrate'].off('click', this.showPromptBox, this)
        if (this.view['Exit']) this.view['Exit'].off('click', this.handleExit, this)
        if (this.view['Rules']) this.view['Rules'].off('click', this._onClickRule, this)
        // 遮罩层
        if (this.view['classicTopAreaMask']) this.view['classicTopAreaMask'].off('click', this.hideClassicTopAreaMask, this)
        EventMgr.off(EventName.EVENT_RENDER_BASE_COMPONENT, this, this.updateAllInfo)

        director.off(EventType.CHANGE_VIEW, this.updateWidget, this);

    }

    updateWidget() {
        const { height } = view.getVisibleSize()
        if (this.node?.isValid && height > 0 && height < 2000 && globalData?.roomInfo?.topPartHeight > 0) {
            commonUtil.setPosition(this.node, v3(0, height / 2 - (globalData?.roomInfo?.topPartHeight ?? 0), 0))
        }
        // console.error('updateWidget topArea', JSON.stringify(this.node.getPosition()), globalData?.roomInfo?.topPartHeight)
    }


    private clickRecharge() {
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '金币余额',
            // element_business_id: globalData.gameRoundId
        })
        sealClick({
            notifyName: 'rechargeClick',
            data: { userId: globalData.myUserId }
        }, () => { })
    }

    private async updateSelfDataShow() {
        let data = TopAreaMgr.instance.getSelfData();
        const antes = data?.antes ?? 0;
        let coin = data?.coin ?? 0;
        const eatChessNum = (data?.eatChessNum ?? 0);
        let multiple = (data?.multiple ?? 0);
        if (I18n.languageState == 1) {
            multiple = Lab.x_1 + multiple;
        } else if (I18n.languageState == 2) {
            multiple = multiple + Lab.x_1;
        }
        const portrait = data?.portrait ?? ""
        coin = Utils.bigNumberTransform(coin);
        this.avatorSp = this.avatorSp ? this.avatorSp : await loadRemoteImg(portrait) as SpriteFrame;
        if (!isValid(this.node, true)) return;
        if (this.view?.["selfAvator"]) {
            if (this.avatorSp) {
                commonUtil.setSpriteFrameAndAdapteHeight(this.view["selfAvator"].getComponent(Sprite), this.avatorSp)
            } else {
                this.tools?.loadImgResAndAdapt('image/defaultImg/spriteFrame', "selfAvator");
            }
        }
        this.tools?.text("selfBottomNoteNum", antes);
        this.tools?.text("selfMultipleNum", multiple);
        this.tools?.text("selfEatNum", eatChessNum);
        this.tools?.text("coinNum", coin);
        this.tools?.visible("gameOver_teamA", data?.teamName == "A")
        this.tools?.visible("gameOver_teamB", data?.teamName == "B")
    }

    private async clickDropDownBtn() {
        if (this.flyCoinState) return;
        if (this.animMoving) return;
        this.animMoving = true;
        this.playCloseFlyCb = null;
        this.animState = !this.animState;
        this.recordClickData(this.animState);
        const scaleX = this.view["arrow"].scale.x;
        const scaleZ = this.view["arrow"].scale.z;
        this.view["arrow"].scale = this.animState ? v3(scaleX, -1, scaleZ) : v3(scaleX, 1, scaleZ);
        if (this.animState) {
            this.tools?.visible("topOtherPeople", true);
            TopAreaMgr.instance.itemFristId = null;
            TopAreaMgr.instance.itemSecondId = null;
            this.updateAllInfo();
            if (!this.flyCoinState) {
                this.flyCoinState = false;
                await this.showOtherUserAnim();
                await this.showItemAnim1();
            } else {
                this.flyCoinState = false;
                await this.showItemAnim1();
            }
        } else {
            this.tools?.visible("topOtherPeople", false);
            this.flyCoinState = false;
        }
        this.animMoving = false;
    }

    private recordClickData(animState: boolean) {
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: animState ? '展开' : '收起',
            // element_business_id: globalData.gameRoundId
        })

        sealClick({
            notifyName: 'coinPanelClick',
            data: {
                isExpand: animState ? 1 : 0,
                userIds: [ViewManager.playerSortPosIndexs[0], ViewManager.playerSortPosIndexs[3]].map(
                    (i: number) => `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`
                ) // 上半区域userId
            }
        }, () => { })
    }

    // 出现动画
    private async showOtherUserAnimTwo() {
        return new Promise((resolve, reject) => {
            this.tools?.opacity("topOtherPeople", 0);
            this.tools?.opacity("topOtherItem1", 255);
            this.tools?.opacity("topOtherItem2", 0);
            commonUtil.setHeight(this.view["otherBg"], 128);
            commonUtil.setY(this.view["topOtherPeople"], -130);
            this.tweenAnim0 = tween(this.view["topOtherPeople"])
                .to(0.29, { position: v3(-36.955, -186, 0) })
                .call(() => {
                    resolve("")
                    this.tweenAnim0.removeSelf();
                    this.tweenAnim0 = null;
                })
                .start();
            this.tweenAnim1 = tween(commonUtil.getUIOpacity(this.view["topOtherPeople"]))
                .to(0.29, { opacity: 255 })
                .call(() => {
                    this.tweenAnim1.removeSelf();
                    this.tweenAnim1 = null;
                })
                .start();
        })
    }

    // 出现动画
    private async showOtherUserAnim() {
        return new Promise((resolve, reject) => {
            this.tools?.opacity("topOtherPeople", 0);
            this.tools?.opacity("topOtherItem1", 0);
            this.tools?.opacity("topOtherItem2", 0);
            commonUtil.setHeight(this.view["otherBg"], 72);
            commonUtil.setY(this.view["topOtherPeople"], -130);
            this.tweenAnim0 = tween(this.view["topOtherPeople"])
                .to(0.29, { position: v3(-36.955, -186, 0) })
                .call(() => {
                    resolve("")
                    this.tweenAnim0.removeSelf();
                    this.tweenAnim0 = null;
                })
                .start();
            this.tweenAnim1 = tween(commonUtil.getUIOpacity(this.view["topOtherPeople"]))
                .to(0.29, { opacity: 255 })
                .call(() => {
                    this.tweenAnim1.removeSelf();
                    this.tweenAnim1 = null;
                })
                .start();
        })
    }

    /** item1 动画 */
    private async showItemAnim1() {
        return new Promise(async (resolve) => {
            this.tweenAnim2 = tween(commonUtil.getUITransform(this.view["otherBg"])).to(0.4,
                { height: 192 }).call(() => {
                    this.tweenAnim2.removeSelf();
                    this.tweenAnim2 = null;
                    this.flyCoinState = false;
                }).start();
            await Utils.timeOut(this, 0.1);
            this.showItemAnim2().then(() => {
                resolve("");
            });
            commonUtil.setY(this.view["topOtherItem1"], 30);
            this.tweenAnim3 = tween(this.view["topOtherItem1"]).to(0.12,
                { position: v3(2.598, 0, 0) }).call(() => {
                    this.tweenAnim3.removeSelf();
                    this.tweenAnim3 = null;
                }).start();

            this.tweenAnim6 = tween(commonUtil.getUIOpacity(this.view["topOtherItem1"]))
                .to(0.12, { opacity: 255 })
                .call(() => {
                    this.tweenAnim6.removeSelf();
                    this.tweenAnim6 = null;
                }).start();

        })
    }

    /** item2 动画  */
    private async showItemAnim2() {
        return new Promise(async (resolve) => {
            await Utils.timeOut(this, 0.15);
            commonUtil.setY(this.view["topOtherItem2"], -29);
            const position = this.view["topOtherItem2"].position;
            this.tweenAnim4 = tween(this.view["topOtherItem2"])
                .to(0.12, { position: v3(position.x, -59.5, position.z) })
                .call(() => {
                    resolve("");
                    this.tweenAnim4.removeSelf();
                    this.tweenAnim4 = null;
                })
                .start();
            this.tweenAnim5 = tween(commonUtil.getUIOpacity(this.view["topOtherItem2"]))
                .to(0.12, { opacity: 255 })
                .call(() => {
                    this.tweenAnim5.removeSelf();
                    this.tweenAnim5 = null;
                })
                .start();
        })
    }

    private clickQuestionMark() {
        EventMgr.dispatchEvent("test")

        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '规则入口',
        })
        sealClick({
            notifyName: 'ruleClick',
            data: {}
        }, () => { })
    }

    /**
     * 更新全部的数据信息
     * @param data 
     */
    public updateAllInfo() {
        if (!isValid(this.node, true)) return;
        this.updateSelfDataShow();
        EventMgr.dispatchEvent(EventName.EVENT_TOP_ITEM_UPDATE);
    }

    /**
     * 其他地方主动调用飞金币动效
     * 更新金币
     * @param id  userId 需要展示的人数据
     * 自身维护关闭状态
     * @returns 
     */
    public openFlyCoinAnim(ids) {
        if (ids.length <= 0) {
            return;
        }

        if (ids.length > 1) {
            this.twoVsTwoFlyCoinAnima(ids);
        } else {
            this.normalFlyCoinAnima(ids[0]);
        }
    }

    public twoVsTwoFlyCoinAnima(ids) {
        const index = ids.indexOf(String(TopAreaMgr.instance.userId));
        if (index >= 0) {
            this.flyCoinState = true;
            this.recordClickData(this.flyCoinState);
            if (!this.animState) {
                let list = globalData.gameInfo.userCoinInfo;
                const temp = list.filter(item => item.userId == TopAreaMgr.instance.userId)

                if (temp[0].balance <= 0) {
                    TopAreaMgr.instance.itemFristId = ids[index == 0 ? 1 : 0];
                    EventMgr.dispatchEvent(EventName.EVENT_TOP_ITEM_UPDATE);
                    this.tools?.visible("topOtherPeople", true);
                    this.itemList[0].id = ids[index == 0 ? 1 : 0];
                    this.showOtherUserAnim();
                }
            }
            this.playCloseFlyCb = () => {
                this.flyCoinState = false;
                const state = this.animState ? this.animState : this.flyCoinState
                this.recordClickData(state);
                if (!this.animState) {
                    this.tools?.visible("topOtherPeople", false);
                    this.animState = false;
                }
            }
            this.unschedule(this.outTimeCb)
            this.outTimeOne();

            return;
        }
        if (this.flyCoinState) return;
        this.flyCoinState = true;
        this.recordClickData(this.flyCoinState);
        if (!this.animState) {
            TopAreaMgr.instance.itemFristId = ids[0];
            // TopAreaMgr.instance.itemSecondId = ids[1];
            EventMgr.dispatchEvent(EventName.EVENT_TOP_ITEM_UPDATE);
            this.tools?.visible("topOtherPeople", true);
            this.itemList[0].id = ids[0];
            // this.itemList[1].id = ids[1];
            // this.showOtherUserAnimTwo();
            this.showOtherUserAnim();
        }
        this.playCloseFlyCb = () => {
            this.flyCoinState = false;
            const state = this.animState ? this.animState : this.flyCoinState
            this.recordClickData(state);
            if (!this.animState) {
                this.tools?.visible("topOtherPeople", false);
                this.animState = false;
            }
        }
        this.unschedule(this.outTimeCb)
        this.outTimeOne();
    }

    public normalFlyCoinAnima(id) {
        if (String(id) === String(TopAreaMgr.instance.userId)) return;
        if (this.flyCoinState) return;
        this.flyCoinState = true;
        this.recordClickData(this.flyCoinState);
        TopAreaMgr.instance.itemFristId = id;
        EventMgr.dispatchEvent(EventName.EVENT_TOP_ITEM_UPDATE);
        this.tools?.visible("topOtherPeople", true);
        this.itemList[0].id = id;
        if (!this.animState) this.showOtherUserAnim();
        this.playCloseFlyCb = () => {
            this.flyCoinState = false;
            const state = this.animState ? this.animState : this.flyCoinState
            this.recordClickData(state);
            if (!this.animState) {
                this.tools?.visible("topOtherPeople", false);
                this.animState = false;
            }
        }
        this.unschedule(this.outTimeCb)
        this.outTimeOne();
    }

    private outTimeOne() {
        this.scheduleOnce(this.outTimeCb, this.waitTime);
    }

    private outTimeCb() {
        this.playCloseFlyCb && this.playCloseFlyCb();
    }

    /**
     * 通过userid 获取世界坐标以及信息
     * @param id userId
     * @returns TODO 代码优化
     */
    public getCoinWorldPosById(id: string, index = null) {
        if (id == TopAreaMgr.instance.userId) {
            let { coinIconPos, multiplePos, multipleFontSize, eatPos, eatFontSize, coinPos, coinFontSize } = this.getPosAndFontSize();
            return {
                worldPos: coinIconPos,
                h: 50,
                w: 50,
                multuple: { position: multiplePos, fontSize: multipleFontSize },
                chess: { position: eatPos, fontSize: eatFontSize },
                coin: { position: coinPos, fontSize: coinFontSize }
            }
        }
        // 如果当前是打开状态则返回第一个的坐标信息
        // 如果不是
        let idx = 0;
        if (this.animState) { //false 已经打开
            for (let i = 0; i < this.itemList.length; i++) {
                const isSelf = this.itemList[i]?.script?.isSelf(id);
                if (isSelf) {
                    idx = this.itemList[i]?.script?.getItemIdx();
                    break
                }
            }
        } else {
            (index != null) && (idx = index);
        }
        let itemInfo = this.itemList[idx];
        let { coinIconPos, multiplePos, multipleFontSize, eatPos, eatFontSize, coinPos, coinFontSize } = itemInfo?.script?.getPosAndFontSize();
        return {
            worldPos: coinIconPos,
            h: itemInfo?.h,
            w: itemInfo?.w,
            multuple: { position: multiplePos, fontSize: multipleFontSize },
            chess: { position: eatPos, fontSize: eatFontSize },
            coin: { position: coinPos, fontSize: coinFontSize }
        }
    }

    /**
     * 设置显示隐藏
     * @param id userid
     * @param bool 是否显示
     * @param itemIdx 是否显示
     * 增加容错
     */
    setLabActive(id: string, bool: boolean, itemIdx?: number | null) {
        if (bool && itemIdx !== null) {
            for (let j = 0; j < this.itemList.length; j++) {
                const getItemIdx = this.itemList[j]?.script?.getItemIdx();
                if (getItemIdx == itemIdx) {
                    this.itemList[j]?.script?.setLabActive(bool);
                }
            }
            return null;
        }
        if (id == TopAreaMgr.instance.userId) {
            this.tools?.visible("selfMultipleNum", bool);
            this.tools?.visible("selfEatNum", bool);
            this.tools?.visible("coinNum", bool);
            return null;
        }
        let idx = 0;
        if (this.animState) {
            for (let i = 0; i < this.itemList.length; i++) {
                const isSelf = this.itemList[i]?.script?.isSelf(id);
                if (isSelf) {
                    idx = this.itemList[i]?.script?.getItemIdx();
                    break
                }
            }
        }
        this.itemList[idx]?.script?.setLabActive(bool);
        return this.itemList[idx]?.script?.getItemIdx();
    }

    /**
     * 设置显示隐藏
     * @param id userid
     * @param bool 是否显示
     * @param itemIdx 是否显示
     * 增加容错
     */
    setLabActiveView(id: string, multipleBool: boolean, eatBool: boolean, coinBool: boolean, itemIdx?: number | null, index = null) {
        if (!this.itemList) {
            return null;
        }

        if (multipleBool && itemIdx !== null) {
            for (let j = 0; j < this.itemList.length; j++) {
                const getItemIdx = this.itemList[j]?.script?.getItemIdx();
                if (getItemIdx == itemIdx) {
                    this.itemList[j]?.script?.setMultipleLabActive(multipleBool);
                    this.itemList[j]?.script?.setEatLabActive((eatBool || coinBool));
                    this.itemList[j]?.script?.setCoinLabActive(coinBool);
                }
            }
            return null;
        }
        if (id == TopAreaMgr.instance.userId) {
            this.tools?.visible("selfMultipleNum", multipleBool);
            this.tools?.visible("selfEatNum", (eatBool || coinBool));
            this.tools?.visible("coinNum", coinBool);
            return null;
        }
        let idx = 0;
        if (this.animState) {
            for (let i = 0; i < this.itemList.length; i++) {
                const isSelf = this.itemList[i]?.script?.isSelf(id);
                if (isSelf) {
                    idx = this.itemList[i]?.script?.getItemIdx();
                    break
                }
            }
        } else {
            (index != null) && (idx = index);
        }
        this.itemList[idx]?.script?.setMultipleLabActive(multipleBool);
        this.itemList[idx]?.script?.setEatLabActive((eatBool || coinBool));
        this.itemList[idx]?.script?.setCoinLabActive(coinBool);
        return this.itemList[idx]?.script?.getItemIdx();
    }


    /**
     * 获取位置和fontsize
     */
    private getPosAndFontSize() {
        const coinIconPos = commonUtil.convertToWorldSpaceARToZero(this.view["coin"]) ?? null;
        const multiplePos = commonUtil.convertToWorldSpaceARToZero(this.view["selfMultipleNum"]) ?? null;
        const multipleFontSize = this.view["selfMultipleNum"]?.getComponent(Label).fontSize ?? 0;
        const eatPos = commonUtil.convertToWorldSpaceARToZero(this.view["selfEatNum"]) ?? null;
        const eatFontSize = this.view["selfEatNum"]?.getComponent(Label).fontSize ?? 0;
        const coinPos = commonUtil.convertToWorldSpaceARToZero(this.view["coinNum"]) ?? null;
        const coinFontSize = this.view["coinNum"]?.getComponent(Label).fontSize ?? 0;
        return { coinIconPos, multiplePos, multipleFontSize, eatPos, eatFontSize, coinPos, coinFontSize }
    }

    /**
     * 获取棋盘金币信息
     */
    private getGameCoinInfo() {
        let coinInfo
        let maxEatChess = 0
        const userCoinInfo = globalData.gameInfo?.userCoinInfo?.filter((infos) => {
            if (infos.userId === globalData.myUserId) {
                coinInfo = infos
            }
            return [PlayerStatus.Gaming, PlayerStatus.Hosting].includes(globalData.getUserInfoByUserId(infos?.userId)?.status)
        })
        userCoinInfo?.forEach((infos) => {
            if (+infos.eatChessNum > maxEatChess) {
                maxEatChess = (+infos.eatChessNum)
            }
        })
        return {
            coinNum: coinInfo?.coin || 0,
            antes: coinInfo?.antes ?? 0,
            maxEatChess
        }
    }

    /**
 *  显示说明提示框
 */
    showPromptBox() {
        let target = this.view['promptBox']
        let classicTopAreaMask = this.view['classicTopAreaMask']
        if (target.active) {
            this.tools.visible('promptBox', false)
            classicTopAreaMask.active = false
        } else {
            this.tools.visible('promptBox', true)
            classicTopAreaMask.active = true
        }
        const userId = [ViewManager.playerSortPosIndexs[0], ViewManager.playerSortPosIndexs[3]].map(
            (i: number) => {
                console.error("=====", globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userIndex)
                return `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`
            }
        )[I18n.languageState === 2 ? 1 : 0]
        console.error("====", userId)
        // 通知客户端
        this.handleClickSetup()
        // 埋点
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '设置',
            page_business_type: globalData.getUserInfoByUserId(globalData.myUserId)?.status
        })
    }


    /** 
 * 点击离开游戏
 * */
    handleExit() {
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '离开游戏',
        })
        const { antes, coinNum, maxEatChess } = this.getGameCoinInfo()
        console.log('游戏金币', coinNum)
        console.log('排名', globalData.gameInfo.playerInfo.find(p => p.userid === globalData.myUserId)?.userIndex === (globalData.gameInfo?.modeData?.userIndex1st) ? 1 : 2)
        sealClick({
            notifyName: 'leaveClick',
            data: {
                userId: globalData.myUserId, // 用户ID
                coinThreshold: antes * (maxEatChess < 3 ? 3 : maxEatChess),
                antes, // 基数
                maxEatChess, // 最大吃棋子数
                coinNum,
                ranK: globalData.gameInfo.playerInfo.find(p => p.userid === globalData.myUserId)?.userIndex === (globalData.gameInfo?.modeData?.userIndex1st) ? 1 : 2
            }
        }, () => { })
    }

    /** 
    *  点击规则弹窗
     */
    showRules() {
        sensorClick({
            $title: 'ludo游戏房页',
            $element_name: '规则入口',
        })

        sealClick({
            notifyName: 'ruleClick',
            data: {}
        }, () => { })
    }
    
    /**
     * 处理点击setupClick功能
     */
    handleClickSetup() {
        const userId = [ViewManager.playerSortPosIndexs[0], ViewManager.playerSortPosIndexs[3]].map(
            (i: number) => {
                console.error("=====", globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userIndex)
                return `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`
            }
        )[I18n.languageState === 2 ? 1 : 0]
        console.error("====", userId)
        sealClick({
            notifyName: 'setupClick',
            data: {
                userId, // 右上角用户ID
                isExpand: this.view['promptBox']?.active // 是否展开设置面板
            }
        }, () => { })
    }

    /*
 *  隐藏遮罩层
 */
    hideClassicTopAreaMask() {
        let target = this.view['promptBox']
        if (!target.active) return
        let userId = [ViewManager.playerSortPosIndexs[0]].map((i: number) => `${globalData.gameInfo?.playerInfo?.find(p => p.userIndex === i)?.userId || ''}`)[0]
        // 隐藏对话框
        this.tools.visible('promptBox', false)
        // 隐藏遮罩层
        this.view['classicTopAreaMask'].active = false
        this.handleClickSetup()
    }
}


