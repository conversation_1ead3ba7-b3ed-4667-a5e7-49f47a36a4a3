import { GAME_MODE, END_MODE, SOCKET_TYPE } from '../network/constants';
import globalData from '../globalData';
import { Button, Component, EventTouch, NodeEventType, _decorator } from 'cc';
import { EventType } from '../type/events';
import { UIMgr } from '../framework/uiMgr';
const { ccclass, property } = _decorator;

@ccclass("ReadyModel")
export default class ReadyModal extends Component {

  @property(Button)
  startBtn: Button = null;

  // 游戏模式
  gameMode: number = GAME_MODE.ONE_VS_ONE;
  // 获胜模式
  endMode: number = END_MODE.CLASSIC;

  // LIFE-CYCLE CALLBACKS:

  onLoad() {
    this.gameMode = GAME_MODE.TWO_VS_TWO
    this.endMode = END_MODE.TWO_VS_TWO
    this.startBtn.node.on(NodeEventType.TOUCH_START, () => {
      console.log('点击设置游戏模式按钮..', this.gameMode, this.endMode)
      let subCommand = {
        commandKey: 'setGameMode',
        gameMode: this.gameMode,
        endMode: this.endMode
      }
      // 请求设置游戏模式/获胜模式
      globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand })
      // // 请求加入游戏
      globalData.socketSend(SOCKET_TYPE.JOIN_GAME)
      globalData.socketSend(SOCKET_TYPE.GAME_INFO)
    })
  }

  option1Callback(event: any, type: string) {
    // UIMgr.instance.showDialog("prefab/2v2/gameOver_2v2");
    let value = event.node._name
    console.log('optionCallback', value, type)
    switch (type) {
      case 'endMode': {
        this.endMode = Number(value)
        break
      }
      case 'gameMode': {
        this.gameMode = Number(value)
        break
      }
      default: break
    }
    console.log(`this.gameMode:${this.gameMode},this.endMode:${this.endMode}`)
  }

}
