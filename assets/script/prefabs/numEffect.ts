import { Component, instantiate, Label, Mask, Node, NodePool, tween, v3, Vec3, _decorator } from "cc";
import ActionManager from "../manager/action";
import { commonUtil } from "../utils/commonUtil";

const { ccclass, property } = _decorator;

enum NumEffectType {
    Normal,
    Scrool,
}

@ccclass("NumEffectManager")
export default class NumEffectManager extends Component {

    @property(Node)
    animNode: Node

    @property(Node)
    scrollListNode: Node

    @property(Node)
    scrollItemNode: Node

    numNodePool: NodePool = new NodePool()

    protected onDestroy(): void {
        this.numNodePool.clear()
    }

    /**
     * @description render scroll component
     */
    restoreNodes() {
        while (this.scrollListNode.children.length) {
            this.numNodePool.put(this.scrollListNode.children[0])
        }
    }
    /**
     * @description create vercitle Item
     */
    createScrollNumItem(len: number, numStr: string) {
        while (len) {
            let node
            if (this.numNodePool.size() > 0) {
                node = this.numNodePool.get()
            } else {
                node = instantiate(this.scrollItemNode)
            }
            node.parent = this.scrollListNode
            len--
        }
    }

    private setNumLabel(labelStr: string) {
        this.scrollItemNode.children[0].getComponent(Label).string = labelStr
    }

    /**
     * @description create scrool Item
     */
    public initScrollItem() {
        this.scrollListNode.getComponent(Mask).enabled = true
        const childNode = this.scrollItemNode?.children?.[0]
        if (childNode) {
            childNode.getComponent(Label).string = `0`
            for (let i = 0; i < 9; i++) {
                const node = instantiate(childNode)
                node.getComponent(Label).string = `${i + 1}`
                node.parent = this.scrollItemNode
            }
        }
        this.restoreNodes()
    }
    /**
     * @description play node animation
     */
    async playAnimation(numStr: string, isScroll?: boolean) {
        if (isScroll && numStr) {
            this.restoreNodes()
            this.createScrollNumItem(`${numStr || 0}`.length, numStr)
        } else {
            this.scrollItemNode.getChildByName('Label').getComponent(Label).spacingX = -15
            this.scrollItemNode.getChildByName('Label').getComponent(Label).lineHeight = 72
            this.scrollItemNode.getChildByName('Label').getComponent(Label).fontSize = 72
            this.setNumLabel(numStr)
        }
        commonUtil.setOpacity(this.node, 0);
        // this.node.opacity = 0
        this.node.scale = Vec3.ONE;
        if (isScroll) {
            await this.playScroolNumAnimation(numStr)
        } else {
            await this.playNormalNumAnimation()
        }

        return;
    }

    /**
     * @description play normal effect
     */
    playNormalNumAnimation() {
        return new Promise((resolve) => {
            tween(this.node)
                .to(.083, { scale: Vec3.ONE })
                .call(() => {
                    ActionManager.playAnimation(this.animNode, 'boom')
                })
                .to(0.125, { scale: v3(1.2, 1.2, 1.2) })
                .to(0.125, { scale: v3(0.9, 0.9, 0.9) })
                .to(0.125, { scale: v3(1.1, 1.1, 1.1) })
                .to(0.125, { scale: v3(0.94, 0.94, 0.94) })
                .to(0.125, { scale: v3(0.96, 0.96, 0.96) })
                .to(0.125, { scale: v3(0.9, 0.9, 0.9) })
                .delay(1.58)
                .call(() => {
                    this.playVanishAnimation(NumEffectType.Normal)
                    resolve('')
                })
                .start()
            tween(commonUtil.getUIOpacity(this.node))
                .to(.083, { opacity: 255 })
                .delay(2.33)
                .start()
        }).catch((err) => {
            console.error("playNormalNumAnimation Error:" + err);
        })
    }

    playScroolNumAnimation(numStr: string) {
        return new Promise((resolve) => {
            tween(this.node)
                .delay(0.05)
                .to(0.125, { scale: v3(1.4, 1.4, 1.4) })
                .to(0.125, { scale: v3(0.9, 0.9, 0.9) })
                .call(() => {
                    this.playScrollAnimation(numStr)
                })
                .delay(1.9)
                .call(() => {
                    ActionManager.playAnimation(this.animNode, 'boom')
                })
                .delay(0.1)
                .to(0.125, { scale: v3(1.4, 1.4, 1.4) })
                .call(() => {
                    this.playVanishAnimation(NumEffectType.Scrool)
                    resolve('')
                })
                .start()
            tween(commonUtil.getUIOpacity(this.node))
                .to(.05, { opacity: 255 })
                .delay(2.375)
                .start()
        }).catch((err) => {
            console.error("playScroolNumAnimation Error:" + err);
        })
    }

    /**
     * @description 消失动画
     */
    playVanishAnimation(numEffectType: NumEffectType) {
        return new Promise((resolve) => {
            const scale = numEffectType === NumEffectType.Normal ? 0.4 : 0.9;
            tween(this.node)
                .to(.54, { scale: v3(scale, scale, scale) })
                .delay(0.083)
                .call(() => {
                    resolve('')
                })
                .start()
            tween(commonUtil.getUIOpacity(this.node))
                .delay(0.54)
                .to(.083, { opacity: 0 })
                .start()
        })
    }

    /**
     * @description play verticlly scrolling anim
     */
    playScrollAnimation(numStr: string) {
        const len = `${numStr || 0}`.length
        if (len) {
            const midLen = Math.floor(len / 2)
            const height = 56
            for (let i = 0; i < len; i++) {
                const targetNode = this.scrollListNode.children[i]
                if (!targetNode) return
                const { x } = targetNode.getPosition()
                const finalIndex = +(numStr?.charAt(i))
                tween(targetNode)
                    .to(.5 - Math.abs(midLen - i) * 0.1, { position: v3(x, height * 9, 0) }, { easing: 'cubicIn' })
                    .to(.01, { position: v3(x, 0, 0) })
                    .to(.6 - Math.abs(midLen - i) * 0.1, { position: v3(x, height * 9, 0) })
                    .call(() => {
                        targetNode.setPosition(v3(0, 0, 0))
                    })
                    .to(.1, { position: v3(x, height * (finalIndex > 0 ? finalIndex - 1 : 0), 0) }, { easing: 'quartInOut' })
                    .to(.45, { position: v3(x, height * finalIndex, 0) })
                    .call(() => {
                    })
                    .start()
            }
        }
    }
}
