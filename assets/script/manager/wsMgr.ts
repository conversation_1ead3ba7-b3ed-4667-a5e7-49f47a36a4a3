import { _decorator, Component, Node, director, sys, game, Game } from 'cc';
import { Utils } from '../framework/frameworkUtils';
import { cs } from '../framework/log';
import { UIMgr } from '../framework/uiMgr';
import GameConfig from '../gameConfig';
import globalDataInstance from '../globalData';
import { SOCKET_TYPE } from '../network/constants';
import wsClient from '../network/wsClient';
import WsUtil from '../network/wsUtil';
import { getAppInfo, getGameConfigId, getGameSubType, getToken, rdsTraceReport } from '../seal/bridge';
import { sealBridge, sealClick } from '../seal/SealBridge';
import { IGetSystemInfoRes } from '../seal/types';
import { rdsTrack } from '../track/rdsTrack';
import { commonErrorCode } from '../type/commonErrorCode';
import ViewManager from './view';
const { ccclass, property } = _decorator;

@ccclass('wsMgr')
export class wsMgr extends Component {

    //重试获取token的次数
    _tryGetTokenTimes = 0;
    //重连websocket次数
    _retryCnt = 0;
    //总尝试次数。
    _totalretryCnt = 2;
    //当前重试间隔时间
    _curTime = 0;
    //是否网络异常
    _isNetError = false;
    //是否重试中
    _isRetry = false;
    //是否刷新token
    _isRefreshToken = false;

    _h5CallBackObj = {};

    _hideTime = 0;
    _isSendWsRds = false;

    //初始化
    init() {
        director.on('INIT-PRELOAD', this.preload, this)
        director.on('CLOSE-WEBSOCKET', this.closeWebSocket, this)
        director.on('checkWebsocketConnect', this.checkWebsocketConnect, this)
        game.on(Game.EVENT_SHOW, this.onGameShow, this)
        game.on(Game.EVENT_HIDE, this.onGameHide, this)
    }

    //组件销毁
    onDestroy() {
        director.off('INIT-PRELOAD', this.preload, this)
        director.off('CLOSE-WEBSOCKET', this.closeWebSocket, this)
        director.off('checkWebsocketConnect', this.checkWebsocketConnect, this)
        game.off(Game.EVENT_SHOW, this.onGameShow, this)
        game.off(Game.EVENT_HIDE, this.onGameHide, this)
    }

    /**
     * @zh 游戏切入后台 @en switch app into background
     */
    private onGameHide() {
        this._hideTime = new Date().getTime();
    }

    /**
     * @zh 显示App @en disaplay game app
     */
    private onGameShow() {
        let now = new Date().getTime();
        let dt = now - this._hideTime;
        if (this._hideTime && dt > 1000 * 60 * 10) {
            globalDataInstance.WS && globalDataInstance.WS.close();
            globalDataInstance.WS && globalDataInstance.WS.reconnect();
            UIMgr.instance.showDialog("prefab/dialog/reconnect", {}, ViewManager.gameInstance?.popUpLayer, 100)
        }

        this._hideTime = now;
    }

    private async _initSystemInfo() {
        let appInfo: IGetSystemInfoRes
        // cs.log(`【cocos】开始获取AppInfo`);
        appInfo = await getAppInfo()
        // cs.log(`【cocos】获取到AppInfo${JSON.stringify(appInfo)}`);
        GameConfig.ENV = ['prod', 'pre', 'dev'][appInfo.env || 0]
    }

    /**
     * 加载
     * @param userId 
     * @param h5callbackId 
     */
    async preload(userId: string, h5callbackId?: string) {
        await this._initSystemInfo()
        // cs.log("【cocos】preload" + `,userId:${userId}` + "|" + new Date().getTime());
        if (!userId) {
            // cs.log("【cocos】preload no userId" + "|" + new Date().getTime());
            if (h5callbackId) {
                return sealBridge.h5Callback(h5callbackId, { "status": "失败，没有收到UserId" })
            }

            return;
        }

        //用户id相同并且ws正在连接则不用再次链接
        if (userId == globalDataInstance.userId) {
            if (globalDataInstance.WS.connected) {
                sealBridge.h5Callback(h5callbackId, { "status": "success" })

                return;
            } else if (this._retryCnt <= this._totalretryCnt) {
                this._h5CallBackObj[h5callbackId] = 1;
                return;
            }
        }

        // cs.log(`【cocos】开始获取Token`);
        globalDataInstance.token = await getToken()
        rdsTrack.rdsGameCostTimeFlow("GetToken", globalDataInstance.dateTime["preload"], globalDataInstance.dateTime["preload"], "Success");
        // await Utils.timeOut1(150)
        // cs.log("token====", globalDataInstance.token)
        globalDataInstance.isKeepAlive = true

        // cs.log(`【cocos】开始连接ws,userId=${userId}` + "|" + new Date().getTime());
        // globalDataInstance.WS && globalDataInstance.WS.close();
        this._h5CallBackObj = {};
        this._h5CallBackObj[h5callbackId] = 1;
        globalDataInstance.userId = userId;
        globalDataInstance.myUserId = globalDataInstance.userId;
        this._retryCnt = 0;
        this._isSendWsRds = false;
        this.initWebSocket(h5callbackId)
    }

    /**
     * 初始化webSocket
     * @param userId 
     * @param h5callbackId 
     */
    initWebSocket(h5callbackId?: string) {
        globalDataInstance.dateTime["websocket"] = new Date().getTime();
        globalDataInstance.WS && globalDataInstance.WS.close();
        globalDataInstance.WS && globalDataInstance.WS.reconnect();
        !globalDataInstance.WS && (globalDataInstance.WS = new wsClient());
        this.handleInitEvents(h5callbackId);
    }

    /**
     * 关闭ws（安卓切换用户不关闭ws会必现闪退）
     * @returns 
     */
    closeWebSocket() {
        if (!globalDataInstance?.WS) {
            return;
        }

        globalDataInstance.WS.close();
    }

    /**
     * 初始化链接回调事件
     */
    private handleInitEvents(h5callbackId?: string) {
        const { WS } = globalDataInstance;

        WS.onopen = () => {
            // cs.log(`【cocos】ws-open` + "|" + new Date().getTime());
            UIMgr.instance.hideDialog("prefab/dialog/reconnect")
            if (!this._isSendWsRds) {
                rdsTrack.rdsGameCostTimeFlow('WebSocket', globalDataInstance.dateTime["websocket"], globalDataInstance.dateTime["preload"], 'Success');
                this._isSendWsRds = true;
            }
            this.h5CallBack(1);
            sealClick({
                notifyName: 'ws_ready_callback',
                data: {}
            }, () => { })
            // 心跳逻辑，保证socket存活
            WsUtil.heartCheck(WS);
            WsUtil.timeOutCnt = 0;
            this._retryCnt = 0;

            this.scheduleOnce(() => {
                director.emit('wsOpen');
            }, 1)
            this.resendMessage();
        }
        WS.onmessage = (event) => {
            let data
            // console.log('onmessage', event.data)
            try {
                data = JSON.parse(event.data)
            } catch (e) { }
            // WsUtil.heartCheck(WS);
            WsUtil.receive(data) // 清空超时
            this.checkWebsocketConnect();
            const { rCode, type, message, errorMessage } = data || {}
            const rCodeNumber = Number.parseInt(rCode, 10)
            // 处理rCode不为0的情况，比如token过期
            if (rCodeNumber === 0) {
                this.handleGameStage(type, message)
            } else {
                this.handleInvalidMessage(type, rCodeNumber, errorMessage)
            }
        }
        WS.onerror = (e) => {
            cs.log(`【cocos】ws-error` + "|" + new Date().getTime());

        }
        WS.onclose = async (e) => {
            cs.log(`【cocos】ws-close` + "|" + new Date().getTime());
            if (e.code == 1000) {
                return;
            }
            (this._retryCnt >= this._totalretryCnt) && this.h5CallBack(0);
            this._isRetry = false;
            this.checkNetWork();
            UIMgr.instance.showDialog("prefab/dialog/reconnect", {}, ViewManager.gameInstance?.popUpLayer, 100)
        }
    }

    /**
     * 消息成功处理
     * @param type 
     * @param message 
     */
    handleGameStage(type, message) {
        // cs.log(`【cocos】handleGameStage,type:${type},message:${JSON.stringify(message)}` + new Date().getTime());
        this._tryGetTokenTimes = 0;
        director.emit('handleGameStage', type, message);
    }

    /**
     *消息失败处理
     * @param code 
     * @param message 
     * @returns 
     */
    async handleInvalidMessage(type, code, message) {
        // cs.log(`【cocos】handleInvalidMessage,type:${type},code:${code},message:${JSON.stringify(message)}` + "|" + new Date().getTime());
        // console.log("handleInvalidMessage", rCodeNumber, errorMessage)
        if (code === commonErrorCode.InvalidToken || code == commonErrorCode.鉴权失败) {
            // console.error('【cocos】invalid token。。' + "|" + new Date().getTime())
            // if (this._tryGetTokenTimes++ > 2) {
            //     return;
            // }

            // if (type == SOCKET_TYPE.UNBIND_ROOM) {
            // return;
            // }

            // globalDataInstance.token = await getToken()
            // globalDataInstance.socketSend(type, globalDataInstance.sendMessageParam[type]);
            // console.error('【cocos】new tolen:', globalDataInstance.token + "|" + new Date().getTime());

            return;
        }

        director.emit('handleInvalidMessage', type, code, message)
    }

    /**
     * 重新发送消息。
     */
    resendMessage() {
        // const len = globalDataInstance?.unsentMessage?.length ?? 0;

        // if (len <= 0) {
        //     return;
        // }

        // len > 0 && console.log(`【cocos】有发送未成功的消息，重新发送` + "|" + new Date().getTime());
        // this.schedule(() => {
        //     if (globalDataInstance?.unsentMessage?.length <= 0) {
        //         return;
        //     }
        //     const parm = globalDataInstance.unsentMessage.shift();

        //     console.log(`【cocos】重新发送,type:${parm.type},message:${JSON.stringify(parm.message)}` + "|" + new Date().getTime());
        //     globalDataInstance.socketSend(parm.type, parm.message);
        // }, 0.016, len);
    }

    //重连ws
    retryWebSocket() {
        if (this._isRetry) {
            return;
        }

        this._isRetry = true;
        const cb = () => {
            // console.log(`【cocos】重连ws` + "|" + new Date().getTime());
            globalDataInstance.WS.reconnect()
            UIMgr.instance.showDialog("prefab/dialog/reconnect", {}, ViewManager.gameInstance?.popUpLayer, 100)
        };

        this.scheduleOnce(cb, Math.min(1.5, this._retryCnt++ * 2));
    }

    /**
     * 检测网络情况
     */
    checkNetWork() {
        const netState = sys.getNetworkType();

        this._isNetError = (netState == sys.NetworkType.NONE);
        // cs.log(`【cocos】检测网络，网络状态：${this._isNetError ? "异常" : "正常"}` + "|" + new Date().getTime());
        //如果网络正常,则重试链接ws
        !this._isNetError && this.retryWebSocket();
    }

    //检测ws是否连接正常
    checkWebsocketConnect() {
        //超时3次后重连ws
        if (WsUtil.timeOutCnt >= 3) {
            WsUtil.timeOutCnt = 0;
            globalDataInstance.WS.close();
            globalDataInstance.WS.reconnect();
            UIMgr.instance.showDialog("prefab/dialog/reconnect", {}, ViewManager.gameInstance?.popUpLayer, 100);
        }

    }

    //发送h5callBack
    h5CallBack(result) {
        for (let key in this._h5CallBackObj) {
            sealBridge.h5Callback(key, { "status": result ? "success" : "webSocket断开连接" })
        }

        if (Object.keys(this._h5CallBackObj).length > 0 && result == 0) {
            rdsTrack.rdsGameCostTimeFlow('WebSocket', globalDataInstance.dateTime["websocket"], globalDataInstance.dateTime["preload"], 'Fail');
        }

        this._h5CallBackObj = {};
    }

    update(dt) {
        if (!this._isNetError) {
            return;
        }

        this._curTime += dt;

        //断网后0.5秒检测一次网络情况
        if (this._curTime >= 0.5) {
            this._curTime = 0;
            this.checkNetWork();
        }
    }
}

