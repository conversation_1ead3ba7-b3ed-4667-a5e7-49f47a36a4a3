{"name": "seal-ludo", "platform": "ios", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "replaceSplashScreen": false, "startScene": "d19bd984-71ec-4ae3-9f86-0d28ce03c6c5", "outputName": "ios", "scenes": [{"url": "db://assets/scene/ludo.scene", "uuid": "21936b10-d44a-483b-89b9-c96e70b90a08", "inBundle": false}, {"url": "db://assets/scene/gameHall.scene", "uuid": "d19bd984-71ec-4ae3-9f86-0d28ce03c6c5", "inBundle": false}], "android": [], "packages": {"ios": {"renderBackEnd": {"metal": true}, "skipUpdateXcodeProject": false, "orientation": {"portrait": true, "upsideDown": false, "landscapeRight": false, "landscapeLeft": false}, "osTarget": {"iphoneos": false, "simulator": true}, "targetVersion": "12.0", "__version__": "1.0.0", "packageName": "ios.game.test"}, "cocos-service": {"configID": "34f2fd", "services": [], "__version__": "3.0.4"}, "native": {"encrypted": false, "xxteaKey": "cbpCZ4lUNMp62oxM", "compressZip": false, "JobSystem": "none", "__version__": "1.0.2"}}, "__version__": "1.3.3", "ios": []}