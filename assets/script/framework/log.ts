/**
 * @content 日志相关
 * <AUTHOR>
 */

import { Evn, LogLevel } from "./enum";

const evn: Evn = Evn.Dev;

/**
 * 方案二
 * 针对不同类型的日志信息进行处理
 * 可拓展
 * 使用方式遵守console即可
 * 类型 log  warn  error  info  time  timeEnd
 */
export class cs {

    public static log(...args: any) {
        this.common(console.log, arguments);
    }

    public static warn(...args: any) {
        this.common(console.warn, arguments);
    }

    public static error(...args: any) {
        this.common(console.error, arguments);
    }

    public static info(...args: any) {
        this.common(console.info, arguments);
    }

    public static time(...args: any) {
        this.common(console.time, arguments);
    }

    public static timeEnd(...args: any) {
        this.common(console.timeEnd, arguments);
    }

    private static common(func: Function, arrInfo: IArguments) {
        if (evn == Evn.Release || evn == Evn.Formal) return;
        func.apply(console, arrInfo);
    }

}
