import { native } from "cc"
import { NATIVE } from "cc/env";
import { getUrlParam1, throttleFunc } from "../utils/utils";
import { sealCallEvents, TCallback, TNativeMethod } from "./sealType";
const ua = decodeURIComponent(window.navigator.userAgent.toLocaleLowerCase())
const isSeal = !!(/sealgame/gi.test(ua) || NATIVE)

interface IWindow { webkit?: any; CustomEvent?: any }
declare var window: Window & IWindow

class SealBridge {
  private static instance: SealBridge
  private callbackID = 1000
  private callbacks: { [key: string]: TCallback } = {}
  private registerHandlers: { [key: string]: TCallback[] } = {}
  readonly isSeal: boolean = isSeal
  readonly debug: boolean = (function () {
    return Boolean(getUrlParam1('debug')) || false
  })() || false

  private constructor() {
    this.init()
  }
  public static getInstance() {
    if (!SealBridge.instance) {
      SealBridge.instance = new SealBridge()
    }
    return SealBridge.instance
  }

  private init() {
    if (native.bridge) {
      console.info("==register native bridge success")
      native.bridge.onNative = (eventName: string, args: any) => {
        try { args = JSON.parse(args) } catch (e) { }
        const { name, __params, __callback_id } = args
        if (name !== 'setSpeak') {
          console.info('==onNative', name, __callback_id, __params)
        }
        switch (eventName) {
          case 'sealJSBridgeListener': {
            // 处理客户端回调前端事件
            if (name && Array.isArray(this.registerHandlers?.[name])) {
              this.registerHandlers[name].forEach((handler) => {
                // console.info('==handler name', name, typeof handler === 'function')
                if (typeof handler === 'function') {
                  handler(__params, __callback_id)
                }
              })
            } else {
              console.info('==handler name', name, typeof this.registerHandlers?.[name])
            }
            break
          }
          case 'sealJSBridgeCallback': {
            // 处理游戏调用客户端事件
            if (__callback_id && typeof this.callbacks?.[__callback_id] === 'function') {
              console.log(__callback_id, __params);
              const ret = this.callbacks[__callback_id](__params);
              delete this.callbacks[__callback_id];
              return ret
            }
            break
          }
          default: break
        }
      }
    }
  }

  private _callNative(method, params, __callback_id) {
    try {
      native.bridge.sendToNative('resposneOcMethod', JSON.stringify({
        method,
        params,
        __callback_id
      }))
    } catch (err) { }
  }

  public call(name: TNativeMethod, params: unknown, callback?: TCallback) {
    const id = (this.callbackID++).toString()
    this.callbacks[id] = callback
    // console.info(`--> H5主动调用客户端name：${name}`, params, id)
    this._callNative(name, params, id)
  }

  public h5Callback(__callback_id, params) {
    if (!__callback_id) return
    try {
      const newParams = JSON.stringify(params)
      console.info(`--> H5被动调用客户端 __h5callback_id：${__callback_id}`, newParams)
      this._callNative('h5callback', newParams, __callback_id)
    } catch (e) { }
  }


  /**
   * 监听客户端事件
   * @param name 事件名
   * @param callback 回调函数
   */
  public on(name: sealCallEvents, callback: TCallback) {
    if (!this.registerHandlers[name]) this.registerHandlers[name] = []
    console.log('==SealBridge on name:', name)
    this.registerHandlers[name].push(callback)
  }

  /**
   * 解绑客户端事件
   * @param name 事件名
   * @param callback 回调函数
   */
  public off(name: sealCallEvents, callback: TCallback) {
    let namedListeners = this.registerHandlers[name]
    if (namedListeners.indexOf(callback) >= 0) {
      delete namedListeners[namedListeners.indexOf(callback)]
    }
  }

  public log(msg: any) {
    this.call('log', { msg: msg });
  }
}


export const sealBridge = SealBridge.getInstance()
export const sealClick = (params: { notifyName: string, data?: Record<any, any> }, callback?: (...ret) => any) => {
  sealBridge.call("notifyClickEvent", { ...params }, callback || ((ret) => {
    console.log("客户端点击回调===", ret)
  }));
}
