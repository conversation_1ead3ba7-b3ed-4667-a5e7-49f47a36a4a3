import { _decorator, Component, Node } from 'cc';
import { GAME_MODE } from '../network/constants';
import { ChessType, finalChessIndexs } from '../type';
import { GameModeMgr } from './gameModeMgr';
import { localServer, runPostion } from './localServer';
const { ccclass, property } = _decorator;

type TChessType = number | ChessType

interface IClassicModeData {
    userIndex1st: TChessType
    userExps: Record<TChessType, number>
    userMaxExp: number
    rewardExp: number
    gameReward: Array<Record<string, any>>
}

@ccclass('gameLocalServer')
export class gameLocalServer extends localServer {
    private static _ins: gameLocalServer = null;
    public static get instance() {
        if (!this._ins) this._ins = new gameLocalServer();
        return this._ins;
    }
    /**  1 2 3 4  */
    public opIndex: number = -1;
    /** 障碍物 */
    public obstacleInfo: Array<number> = [];
    /** 骰子信息 */
    public diceNumArray: Array<number> = []
    /** 所有人的userCoinInfo data */
    public userCoinInfoList: Array<any> = [];
    /** 当前游戏模式 默认为4 */
    public gameMode: GAME_MODE = 4;
    /** 额外保存用户id */
    public extraOpIdx: number = -1;
    /** init opidx */
    public selfOpIdx: number = -1;
    /** 用户吃棋子连击数 */
    public _userComboMap = new Map([[ChessType.red, 0], [ChessType.green, 0], [ChessType.yellow, 0], [ChessType.blue, 0]])
    /** 用户当前经验值 */
    public _userExpMap = new Map([[ChessType.red, 0], [ChessType.green, 0], [ChessType.yellow, 0], [ChessType.blue, 0]])
    /** 用户最大经验经验值上限 */
    public userMaxExp = 100
    /** 基础奖励用户经验值 */
    public rewardExp = 40
    /**首个胜利的玩家棋子 */
    public userIndex1st = ChessType.none

    /**
     * @param chessInfo 
     */
    public setUseAllChessInfo(chessInfo) {
        this.useAllChessInfo = chessInfo;
    }

    /**
     * @returns boolean 
     */
    public isCanGameMode() {
        if (this.gameMode == GAME_MODE.TWO_VS_TWO) return false;
        return true;
    }

    /**
     * @zh 获取最新的经验 @en return user's exp
    **/
    public getUserExpValue(userIndex: TChessType, multiple: number = 1): number {
        let exp = this._userExpMap.get(userIndex)
        // if (exp === -1) return 0 // 提醒经验值达到上限
        if (exp >= this.userMaxExp) {
            return 0
        } else if (exp < this.userMaxExp) {
            const newExp = this.rewardExp * multiple
            exp = newExp + exp
            if (exp > this.userMaxExp) return newExp - (exp - this.userMaxExp)
            else return newExp
        }
        return 0
    }

    /**
     * @zh 同步服务器用户经验值 @en set user's exp
     */
    public setUserExpValue(userIndex: TChessType, value: number) {
        this._userExpMap.set(userIndex, value)
    }

    /**
     * @zh 获取comb属性值 @en get combo value
    **/
    public getUserComboValue(userIndex: TChessType, isEatChess: boolean): number {
        let combo = this._userComboMap.get(userIndex) || 0
        if (isEatChess) ++combo
        return combo
    }

    /**
     * @zh 获取第一个玩家到达终点 @en fist winner Index
     */
    public get1stUserIndex(userIndex?: TChessType, movePath?: number[]) {
        // 2人对战不需要计算1st
        if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isQuickTwoMode) {
            return ChessType.none
        }
        if (!userIndex || !movePath) return this.userIndex1st
        if (this.userIndex1st !== ChessType.none && (!!this.userIndex1st)) {
            return this.userIndex1st
        } else {
            if (!!movePath.length && finalChessIndexs.includes(movePath[movePath.length - 1])) return userIndex
        }
        return ChessType.none
    }

    /**
     * @zh 设置第一名 @en set first winner
     */
    public setUserIndex1st(value: TChessType) {
        this.userIndex1st = value
    }

    /**
     * @zh 同步服务器用户combo数据 @en set user's combo
    **/
    public setUserComboValue(userIndex: TChessType, combo: number = 0) {
        this._userComboMap = new Map([[ChessType.red, 0], [ChessType.green, 0], [ChessType.yellow, 0], [ChessType.blue, 0]])
        this._userComboMap.set(userIndex, combo)
    }

    /**
     * @zh 设置游戏模式数据  @en set mode data
     */
    public setGameModeData(modeData) {
        if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
            this._handleClassicModeData(modeData)
        }
    }

    /**
     * @zh 处理经典模式数据 @en handle classic data
     */
    private _handleClassicModeData(modeData: IClassicModeData) {
        const { userExps, userMaxExp, rewardExp, userIndex1st } = modeData
        Object.keys(userExps).forEach((key: string) => this._userExpMap.set(Number(key), userExps[key]))
        this.userMaxExp = userMaxExp
        this.rewardExp = rewardExp
        this.userIndex1st = userIndex1st
    }

    /**
     * @param stepNum 走的步数 
     * 返回的数据数组如果小于等于0，不可移动
     * @return list
     */
    public getMoveInfoList(chessId: string, stepNum: number) {
        let chessIdx = this.getChessIdx(chessId);
        if (chessId === null || chessId === undefined) return null;
        const info = runPostion[this.opIndex];
        if (chessIdx == 0) {
            if (this.opIndex == -1) return null;
            return [info["move"][0]];
        }
        let hasObs = this.calcHasObstacle(runPostion[this.opIndex]["obstacleInfo"]);
        if (hasObs) {
            let obs = this.getHasObsList(info, chessIdx, stepNum);
            return obs;
        } else {
            let notObs = this.getDontHasObsList(info, chessIdx, stepNum);
            return notObs;
        }
    }

    /**
     * @param eatOff 
     * @returns 
     */
    public getUserCoinInfoList(eatOff: Array<any>) {
        const coinInfoArr = this.userCoinInfoList.concat([]);
        if (eatOff.length <= 0) return coinInfoArr;
        let otherIdx = eatOff[0].split("-")[0];
        let len = coinInfoArr.length;
        for (let n = 0; n < len; n++) {
            coinInfoArr[n].balance = 0;
        }
        let otherCoin = coinInfoArr.reduce((pre, cur) => {
            if (cur.userIndex == otherIdx) {
                pre = cur.coin;
            }
            return pre;
        }, 0);
        for (let i = 0; i < len; i++) {
            let userIdx = Number(coinInfoArr[i]?.userIndex);
            const antes = coinInfoArr[i].antes;
            if (userIdx == Number(otherIdx)) {
                coinInfoArr[i].isEatenChessNum = coinInfoArr[i].isEatenChessNum + eatOff.length;
                coinInfoArr[i].balance = -  eatOff.length * antes;
                let curCoin = coinInfoArr[i].coin + coinInfoArr[i].balance;
                coinInfoArr[i].coin = curCoin >= 0 ? curCoin : 0;
            }
            if (userIdx == Number(this.opIndex)) {
                coinInfoArr[i].eatChessNum = coinInfoArr[i].eatChessNum + eatOff.length;
                coinInfoArr[i].multiple = coinInfoArr[i].eatChessNum * (coinInfoArr.length - 1);
                coinInfoArr[i].balance = eatOff.length * antes;
                if (otherCoin <= 0)
                    coinInfoArr[i].coin = coinInfoArr[i].coin + (otherCoin <= 0 ? 0 : coinInfoArr[i].balance);
            }
        }
        return coinInfoArr
    }

    /**
     * @param stepNum 
     * @returns 
     */
    public getResetDiceNumList(stepNum: number) {
        let i = 0;
        return gameLocalServer.instance.diceNumArray.reduce((pre, cur) => {
            (cur == stepNum && i == 0) ? (i++) : (pre.push(cur));
            return pre;
        }, [])
    }

    /**
     * @param eatOff 
     * @returns 
     */
    public getObsList(eatOff: Array<any>) {
        if (eatOff.length <= 0) return this.obstacleInfo;
        const obs = runPostion[this.opIndex].obstacleInfo;
        return this.obstacleInfo.reduce((pre, cur) => {
            if (cur != obs) pre.push(cur);
            return pre;
        }, [])
    }

    /**
     * @param movePath 
     * @param chessId 
     * @returns 
     */
    public getFinalChessesList(movePath: Array<number>, chessId: string, eatOffList: Array<string>) {
        let chessInfoArr = this.useAllChessInfo.concat([]);
        if (movePath.length <= 0) return chessInfoArr;
        let lastMovePath = movePath[movePath.length - 1];
        for (let i = 0; i < chessInfoArr.length; i++) {
            for (let j = 0; j < eatOffList.length; j++) {
                if (eatOffList[i] == chessInfoArr[i].chessId) {
                    chessInfoArr[i].chessIndex = 0;
                }
            }
            if (chessInfoArr[i].chessId == chessId) {
                chessInfoArr[i].chessIndex = lastMovePath;
            }
        }
        return chessInfoArr;
    }

    /**
     * @param movePath 
     * @param chessId 
     * @return 
     */
    public getEatOffList(movePath: Array<number>, chessId: string) {
        if (movePath.length <= 0) return [];
        let arr = [], lastMovePath = movePath[movePath.length - 1];
        let chessInfoArr = this.useAllChessInfo.concat([]);
        const safe = runPostion["1"].safe;
        const userType = chessId.split("-")[0];
        for (let i = 0; i < chessInfoArr.length; i++) {
            if (chessInfoArr[i].chessIndex == lastMovePath && safe.indexOf(chessInfoArr[i].chessIndex) < 0 &&
                chessInfoArr[i].chessId.split("-")[0] != userType) {
                arr.push(chessInfoArr[i].chessId);
            }
        }
        return arr;
    }

    /**
     * @param info 
     * @param chessIdx 
     * @param stepNum 
     * @returns 
     */
    private getHasObsList(info: any, chessIdx: number, stepNum: number) {
        let moveList = JSON.parse(JSON.stringify(info["move"]));
        if (info["notEnterlast"] == chessIdx) {
            return moveList.slice(0, stepNum + 1)
        } else {
            let idx = moveList.indexOf(chessIdx);
            if (idx + stepNum >= moveList.length) {
                let arr = moveList.slice(idx + 1, moveList.length);
                let len = stepNum - arr.length - 1;
                if (len == 0) {
                    arr.push(info["notEnterlast"]);
                } else {
                    arr.push(info["notEnterlast"]);
                    arr.concat(moveList.slice(0, len + 1))
                }
                return arr;
            } else {
                return moveList.slice(idx + 1, idx + stepNum + 1)
            }
        }
    }

    /**
     * @param info 
     * @param chessIdx 
     * @param stepNum 
     * @returns 
     */
    private getDontHasObsList(info: any, chessIdx: number, stepNum: number) {
        const moveList = JSON.parse(JSON.stringify(info["move"]));
        const enterEndList = JSON.parse(JSON.stringify(info["enterEnd"]));
        let idx = moveList.indexOf(chessIdx);
        if (idx >= 0) {
            if (idx + stepNum >= moveList.length) {
                let arr = moveList.slice(idx + 1, moveList.length);
                let len = stepNum - arr.length;
                let enterArr = enterEndList.slice(0, len);
                return arr.concat(enterArr);
            } else {
                return moveList.slice(idx + 1, idx + stepNum + 1);
            }
        } else {
            let enterIdx = enterEndList.indexOf(chessIdx);
            if (enterIdx >= 0) {
                if (enterIdx + stepNum == enterEndList.length - 1) {
                    return enterEndList.slice(enterIdx + 1, enterEndList.length);
                } else if (enterIdx + stepNum < enterEndList.length - 1) {
                    return enterEndList.slice(enterIdx + 1, enterIdx + stepNum + 1);
                } else {
                    console.warn("no movement");
                    return []
                }
            } else {
                console.warn("check !");
                return [];
            }
        }
    }

    /**
     * @param chessId 
     * @returns 
     */
    private getChessIdx(chessId: string) {
        let chessInfoArr = this.useAllChessInfo.concat([]);
        for (let i = 0; i < chessInfoArr.length; i++) {
            if (chessInfoArr[i].chessId == chessId) {
                return chessInfoArr[i]?.chessIndex;
            }
        }
        return null
    }

    /**
     * @param obstacle 
     * @returns boolean 
     */
    private calcHasObstacle(obstacle: number) {
        if (this.obstacleInfo.length <= 0) return false;
        if (this.obstacleInfo.indexOf(obstacle) >= 0) return true;
        return false;
    }

}

