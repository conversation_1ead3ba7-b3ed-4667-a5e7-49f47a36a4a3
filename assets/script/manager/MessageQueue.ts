import globalDataInstance from "../globalData";
import { ACTIONS } from "../network/constants";

export class MessageQueue {

    private static _queue = {};

    /**
     * 放入队列。
     * rollDice ==> notifyPlayerDice（摇骰子）
     * chessMoveSelect ==> notifyMoveSelect（选择移动棋子）
     * finishMove ==> notifyChessMove（完成移动动画）
     * finishDiceMoving ==> notifyDiceResult（完成摇骰子动画）
     * @param type 
     * @param userRound 
     * @param round 
     */
    public static enqueueMessage(type, userRound, round) {
        // if (globalDataInstance.isMyRound) {
        //     console.log(`setTest---type:${type}  userRound:${userRound}   round:${round}`);
        //     this._queue[type] = {
        //         userRound: userRound,
        //         round: round
        //     }
        // }
    }

    /**
     * 发送回复消息。
     * @param type 
     * @returns 
     */
    public static getqueueMessage(type) {
        const { userRound, round } = this._queue[this.getType(type)];
        console.log(`getTest---type:${type}  userRound:${userRound}   round:${round}`);

        return [userRound, round];
    }

    /**
     * 判断该类型是否需要带上roundId。
     * @param sendType 
     * @returns 
     */
    public static includesType(sendType) {
        return false;
        const types = [ACTIONS.FINISH_MOVE, ACTIONS.ROLL_DICE, ACTIONS.FINISH_DICE_MOVING, ACTIONS.CHESS_MOVE_SELECT];

        return types.includes(sendType);
    }

    /**
     * 获取最新type。
     * @param sendType 
     */
    private static getType(sendType) {
        switch (sendType) {
            case ACTIONS.ROLL_DICE:
                return ACTIONS.NOTIFY_PLAYER_DICE;

            case ACTIONS.FINISH_DICE_MOVING:
                return ACTIONS.NOTIFY_DICE_RESULT;

            case ACTIONS.CHESS_MOVE_SELECT:
                return ACTIONS.NOTIFY_MOVE_SELECT;

            case ACTIONS.FINISH_MOVE:
                return ACTIONS.NOTIFY_CHESS_MOVE;
        }
    }
}