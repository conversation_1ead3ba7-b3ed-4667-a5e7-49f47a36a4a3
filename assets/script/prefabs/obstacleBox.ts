/**
 * @description obstacleList
 */

import { Component, Node, Sprite, Sprite<PERSON><PERSON>e, tween, v3, Vec3, _decorator } from "cc";
import { gameObstacleIndexs, gridCoordinateMap, obtacleFinalIndexMap } from "../gameConfig";
import ViewManager from "../manager/view";
import { commonUtil } from "../utils/commonUtil";
import { loadLoaclResource } from "../utils/utils";

const { ccclass } = _decorator;

@ccclass("ObstacleManager")
export default class ObstacleManager extends Component {

    private getObstacleVec(oIndex: number): any {
        const chssCoor = gridCoordinateMap.get(oIndex)
        if (chssCoor) {
            const gPos = commonUtil.convertToWorldSpaceAR(ViewManager.gameInstance.gridContainer, chssCoor)
            return commonUtil.convertToNodeSpaceAR(this.node, gPos)
        }
    }

    /**
     * @description control view
     */
    public toggleObstacleVisiable(obstacleInfo: number[]) {
        if (this.node.children.length === 0) {
            gameObstacleIndexs.forEach((oIndex: number) => {
                this.addNode(oIndex, obstacleInfo.includes(oIndex))
            })
            return
        }
        gameObstacleIndexs.forEach((oIndex: number) => {
            const targetNode: Node = this.node.getChildByName(`${oIndex}`)
            if (!targetNode) return
            if (obstacleInfo.includes(oIndex)) {
                const targetVec = this.getObstacleVec(oIndex)
                if (!targetVec) return
                targetNode.setPosition(targetVec)
                targetNode.scale = commonUtil.numToV3(0.8)
                commonUtil.setOpacity(targetNode, 255);
                targetNode.angle = 0
                commonUtil.setActive(targetNode, true)
            } else {
                tween(targetNode)
                    .to(1, {
                        angle: 360,
                        scale: v3(2.8, 2.8, 2.8),
                        position: commonUtil.convertToNodeSpaceAR(ViewManager.baseComponentManager.node, commonUtil.convertToWorldSpaceAR(ViewManager.gameInstance.gridContainer, gridCoordinateMap.get(obtacleFinalIndexMap.get(oIndex))))
                    })
                    .set({ scale: commonUtil.numToV3(0.8) })
                    .start();

                tween(commonUtil.getUIOpacity(targetNode))
                    .to(1, {
                        opacity: 0
                    })
                    .start()
            }
        })
    }

    /**
     * @description 添加节点
     */
    private async addNode(oIndex: number, isActive: boolean) {
        const targetVec = this.getObstacleVec(oIndex)
        if (!targetVec) return
        const newNode = new Node(`${oIndex}`)
        const boxComp = newNode.addComponent(Sprite)
        // boxComp.trim = false
        boxComp.spriteFrame = await loadLoaclResource('image/obstacle', SpriteFrame)
        commonUtil.setWidth(newNode, 48);
        commonUtil.setHeight(newNode, 48);
        newNode.scale = commonUtil.numToV3(0.8)
        newNode.setPosition(targetVec)
        commonUtil.setActive(newNode, !!isActive)
        newNode.parent = this.node
    }
}
