import { Asset, assetManager } from "cc";
import { cs } from "./log";
interface IAssetCache { asset: Asset, key: string, bool: boolean }
export class AssetCache {
    private static _ins: AssetCache = null;
    public static get instance() {
        if (!this._ins) this._ins = new AssetCache();
        return this._ins;
    }

    private gameAssetList: Array<IAssetCache> = []

    /** 初始化 */
    init() {
        this.gameAssetList = []
    }

    /**
     * 添加进入list
     * @param asset asset 资源
     * @param bool 缓存是否可以退出游戏时候被删除，默认true可以被删除
     */
    add(asset: Asset, key: string, bool: boolean = true) {
        this.gameAssetList.push({ asset, key, bool })
    }


    /** 销毁 */
    destroy() {
        const len = this.gameAssetList.length;
        for (let i = 0; i < len; i++) {
            if (this.gameAssetList[i]?.bool) {
                assetManager.releaseAsset(this.gameAssetList[i]?.asset);
            }
        }
        this.gameAssetList = [];
    }

    /**
     * 通过key获取asset资源
     * @param key 
     */
    getAssetByKey(key: string) {
        if (!key) {
            cs.log("check input key value !");
            return;
        }
        const len = this.gameAssetList.length;
        for (let i = 0; i < len; i++) {
            if (this.gameAssetList[i]?.key == key) {
                return this.gameAssetList[i]?.asset;
            }
        }
        return null;
    }


}