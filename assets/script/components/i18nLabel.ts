import { _decorator, Component, RichText, Label } from "cc";
import { I18n } from "../framework/i18n";
import { commonUtil } from "../utils/commonUtil";

const { ccclass, property } = _decorator;

/**
 * i18n 语言组件
 * 使用方式：将组件挂载上对应的Label 或者RichText 上
 * 设置i18n中的key在i18nkey 即可
 * refresh点击立即刷新更新编辑展示
 * <AUTHOR>
 */
@ccclass("I18nLabel")
export default class I18nLabel extends Component {
    @property
    labelLen = 10

    @property
    i18nKey = '';
    @property
    get refresh() {
        return false;
    }
    set refresh(v: boolean) {
        this.updateText();
    }

    protected onLoad(): void {
        this.updateText();
    }

    updateText() {
        if (!this.i18nKey) return
        const label = this.getComponent(RichText) ?? this.getComponent(Label);
        if (!label) {
            throw new Error(`I18nLabel [${this.node.name}] 缺少 RichText 或 Label 组件`);
        }
        const str = I18n.str(this.i18nKey) ?? '';
        if (!str) {
            throw new Error(`I18nLabel [${this.node.name}] 上的 key '${this.i18nKey}' 不存在`);
        }
        commonUtil.setLabelEllipsis(label, str, this.labelLen, I18n.languageState === 1)
    }
}
