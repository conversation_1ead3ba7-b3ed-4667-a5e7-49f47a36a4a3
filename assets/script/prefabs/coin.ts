import { Node, ParticleSystem2D, tween, v3, Vec3, _decorator } from "cc";
import { Utils } from "../framework/frameworkUtils";
import SuperPanel from "../framework/superPanel";
import ActionManager from "../manager/action";
import { AudioMgr } from "../manager/audioMgr";
import ViewManager from "../manager/view";
import { PlayerPos } from "../type/index";
import { commonUtil } from "../utils/commonUtil";

const { ccclass } = _decorator;

@ccclass("CoinManager")
export default class CoinManager extends SuperPanel {
    private shineSys: ParticleSystem2D = null;


    /**
     * @desctiption toggle shine particle visiable
     */
    public toggleShineParticleVisiable(isVisiable: boolean) {
        if (!this.view['ShineEffect']) return
        commonUtil.setActive(this.view['ShineEffect'], !!isVisiable)
        if (!this.shineSys) this.shineSys = this.view['ShineEffect'].getComponent(ParticleSystem2D)
        if (isVisiable) {
            this.shineSys.resetSystem()
        } else {
            this.shineSys.stopSystem()
        }
    }

    public resetUi() {
        this.node.setScale(Vec3.ONE)
        this.node.setPosition(v3(0, 0, 0))
        this.view['ShineEffect'].setPosition(v3(0, 0, 0))
        this.view['CoinIcon'].setPosition(v3(0, 0, 0))
        this.view['Icon'].setPosition(Vec3.ZERO)
    }

    /**
     * @desctiption play coin anim
     */
    public playCoinWaveAnimation() {
        ActionManager.playAnimation(this.node, 'coinScale', 6)
        commonUtil.setActive(this.view['WaveEffect'], true)
        Utils.timeOut(this, 1.5).then(() => {
            AudioMgr.instance.play('audio/sound_coinMoveAllValue');
        })
        this.scheduleOnce(() => {
            commonUtil.setActive(this.view['WaveEffect'], false)
        }, 2.2)
    }

    private playMoveAnimation(node: Node, userIndex: PlayerPos) {
        let movePosArr = [-100, -200, -150, 100]
        switch (ViewManager.getCurrentPlayerPosIndex(userIndex)) {
            case PlayerPos.RightBottom: {
                movePosArr = [-300, -300, -300, 200]
                break
            }
            case PlayerPos.LeftBottom: {
                movePosArr = [200, -500, 600, 800]
                break
            }
            case PlayerPos.LeftTop: {
                movePosArr = [150, -250, 150, 50]
                break
            }
            default: break;
        }

        return new Promise((resolve) => {
            commonUtil.bezierTo1(
                node,
                0.3,
                v3(node.position.x + movePosArr[0], node.position.y + movePosArr[1]),
                v3(node.position.x + movePosArr[2], node.position.y + movePosArr[3]),
                Vec3.ZERO
            ).call(() => {
                resolve('')
            })
                .start()
        })
    }

    private playCoinScaleAnimation(node: Node) {
        return new Promise((resolve) => {
            tween(node)
                .to(.05, { scale: v3(1.5, 1.5, 1.5) })
                .to(.05, { scale: v3(1.8, 1.8, 1.8) })
                .to(.1, { scale: Vec3.ONE })
                .call(() => resolve(''))
                .start()
        })
    }

    /**
     * @desctiption coin move animation
     */
    public playCoinMoveAnimation(userIndex: PlayerPos, isWaveEffect?: boolean) {
        commonUtil.setOpacity(this.view['Icon'], 255);
        return new Promise(async (resolve) => {
            if (isWaveEffect) {
                this.resetUi()
                this.node.setScale(v3(1.1, 1.1, 1.1))
                this.playCoinWaveAnimation()
                return resolve('')
            }
            if (!this.node.isValid) return resolve('')
            this.toggleShineParticleVisiable(true)
            await this.playMoveAnimation(this.view['CoinIcon'], userIndex)
            commonUtil.setOpacity(this.view['Icon'], 0);
            this.playCoinScaleAnimation(this.view['Icon'])
            await this.playMoveAnimation(this.view['ShineEffect'], userIndex)
            await Utils.timeOut1(300)
            this.node.active = false;
            this.shineSys.resetSystem()
            resolve('')
        })
    }
}
