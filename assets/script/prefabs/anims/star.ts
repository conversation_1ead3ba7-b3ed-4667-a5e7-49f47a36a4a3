import { _decorator, Vec3, tween, v3, Texture2D, utils } from 'cc';
import { BasePanel } from '../../framework/basePanel';
import { EventMgr } from '../../framework/eventMgr';
import { EventName } from '../../framework/eventName';
import { Utils } from '../../framework/frameworkUtils';
import ActionManager from '../../manager/action';
import { commonUtil } from '../../utils/commonUtil';
const { ccclass, property } = _decorator;

@ccclass('star')
export class star extends BasePanel {
    
    @property(Texture2D)
    starBoomTexture2D

    // 变大动画
    private _largeAnim = tween().to(.05, { scale: commonUtil.getEqualVec3(.5) }).to(.16, { scale: commonUtil.getEqualVec3(1.3) })
    
    // 变小动画
    private _smallAnim = tween().to(.25, { scale: commonUtil.getEqualVec3(.5) })

    // 显示动画
    private _appearAnim = tween().to(.16, { opacity: 255 })

    // 透明动画
    private _disappearAnim = tween().to(.25, { opacity: 0 })

    // init(data: any): void {
    //     super.init(data)
    // }
    onLoad() {
        super.onLoad()
        super.onMessageEvent();
        EventMgr.on(EventName.EVENT_STAR_FLY_ANI, this, this.playFlyAnimation);
    }

    onDestroy() {
        super.onDestroy()
        super.offMessageEvent();
        EventMgr.off(EventName.EVENT_STAR_FLY_ANI, this, this.playFlyAnimation);
    }

    /**
     * @en play the flying start
     * @param startCoor 开始动画位置
     * @param endCoor 结束动画位置
     */
    public playFlyAnimation(startCoor: Vec3, endCoor: Vec3, callback: () => any): Promise<string> {
        return new Promise((resolve) => {
            if (!startCoor || !endCoor) return resolve('')
            commonUtil.setPosition(this.node, startCoor)
            commonUtil.setAngle(this.node, 0)
            commonUtil.setActive(this.node, true)
            const moveTween = () => this.node.isValid && tween(this.node)
            const opacityTween = () =>  this.node.isValid && tween(commonUtil.getUIOpacity(this.node))
            moveTween().then(this._largeAnim).start()
            opacityTween().then(this._appearAnim).start()
            moveTween()
                .to(.8, { position: endCoor, angle: 360 })
                .call(async() => {
                    await ActionManager.playSpriteFrameAnimation(this.view['boom'], this.starBoomTexture2D, 240, 1000 / 24, 1)
                    callback?.()
                    await Utils.timeOut1(50)
                    moveTween().then(this._smallAnim).start()
                    opacityTween().then(this._disappearAnim).call(async() => {
                        commonUtil.setActive(this.node, false)
                        resolve('')
                    }).start()
                })
                .start()
        })
    }
}
