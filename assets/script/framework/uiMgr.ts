/**
 * @content 加载弹窗相关 
 * <AUTHOR>
 */

import { assetManager, find, isValid, Node } from "cc";
import { PRIORITY } from "./enum";
import { LoadMgr } from "./loadMgr";
import { cs } from "./log";

export class UIMgr {
    private static _ins: UIMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new UIMgr();
        return this._ins;
    }

    /** 节点存储 */
    private _dictSharedPanel: any = {}
    /** 状态 */
    private _dictLoading: any = {}

    /**
     * 检查当前界面是否正在展示
     * @param {string} panelPath 
     */
    public isDialogVisible(panelPath: string) {
        if (!this._dictSharedPanel.hasOwnProperty(panelPath)) return false;
        const panel = this._dictSharedPanel[panelPath];
        return isValid(panel) && panel.active && panel.parent;
    }

    /**
     * 显示单例界面
     * @param {String} panelPath 界面path
     * @param {Array} args 参数
     * @param {Node} parent 父节点
     * @param {number} zIndex 层级
     * @returns 
     */
    public async showDialog(
        panelPath: string,
        args?: any,
        parent?: Node,
        zIndex: number = PRIORITY.NORMAL
    ) {
        if (this._dictLoading[panelPath]) return;
        const idxSplit = panelPath.lastIndexOf('/');
        let scriptName = panelPath.slice(idxSplit + 1);
        scriptName = scriptName.charAt(0).toUpperCase() + scriptName.slice(1)
        if (!args) args = {};
        const isExist = this._dictSharedPanel.hasOwnProperty(panelPath);
        const panel = this._dictSharedPanel[panelPath];
        if (isExist && isValid(panel)) {
            panel.parent = parent ?? find("Canvas");
            panel.active = true;
            const script = panel.getComponent(scriptName);
            if (script && script.init) script.init(args);
            else {
                cs.log(`not found file ${scriptName},check file name or init function`)
            }
            return;
        }
        this._dictLoading[panelPath] = true;
        await this.createUi(panelPath, scriptName, parent, zIndex, args);
    }

    /**
     * 创建ui界面
     * @param {string} panelPath 弹窗path
     * @param {string} scriptName 脚本名字
     * @param {Node} parent 父亲节点
     * @param {number} zIndex 层级
     * @param {any} args 参数
     * @returns 
     */
    private async createUi(
        panelPath: string,
        scriptName: string,
        parent: Node,
        zIndex: number,
        args?: any
    ) {
        const node: Node = await LoadMgr.instance.create(panelPath, parent);
        let isCloseBeforeShow = false;
        if (!this._dictLoading[panelPath]) isCloseBeforeShow = true;
        this._dictLoading[panelPath] = false;
        if (!node) {
            cs.error('node is non-existent');
            return;
        }
        node.setSiblingIndex(zIndex);
        this._dictSharedPanel[panelPath] = node;
        let script: any = node.getComponent(scriptName);
        if (script && script.init) script.init(args);
        else {
            cs.log(`not found file ${scriptName},check file name or init function`)
        }
        if (isCloseBeforeShow) this.hideDialog(panelPath);
    }

    /**
     * 针对于alert ，toast，tips，以及加载转圈圈等提示界面
     * 隐藏单例界面
     * 注意一： node的脚本文件中 ，有animClose方法
     * 这按照有动画关闭处理
     * 注意二： 如果创建了animClose函数，未调用，则界面不会被关闭
     * @param {String} panelPath 
     */
    public async hideDialog(panelPath: string) {
        return new Promise((resolve) => {
            const isExist = this._dictSharedPanel.hasOwnProperty(panelPath);
            let panel = this._dictSharedPanel[panelPath];
            const valid = panel && isValid(panel)

            if (!isExist || !valid) {
                resolve("");
                this._dictLoading[panelPath] = false;
                cs.warn(`pleace check ${panelPath} ! ,not found or already closed ! `)
                return;
            }
            if (valid) {
                const idxSplit = panelPath.lastIndexOf('/');
                let scriptName = panelPath.slice(idxSplit + 1);
                scriptName = scriptName.charAt(0).toUpperCase() + scriptName.slice(1)
                // scriptName = scriptName?.charAt(0).toUpperCase() + scriptName.slice(1)
                const script = panel.getComponent(scriptName);
                let ani = script?.["animClose"];
                if (!ani) { panel.parent = null; resolve(""); }
                else ani && ani(() => { panel.parent = null; resolve(""); })
            }
            this._dictLoading[panelPath] = false;
        })
    }

    public destroyAll() {
        for (const key in this._dictSharedPanel) {
            if (Object.prototype.hasOwnProperty.call(this._dictSharedPanel, key)) {
                const element = this._dictSharedPanel[key];
                element.destroy();
            }
        }
        this._dictSharedPanel = {};
    }


    // test code 
    // public getTest() {
    //     console.log(this._dictLoading, this._dictSharedPanel);
    //     assetManager.assets.destroy();
    //     assetManager.releaseAll();
    //     // assetManager.releaseAsset();
    //     assetManager.assets
    // }

}
// test code 
// window.globalThis.test = UIMgr.instance