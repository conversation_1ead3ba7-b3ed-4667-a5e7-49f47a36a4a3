import { <PERSON><PERSON>, Component, director, Label, Node, Sprite, _decorator } from 'cc';
import globalData from '../globalData'
import { SOCKET_TYPE } from '../network/constants'
import { loadRemoteImg } from '../utils/utils'
const { ccclass, property } = _decorator;

@ccclass("MatchingModal")
export default class MatchingModal extends Component {

  @property(Label)
  gameMode: Label = null;

  @property(Label)
  endGameMode: Label = null;

  @property(Button)
  joinBtn: Button = null;

  @property(Button)
  startBtn: Button = null;

  @property(Button)
  shareBtn: Button = null;

  @property(Node)
  oneVsoneContainer: Node = null;
  @property(Sprite)
  oneVsonePlayer1Img: Sprite = null;
  @property(Sprite)
  oneVsonePlayer2Img: Sprite = null;

  @property(Node)
  fourTeamContainer: Node = null;
  @property(Sprite)
  player1Img: Sprite = null;
  @property(Sprite)
  player2Img: Sprite = null;
  @property(Sprite)
  player3Img: Sprite = null;
  @property(Sprite)
  player4Img: Sprite = null;

  onLoad() {
    director.on('updateMatchingModalUI', () => {
      // console.log('updateMatchingModalUI', globalData.gameInfo)
      const { isMineInGame } = globalData;
      const { playerInfo, gameMode, endMode } = globalData.gameInfo;
      this.gameMode.getComponent(Label).string = ['', '1 vs 1', '4 team'][gameMode];
      this.endGameMode.getComponent(Label).string = ['', '经典模式', '快速模式', '大师模式'][endMode];
      this.joinBtn.interactable = !isMineInGame
      if (gameMode === 1) {
        this.oneVsoneContainer.active = true;
        console.log('playerInfo', playerInfo)
        playerInfo[0]?.portrait && loadRemoteImg(playerInfo[0]?.portrait).then(sf => this.oneVsonePlayer1Img.spriteFrame = sf)
        playerInfo[1]?.portrait && loadRemoteImg(playerInfo[1]?.portrait).then(sf => this.oneVsonePlayer2Img.spriteFrame = sf)
      } else if (gameMode === 2) {
        this.fourTeamContainer.active = true;
        playerInfo[0]?.portrait && loadRemoteImg(playerInfo[0]?.portrait).then(sf => this.player1Img.spriteFrame = sf)
        playerInfo[1]?.portrait && loadRemoteImg(playerInfo[1]?.portrait).then(sf => this.player2Img.spriteFrame = sf)
        playerInfo[2]?.portrait && loadRemoteImg(playerInfo[2]?.portrait).then(sf => this.player3Img.spriteFrame = sf)
        playerInfo[3]?.portrait && loadRemoteImg(playerInfo[3]?.portrait).then(sf => this.player4Img.spriteFrame = sf)
      }else if(gameMode == 4){
        this.fourTeamContainer.active = true;
        playerInfo[0]?.portrait && loadRemoteImg(playerInfo[0]?.portrait).then(sf => this.player1Img.spriteFrame = sf)
        playerInfo[1]?.portrait && loadRemoteImg(playerInfo[1]?.portrait).then(sf => this.player2Img.spriteFrame = sf)
        playerInfo[2]?.portrait && loadRemoteImg(playerInfo[2]?.portrait).then(sf => this.player3Img.spriteFrame = sf)
        playerInfo[3]?.portrait && loadRemoteImg(playerInfo[3]?.portrait).then(sf => this.player4Img.spriteFrame = sf)
      }
    })
    this.joinBtn.node.on(Node.EventType.TOUCH_START, () => {
      globalData.socketSend(SOCKET_TYPE.JOIN_GAME)
      globalData.socketSend(SOCKET_TYPE.GAME_INFO)
    })
    this.startBtn.node.on(Node.EventType.TOUCH_START, () => {
      globalData.socketSend(SOCKET_TYPE.START_GAME)
      globalData.socketSend(SOCKET_TYPE.GAME_INFO)
    })
    this.shareBtn.node.on(Node.EventType.TOUCH_START, () => { })
  }

  start() {

  }

  // update (dt) {}
}
