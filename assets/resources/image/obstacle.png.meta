{"ver": "1.0.25", "importer": "image", "imported": true, "uuid": "c562ac26-a10b-453c-8bc6-f951f95a0f51", "files": [".json", ".png"], "subMetas": {"obstacle": {"ver": "1.0.6", "uuid": "712b2968-3ae6-4cad-863f-c98d7dcc91b1", "importer": "sprite-frame", "rawTextureUuid": "c562ac26-a10b-453c-8bc6-f951f95a0f51", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 6, "trimY": 6, "width": 36, "height": 36, "rawWidth": 48, "rawHeight": 48, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {"mipfilter": "none"}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "c562ac26-a10b-453c-8bc6-f951f95a0f51@6c48a", "displayName": "obstacle", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "c562ac26-a10b-453c-8bc6-f951f95a0f51", "visible": false}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c562ac26-a10b-453c-8bc6-f951f95a0f51@f9941", "displayName": "obstacle", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 6, "trimY": 6, "width": 36, "height": 36, "rawWidth": 48, "rawHeight": 48, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-18, -18, 0, 18, -18, 0, -18, 18, 0, 18, 18, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [6, 42, 42, 42, 6, 6, 42, 6], "nuv": [0.125, 0.125, 0.875, 0.125, 0.125, 0.875, 0.875, 0.875], "minPos": [-18, -18, 0], "maxPos": [18, 18, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c562ac26-a10b-453c-8bc6-f951f95a0f51@6c48a", "atlasUuid": ""}, "ver": "1.0.11", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": true, "hasAlpha": true, "redirect": "c562ac26-a10b-453c-8bc6-f951f95a0f51@f9941"}}