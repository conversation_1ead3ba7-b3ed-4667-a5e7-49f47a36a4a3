import { _decorator, Component, Node, Sprite<PERSON>rame, Sprite } from 'cc';
import { I18n } from '../framework/i18n';
import { commonUtil } from '../utils/commonUtil';
const { ccclass, property } = _decorator;

@ccclass('arIcon')
export class arIcon extends Component {
    @property(SpriteFrame)
    replaceFrame: SpriteFrame

    onLoad() {
        if (I18n.languageState === 2) {
            const width = commonUtil.getWidth(this.node)
            const height =commonUtil.getHeight(this.node)
            this.node.getComponent(Sprite).spriteFrame = this.replaceFrame
            commonUtil.setWidth(this.node, width)
            commonUtil.setHeight(this.node, height)
        }
        this.replaceFrame = null
    }
}
