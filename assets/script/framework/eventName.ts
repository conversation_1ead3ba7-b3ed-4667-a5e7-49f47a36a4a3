export const EventName = {
    /** example */
    EVENT_BOTTOMNOTE: "EVENT_BOTTOMNOTE",
    /** 顶部吃棋 */
    EVENT_MAIN_TOP_EAT: "EVENT_MAIN_TOP_EAT",
    /** 顶部底注 */
    EVENT_MAIN_TOP_BOTTOMNOTE: "EVENT_MAIN_TOP_BOTTOMNOTE",
    /** 顶部金币 */
    EVENT_MAIN_TOP_COIN: "EVENT_MAIN_TOP_COIN",
    /** 顶部倍数 */
    EVENT_MAIN_TOP_MULTIPLE: "EVENT_MAIN_TOP_MULTIPLE",
    /** 更新结算界面的Layout */
    EVENT_GAMEOVER_LAYOUT: "EVENT_GAMEOVER_LAYOUT",

    /** 打开飞金币 */
    EVENT_TOP_OPEN_FLYCOIN: "EVENT_TOP_OPEN_FLYCOIN",
    /** 关闭飞金币 */
    EVENT_TOP_CLOSE_FLYCOIN: "EVENT_TOP_CLOSE_FLYCOIN",

    /** 更新topitem */
    EVENT_TOP_ITEM_UPDATE: "EVENT_TOP_ITEM_UPDATE",
    /** 调用吃棋动画 */
    UPATE_EAT_CHESS_ANIM: "UPATE_EAT_CHESS_ANIM",
    /** 调用吃棋动画 */
    EVENT_EAT_CHESS_COMBO_ANIM: "EVENT_EAT_CHESS_COMBO_ANIM",
    /** 星星动画 */
    EVENT_STAR_FLY_ANI: "EVENT_STAR_FLY_ANI",
    /** 星星动画 */
    EVENT_EXP_FLY_ANI: "EVENT_EXP_FLY_ANI",
    /** 渲染基础组件 */
    EVENT_RENDER_BASE_COMPONENT: "EVENT_RENDER_BASE_COMPONENT",

    /** 播放音效 */
    PLAY_SOUND_DICE: "PLAY_SOUND_DICE",
    PLAY_SOUND_USEMOVE: "PLAY_SOUND_USEMOVE",

    /** 顶部区域数据 */
    UPDATE_TOP_AREA_DATA: 'UPDATE_TOP_AREA_DATA',

    /** 渲染基础组件 */
    EVENT_PLAYER_RENDER_1ST: 'EVENT_PLAYER_RENDER_1ST'


}