import { MessageQueue } from './manager/MessageQueue';
import { GAME_STAGE, SOCKET_TYPE, GAME_MODE, END_MODE } from './network/constants'
import wsClient from './network/wsClient';
import WsUtil from './network/wsUtil';
import { sealBridge } from './seal/SealBridge';
import { PlayerOpStatus } from './type/index';
import GameConfig from './gameConfig';
class GlobalData {
  public WS: wsClient | null = null; //websocket实例
  public gameRoundId: string = '' // 游戏局数
  public myUserId: string = '';//当前用户userId
  public currentOpUserId: string = ''; // 当前可操作玩家userId
  public currentPlayerIndex: number = -1; // 玩家序号
  public roomId: string = '';//房间号
  public mineUserInfo: any = null;//当前用户userId
  public isMineInGame = false
  public token: string = ''; // 前后端交互token
  public playerOpStatus: PlayerOpStatus = PlayerOpStatus.Other
  public clickResetDiceTime = Date.now() // 点击重置按钮的时间
  public isKeepAlive = true // 是否保持链接通信
  public gameSkins: Record<any, any> = {} // 游戏皮肤
  public gameScaleRate: number = 1
  public roomInfo: Record<any, any> = {} // 房间信息
  public gameSubType = "";
  public isIos: string = '';
  public userId: string = "";
  public h5CbId = 0;
  public h5GameId = "0";
  public dateTime = {};

  public diceResultInfo: any = null;  // 骰子结果
  public diceList: Array<any> = [];  // 骰子数组
  public canTouchDice: boolean = true; // 是否可以点击骰子
  public noticeDice: boolean = false;  // 通知摇骰子
  public gameHosting: boolean = false; // 是否进入托管

  public gameInfo: any = {
    playerInfo: [
      // "userId":10000001,
      // "name":"昵称1",
      // "portrait":"http://xxx.png",// 头像
      // "userIndex":1,//当前用户的坐标
      // "isAuto":false,//是否托管模式
      // "portraitMask":"",//头像框地址，可为空
    ],
    chessInfo: [], //[["1-1",44]["1-2",0], ...]
    gameStatus: GAME_STAGE.INIT, // 游戏默认状态为待加入状态
    gameMode: GAME_MODE.ONE_VS_ONE, // 游戏模式
    endMode: END_MODE.CLASSIC, // 获胜模式
    resetInfo: [],
    userCoinInfo: [],

    modeData: {
      gameReward: [],
    },
  }
  sendMessageParam = {};
  unsentMessage = [];
  //用户最新的位置信息
  private _newestChessPos: Array<object> = []//: Map<string, number> = new Map();
  public get newestChessPos() {
    return this._newestChessPos;
  }
  public set newestChessPos(arr: Array<object>) {
    this._newestChessPos = arr;
  }
  public gameConfigId = "";
  public get isMyRound() {
    return globalDataInstance.currentOpUserId === globalDataInstance.myUserId // 是我的回合
  }
  public canMoveChessMap: Map<string, number[]> = null;
  //game playing animation 
  public isPlaying: boolean = false;

  /**
   * 统一的socket发送方法
   * @param type websocket交互类型
   * @param message 需要发送的业务数据
   * @param h5Callback 回调ID
   */
  public socketSend(type: SOCKET_TYPE, message: object = {}, h5Callback?: string) {
    // 本地游戏模式下跳过网络发送
    if (GameConfig.FORCE_LOCAL_MODE || GameConfig.DISABLE_NETWORK) {
      console.log(`🎮 本地模式 - 跳过网络消息: type=${type}`, message);
      return;
    }

    // console.log(`【cocos】socketSend,type:${type},message:${JSON.stringify(message)}` + "|" + new Date().getTime());
    if (!this.WS?.connected) {
      // console.log(`【cocos】socketSend,websocket close`);
      this.unsentMessage.push({
        type: type,
        message: message
      })
      return;
    }
    message = message ?? {};
    this.sendMessageParam[type] = message;
    let params: any = {
      type,
      message: Object.assign({}, {
        lzacceptlanguage: 'en',
        gameId: 0, // 临时用，
        gameType: 11,// 临时用，
        liveId: this.roomId,// 临时用，
        roomId: this.roomId,
        userId: this.userId,
        userName: this.mineUserInfo?.nickName,
        gameRoundId: this.gameRoundId,
        userPortrait: this.mineUserInfo?.portrait,
        portraitMask: this.mineUserInfo?.portraitMask
      }, { ...message }),
      token: this.token
    }
    params.env = 'pc'
    // debug模式参数设置
    if (sealBridge.debug) {
      params.message.token = this.myUserId
      params.token = this.myUserId
    }
    // console.log('send param', JSON.stringify(params))
    //没有roomId不调用BindRoom
    if (type == SOCKET_TYPE.BIND_ROOM && !this.roomId) {
      return;
    }
    //@ts-ignore
    const commandKey = message?.subCommand?.commandKey;
    if (MessageQueue.includesType(commandKey)) {
      const [userRound, round] = MessageQueue.getqueueMessage(commandKey);
      //@ts-ignore
      message.subCommand = Object.assign(message.subCommand, { userRound: userRound, round: round });
      // params.message = Object.assign(params.message, { userRound: userRound, round: round });
    }
    // console.log(`send,type:${type},params:${JSON.stringify(params)}` + "|" + new Date().getTime());
    WsUtil.send(this.WS, params, h5Callback)
    return params
  }

  /**
   * 重置global数据
   */
  public resetGlobalData() {
    this.gameInfo = {
      resetInfo: [], // 重置信息
      userCoinInfo: [], // 用户金币信息
      chessInfo: [], // 棋子信息
      playerInfo: [], // 玩家信息
      gameStatus: GAME_STAGE.JOIN, // 游戏默认状态为待加入状态
      gameMode: GAME_MODE.ONE_VS_ONE, // 游戏模式
      endMode: END_MODE.CLASSIC,// 获胜模式
      modeData: {
        gameReward: [],
      },
    }

    this.currentOpUserId = '';
    this.currentPlayerIndex = -1;
    this.isKeepAlive = false
    this.gameSkins = {}
    this.roomInfo = {}
    this.gameScaleRate = 1
    this.roomId = ''
  }

  /**
   * getUserInfoByUserId
   * @param userId 
   * @returns 
   */
  public getUserInfoByUserId(userId: string) {
    return this.gameInfo.playerInfo.find(item => item.userId == userId)
  }


  /**
   * isAllChessInHome
   * @param seatIndex 
   */
  public isAllChessInHome(seatIndex) {
    const { chessInfo } = this.gameInfo;
    let someOneNotInHome = chessInfo?.some(item => {
      let seat = item.chessId?.split('-')[0];
      let coord = item.chessIndex;
      if (seat == seatIndex && coord > 0) {
        return true
      }
    })
    return !someOneNotInHome;
  }

  /**
   * cancal user's hosting
   */
  public cancelHosting(userId: string) {
    this.socketSend(SOCKET_TYPE.OPERATION, { subCommand: { commandKey: 'cancelAutoMode' } })
  }
}

let globalDataInstance: GlobalData
(function () {
  globalDataInstance = new GlobalData()
})()

export default globalDataInstance