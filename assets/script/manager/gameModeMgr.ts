import globalData from "../globalData"
import { END_MODE, GAME_MODE } from "../network/constants"

export class GameModeMgr {
    private static _ins: GameModeMgr
    
    public static get instance() {
        if (!this._ins) this._ins = new GameModeMgr()
        return this._ins
    }
    
    // 经典模式
    public get isClassicMode() {
        return globalData.gameInfo.endMode === END_MODE.CLASSIC && [GAME_MODE.TWO_BATTLE, GAME_MODE.FOUR_BATTLE].includes(globalData.gameInfo.gameMode)
    }
    
    // 快速模式
    public get isClassicQuickMode() {
        return globalData.gameInfo.endMode === END_MODE.QUICK && [GAME_MODE.TWO_BATTLE, GAME_MODE.FOUR_BATTLE].includes(globalData.gameInfo.gameMode)
    }

    // 经典2人模式
    public get isClassicTwoMode() {
        return globalData.gameInfo.endMode === END_MODE.CLASSIC && [GAME_MODE.TWO_BATTLE].includes(globalData.gameInfo.gameMode)
    }

    // 快速2人模式
    public get isQuickTwoMode() {
        return globalData.gameInfo.endMode === END_MODE.QUICK && [GAME_MODE.TWO_BATTLE].includes(globalData.gameInfo.gameMode)
    }

}
