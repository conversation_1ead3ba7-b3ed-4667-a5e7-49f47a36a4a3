<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{0,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{112,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{224,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{336,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{448,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{560,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,112}</string>
                <key>spriteSourceSize</key>
                <string>{112,112}</string>
                <key>textureRect</key>
                <string>{{672,0},{112,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>dices.png</string>
            <key>size</key>
            <string>{784,112}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:f34c6833361190453798c72ddfb58975:669dc9c84e9c88e5b2d2a1a8feb76ea0:0b8c924a885b289e4bf3d63d1b8f8d66$</string>
            <key>textureFileName</key>
            <string>dices.png</string>
        </dict>
    </dict>
</plist>
