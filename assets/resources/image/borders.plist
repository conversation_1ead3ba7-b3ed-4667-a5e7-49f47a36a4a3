<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>blue-border.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{0,0},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>green-border.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{128,0},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>red-border.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{256,0},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>yellow-border.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{384,0},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>borders.png</string>
            <key>size</key>
            <string>{512,128}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:955a0e1962a0cd8649e23e76a376bb41:95ca61d851b464f190b9ac0a09fd9b0e:3db52054e421be99f4ee807b6640cdd9$</string>
            <key>textureFileName</key>
            <string>borders.png</string>
        </dict>
    </dict>
</plist>
