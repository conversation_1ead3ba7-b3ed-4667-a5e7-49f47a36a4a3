import { tween, Vec3, Node, v3, UITransform, UIOpacity } from "cc";
import { Easing } from "./easing";

/**
 * 弹框打开的动画类
 * <AUTHOR>
 */
export class AnimGatherMgr {
    private static _ins: AnimGatherMgr = null;
    public static get instance() {
        if (!this._ins) this._ins = new AnimGatherMgr();
        return this._ins;
    }

    openPanelAnim_0(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(0, 0, 0);
            tween(node).to(0.25, { scale: v3(1, 1, 1) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    closePanelAnim_0(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(1, 1, 1);
            tween(node).to(0.25, { scale: v3(0, 0, 0) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    openPanelAnim_1(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(0, 1, 1);
            tween(node).to(0.25, { scale: v3(1, 1, 1) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    closePanelAnim_1(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(1, 1, 1);
            tween(node).to(0.25, { scale: v3(0, 1, 1) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    openPanelAnim_2(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(1, 0, 1);
            tween(node).to(0.25, { scale: v3(1, 1, 1) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    closePanelAnim_2(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(1, 1, 1);
            tween(node).to(0.25, { scale: v3(1, 0, 1) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    openPanelAnim_3(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let startY = node.getComponent(UITransform).height;//1624/2
            node.setPosition(0, -startY, 0);
            tween(node).to(0.25, { position: new Vec3(0, 0, 0) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    closePanelAnim_3(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let endY = node.getComponent(UITransform).height;
            node.setPosition(0, 0, 0);
            tween(node).to(0.35, { position: new Vec3(0, -endY, 0) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    openPanelAnim_4(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let startX = node.getComponent(UITransform).width / 2;
            node.setPosition(startX, 0, 0);
            tween(node).to(0.25, { position: new Vec3(0, 0, 0) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    closePanelAnim_4(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let endX = node.getComponent(UITransform).width;
            node.setPosition(0, 0, 0);
            tween(node).to(0.35, { position: new Vec3(endX, 0, 0) }).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    openPanelAnim_10(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(0, 0, 0);
            tween(node).to(0.15, { scale: v3(1, 1, 1) })
                .to(0.05, { scale: v3(1, 1, 1) }).call(() => {
                    resolve("finish !");
                }).start();
        })
    }

    closePanelAnim_10(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setScale(1, 1, 1);
            tween(node).to(0.05, { scale: v3(1.1, 1.1, 1.1) })
                .to(0.15, { scale: v3(0, 0, 0) }).call(() => {
                    resolve("finish !");
                }).start();
        })
    }

    openPanelAnim_11(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let initY = node.position.y;
            let startY = initY - node.getComponent(UITransform).height;
            node.setPosition(0, startY, 0);
            tween(node).to(0.35, { position: new Vec3(0, initY, 0) },
                { easing: Easing.quintInOut }).call(() => {
                    resolve("finish !");
                }).start();
        })
    }

    closePanelAnim_11(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let initY = node.position.y;
            let endY = initY - node.getComponent(UITransform).height;
            tween(node).to(0.35, { position: new Vec3(0, endY, 0) },
                { easing: Easing.quintInOut }).call(() => {
                    resolve("finish !");
                }).start();
        })
    }

    openPanelAnim_12(node: Array<Node>) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            let contentArea = node[0];
            let mask = node[1];
            let uiOpacity = contentArea.getComponent(UIOpacity)
            uiOpacity.opacity = 0;
            let maskOpacity = mask.getComponent(UIOpacity)
            maskOpacity.opacity = 0;
            tween(uiOpacity).to(0.3, { opacity: 255 },
                { easing: Easing.quintInOut }).call(() => {
                }).start();
            tween(maskOpacity).to(0.3, { opacity: 170 },
            ).call(() => {
                resolve("finish !");
            }).start();
        })
    }

    closePanelAnim_12(node: Node) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            resolve("finish !");
        })
    }
}