import { _decorator, Component, Node, Label, v3, Layout, instantiate, Texture2D, Sprite, sys, director } from 'cc';
import { NATIVE } from 'cc/env';
import Toast from '../components/toast';
import { EventMgr } from '../framework/eventMgr';
import { EventName } from '../framework/eventName';
import { I18n } from '../framework/i18n';
import { LoadMgr } from '../framework/loadMgr';
import { UIMgr } from '../framework/uiMgr';
import globalData from '../globalData';
import ActionManager from '../manager/action';
import { AudioMgr } from '../manager/audioMgr';
import DiceManager from '../manager/dice';
import { gameLogMgr, LogSendType } from '../manager/gameLogMgr';
import { GameModeMgr } from '../manager/gameModeMgr';
import ProcessCoverManager from '../manager/processCover';
import ResetDiceManager from '../manager/resetDice';
import ViewManager from '../manager/view';
import VoiceManager from '../manager/voice';
import { SOCKET_TYPE } from '../network/constants';
import { ChessType, PlayerOpStatus, PlayerPos, PlayerStatus } from '../type';
import { commonUtil } from '../utils/commonUtil';
import { convertWorldCoordinate, loadRemoteImg, loadRemoteTexture2D, throttleFunc } from '../utils/utils';
import GameConfig from '../gameConfig';
const { ccclass, property } = _decorator;

@ccclass('playerBackup')
export class playerBackup extends Component {

    @property(Node)
    diceLayerNode: Node

    @property(Node)
    infoLayerNode: Node

    @property(Node)
    footerLayerNode: Node

    @property(Label)
    playerLabel: Label

    @property(Node)
    diceDialogNode: Node

    @property(Sprite)
    vipSprite: Sprite

    public _avatorNode: Node

    public _giftNode: Node

    public _avatorMgr: ProcessCoverManager

    private _micNode: Node

    private _hostingNode: Node

    private _diceBtnNode: Node

    private _resetDiceBtnNode: Node

    private _diceGroupNode: Node

    private _arrowNode: Node

    private _escapeNode: Node

    private _bankruptcyNode: Node

    private _nameLayerNode: Node

    private _userId: string

    private _userIndex: number | ChessType // 棋子颜色

    private _userStatus: PlayerStatus = PlayerStatus.Gaming // 玩家在线状态

    public onClickPlayerDice = throttleFunc(this.onClickOpDice.bind(this), 1000) // 点击玩家骰子防抖

    protected onLoad() {
        this.diceDialogNode?.getChildByName('DiceBox')?.on?.("click", this.onClickPlayerDice, this);
        EventMgr.on(EventName.EVENT_PLAYER_RENDER_1ST, this, this.setGame1ST);
    }

    protected onDestroy() {
        // this.diceDialogNode?.getChildByName('DiceBox')?.off?.("click", this.onClickPlayerDice, this);
        EventMgr.off(EventName.EVENT_PLAYER_RENDER_1ST, this, this.setGame1ST);
    }

    public async initPlayerView(userInfo) {
        const { userId, status, userIndex, teamName } = userInfo
        this._userIndex = userIndex
        this.node.name = this._userId = `${userId}`
        await this._setDiceLayerResource(userInfo)
        commonUtil.setOpacity(this.node, 0)
        this._nameLayerNode = this.footerLayerNode.getChildByName('nameLayer')
        this._micNode = await LoadMgr.instance.create('prefab/player/mic', this.infoLayerNode);
        // await convertPrefabToNode('prefab/player/mic', this.infoLayerNode)
        this._micNode.name = this._userId
        this._avatorNode = await LoadMgr.instance.create('prefab/player/avator', this.infoLayerNode);
        // await convertPrefabToNode('prefab/player/avator', this.infoLayerNode)
        this._hostingNode = await LoadMgr.instance.create('prefab/player/hosting', this.infoLayerNode);
        // await convertPrefabToNode('prefab/player/hosting', this.infoLayerNode)
        this._giftNode = await LoadMgr.instance.create('prefab/player/gift', this.infoLayerNode);
        // await convertPrefabToNode('prefab/player/gift', this.infoLayerNode)
        this._giftNode.name = userId
        this._avatorNode.name = userId
        this._diceBtnNode.name = userId
        this._hostingNode.name = userId
        this.initPlayerNodeLocation(ViewManager.getCurrentPlayerPosIndex(userIndex))
        this._setPlayerInfo(userInfo)
        commonUtil.setActive(this.diceDialogNode, globalData.myUserId === this._userId)
        commonUtil.setOpacity(this.node, 255)
        this.setOpDiceView(0)
        this.playPlayerStatus(status)
        this._setAvatorView(teamName)
    }
    /**
     * @zh 设置头像信息 @en set avator view about border and portrait
     */
    private _setAvatorView(teamName: string) {
        if (!this._avatorNode) return
        const avatorMgr = this._avatorNode.getComponent(ProcessCoverManager)
        avatorMgr?.setTeamName(teamName);
        this._avatorMgr = avatorMgr;
    }

    /**
     * @zh 设置头像信息 @en set avator view about border and portrait
     */
    private _setPlayerInfo(userInfo) {
        const { name, userId, userIndex, portrait, vipMedalEffect } = userInfo
        commonUtil.setLabelEllipsis(this.playerLabel, name, 15, I18n.languageState === 1)
        this._avatorNode?.getComponent(ProcessCoverManager)?.init(userId, userIndex, portrait)
        this._setModelView(vipMedalEffect, ViewManager.getCurrentPlayerPosIndex(userIndex))
    }

    /**
     * @zh 资源节点引用 @en set quote node
     */
    private async _setDiceLayerResource(userInfo) {
        this._escapeNode = this.diceLayerNode.getChildByName('escape')
        this._bankruptcyNode = this.diceLayerNode.getChildByName('bankruptcy')
        this._diceGroupNode = this.footerLayerNode.getChildByName('diceGroup')
        const diceBoxNode = this.diceDialogNode.getChildByName('DiceBox')
        this._diceBtnNode = await LoadMgr.instance.create('prefab/player/dice', diceBoxNode);
        // await convertPrefabToNode('prefab/player/dice', diceBoxNode)
        this._arrowNode = diceBoxNode.getChildByName('ArrowIcon')

        for (let i = 0; i < 3; i++) {
            const numDiceNode = instantiate(this._diceBtnNode)
            numDiceNode.setScale(commonUtil.getEqualVec3(0.5)) // size: 56 * 56
            numDiceNode.parent = this._diceGroupNode
        }
        this._resetDiceBtnNode = await LoadMgr.instance.create('prefab/player/resetDice', this.diceDialogNode);
        // await convertPrefabToNode('prefab/player/resetDice', this.diceDialogNode)
        this._resetDiceBtnNode?.getComponent(ResetDiceManager)?.initResetDiceView(userInfo)
        commonUtil.setActive(this.diceDialogNode, globalData.myUserId === this._userId)
        return null
    }

    /**
     * @zh 初始化玩家节点位置 @en init player node
     * @param posIndex 玩家位置
     */
    protected initPlayerNodeLocation(posIndex: PlayerPos) {
        const [x1, y1, x2, y2] = [330, 460, 330, 550] // 玩家节点坐标
        const [ax1, ay1, ax2, ay2] = [-80, 40, 80, 20] // 头像坐标
        const [mx1, my1, mx2, my2] = [0, 15, 0, -10] // 麦克风坐标
        const [hx1, hy1, hx2, hy2] = [-85, 120, 85, 100] // 托管图标坐标
        const [fx1, fy1, fx2, fy2] = [-85, -50, 85, 120] // 底部信息区域坐标
        const [dgx1, dgy1, dgx2, dgy2] = [25, 0, -25, 0] // 骰子列表坐标
        const [gx1, gy1, gx2, gy2] = [-115, 80, 115, 60] // 礼物坐标
        const [dx1, dy1, dx2, dy2] = [-7, -3, 7, -3] // 骰子坐标位置
        const [dyx1, dyy1, dyx2, dyy2] = [-250, 40, 250, 20] // 骰子层级节点坐标
        const [nx1, ny1, nx2, ny2] = [100, 0, -100, 0] // 玩家姓名坐标
        const diceBox = this.diceDialogNode.getChildByName('DiceBox')
        commonUtil.setIndex(diceBox, 20)
        switch (posIndex) {
            case PlayerPos.RightTop: {
                commonUtil.setAnchor(this.footerLayerNode, 1, 0.5)
                commonUtil.setAngle(diceBox.getChildByName('Bg'), 180)
                commonUtil.setPosition(this.node, v3(x1, y1, 0))
                commonUtil.setPosition(this._avatorNode, v3(ax1, ay1, 0))
                commonUtil.setPosition(this._micNode, v3(mx1, my1, 0))
                commonUtil.setPosition(this._hostingNode, v3(hx1, hy1, 0))
                commonUtil.setPosition(this.footerLayerNode, v3(fx1, fy1, 0))
                commonUtil.setPosition(this._nameLayerNode, v3(nx1, ny1, 0))
                commonUtil.setAnchor(this._nameLayerNode, 1, 1)
                commonUtil.setPosition(this._diceGroupNode, v3(dgx1, dgy1, 0))
                commonUtil.setPosition(this._giftNode, v3(gx1, gy1, 0))
                commonUtil.setPosition(this._diceBtnNode, v3(dx1, dy1, 0))
                commonUtil.setPosition(this.diceLayerNode, v3(dyx1, dyy1, 0))
                commonUtil.setAnchor(this._nameLayerNode, 1, .5)
                commonUtil.setLayoutAlign(this._diceGroupNode, Layout.HorizontalDirection.RIGHT_TO_LEFT)
                break
            }
            case PlayerPos.RightBottom: {
                commonUtil.setPosition(this.node, v3(x1, -y2, 0))
                commonUtil.setPosition(this._avatorNode, v3(ax1, ay2, 0))
                commonUtil.setPosition(this._micNode, v3(mx1, my2, 0))
                commonUtil.setPosition(this._hostingNode, v3(hx1, hy2, 0))
                commonUtil.setPosition(this.footerLayerNode, v3(fx1, fy2, 0))
                commonUtil.setAnchor(this.footerLayerNode, 1, 0.5)
                commonUtil.setPosition(this._nameLayerNode, v3(nx1, ny2, 0))
                commonUtil.setPosition(this._diceGroupNode, v3(dgx1, dgy1, 0))
                commonUtil.setPosition(this._giftNode, v3(gx1, gy2, 0))
                commonUtil.setAngle(diceBox.getChildByName('Bg'), 180)
                commonUtil.setPosition(this._diceBtnNode, v3(dx1, dy1, 0))
                commonUtil.setPosition(this.diceLayerNode, v3(dyx1, dyy2, 0))
                commonUtil.setAnchor(this._nameLayerNode, 1, .5)
                commonUtil.setLayoutAlign(this._diceGroupNode, Layout.HorizontalDirection.RIGHT_TO_LEFT)
                break
            }
            case PlayerPos.LeftTop: {
                commonUtil.setAnchor(this.footerLayerNode, 0, 0.5)
                commonUtil.setPosition(this.node, v3(-x2, y1, 0))
                commonUtil.setPosition(this._avatorNode, v3(ax2, ay1, 0))
                commonUtil.setPosition(this._micNode, v3(mx2, my1, 0))
                commonUtil.setPosition(this._hostingNode, v3(hx2, hy1, 0))
                commonUtil.setPosition(this.footerLayerNode, v3(fx2, fy1, 0))
                commonUtil.setPosition(this._nameLayerNode, v3(nx2, ny1, 0))
                commonUtil.setPosition(this._diceGroupNode, v3(dgx2, dgy2, 0))
                commonUtil.setPosition(this._giftNode, v3(gx2, gy1, 0))
                commonUtil.setPosition(this._diceBtnNode, v3(dx2, dy2, 0))
                commonUtil.setPosition(this.diceLayerNode, v3(dyx2, dyy1, 0))
                commonUtil.setAnchor(this._nameLayerNode, 0, .5)
                this.playerLabel.horizontalAlign = Label.HorizontalAlign.LEFT
                commonUtil.setLayoutAlign(this.diceDialogNode, Layout.HorizontalDirection.RIGHT_TO_LEFT)
                commonUtil.setLayoutAlign(this._diceGroupNode, Layout.HorizontalDirection.LEFT_TO_RIGHT)
                break
            }
            case PlayerPos.LeftBottom:
            default: {
                commonUtil.setAnchor(this.footerLayerNode, 0, 0.5)
                commonUtil.setPosition(this.node, v3(-x2, -y2, 0))
                commonUtil.setPosition(this._avatorNode, v3(ax2, ay2, 0))
                commonUtil.setPosition(this._micNode, v3(mx2, my2, 0))
                commonUtil.setPosition(this._hostingNode, v3(hx2, hy2, 0))
                commonUtil.setPosition(this.footerLayerNode, v3(fx2, fy2, 0))
                commonUtil.setPosition(this._nameLayerNode, v3(nx2, ny2, 0))
                commonUtil.setPosition(this._diceGroupNode, v3(dgx2, dgy2, 0))
                commonUtil.setPosition(this._giftNode, v3(gx2, gy2, 0))
                commonUtil.setPosition(this._diceBtnNode, v3(dx2, dy2, 0))
                commonUtil.setPosition(this.diceLayerNode, v3(dyx2, dyy2, 0))
                commonUtil.setAnchor(this._nameLayerNode, 0, .5)
                this.playerLabel.horizontalAlign = Label.HorizontalAlign.LEFT
                commonUtil.setLayoutAlign(this.diceDialogNode, Layout.HorizontalDirection.RIGHT_TO_LEFT)
                commonUtil.setLayoutAlign(this._diceGroupNode, Layout.HorizontalDirection.LEFT_TO_RIGHT)
                break
            }
        }
    }

    /**
     * @zh 设置操作骰子区域点数 @en set dice view
     * @param diceNum 
     * @param isShine 是否显示粒子特效
     */
    public setOpDiceView(diceNum: number, isShine?: boolean) {
        this._diceBtnNode?.getComponent(DiceManager)?.setDiceView(diceNum, isShine)
    }

    public setIconView(node: Node) {
        if (!node?.isValid) return
        const iconNode = node.getChildByName('Icon')
        const { x } = convertWorldCoordinate(this.node)
        iconNode.setPosition(
            v3(
                x > 500 ? 30 : -30,
                0,
                0
            ))
        if (x > 500) {
            iconNode.angle = -10
        }
    }

    /**
     * @zh 设置勋章视图 @en update vip model view
     * @param vipMedal 勋章地址
     */
    protected _setModelView(vipMedalEffect: string, direction: number) {
        console.log('_setModelView====', vipMedalEffect)
        if (!vipMedalEffect && [PlayerPos.RightTop, PlayerPos.RightBottom].includes(direction)) {
            // if (sys.Platform.ANDROID || sys.Platform.IOS)
            NATIVE && commonUtil.setPosition(this._nameLayerNode, v3(140, 0, 0))
        }
        loadRemoteTexture2D(vipMedalEffect, {}).then((texture2d: Texture2D) => {
            const node = this._nameLayerNode?.getChildByName('vipSprite')
            if (!node?.isValid || !texture2d) return
            commonUtil.setActive(node, !!vipMedalEffect)
            ActionManager.playSpriteFrameAnimation(node, texture2d, 48, 1000 / 12, -1)
        })
    }

    /**
     * @zh 设置某一个子节点显示
     */
    private setOnlyChildVisiable(parentNode: Node, chindNodeName: string) {
        parentNode?.children?.forEach((child) => {
            commonUtil.setActive(child, child.name === chindNodeName)
        })
    }

    /**
     * @zh 显示玩家在游戏中的状态 @en show player status about current game
     * @param status 玩家状态
     */
    public playPlayerStatus(status: PlayerStatus) {
        this._userStatus = status
        const iconNode = this._avatorNode?.getComponent(ProcessCoverManager)?.coverSpComp?.node
        if (!iconNode) return
        commonUtil.setActive(this._hostingNode, false)
        commonUtil.setActive(iconNode.getChildByName('Quit'), false)
        commonUtil.setActive(iconNode.getChildByName('Hosting'), false)
        // commonUtil.setActive(iconNode.getChildByName('Leave'), false)
        // console.log("playPlayerStatus=====", status)

        switch (status) {
            case PlayerStatus.Hosting: {
                if (this._userId !== globalData.myUserId) return
                commonUtil.setActive(iconNode.getChildByName('Hosting'), true)
                this.setOnlyChildVisiable(this.diceLayerNode, 'diceOpDialog')
                commonUtil.setActive(this._hostingNode, true)
                break
            }
            case PlayerStatus.Quit: {
                // commonUtil.setActive(iconNode.getChildByName('Quit'), true)
                this.setIconView(this._escapeNode)
                this.setOnlyChildVisiable(this.diceLayerNode, 'escape')
                break
            }
            case PlayerStatus.Busting: {
                this.setIconView(this._bankruptcyNode)
                this.setOnlyChildVisiable(this.diceLayerNode, 'bankruptcy')
                break
            }
            case PlayerStatus.NO1: {
                if (GameModeMgr.instance.isClassicTwoMode || GameModeMgr.instance.isQuickTwoMode) return // 2人模式不显示1st
                this.setOnlyChildVisiable(this.diceLayerNode, '1st')
                break
            }
            case PlayerStatus.LEAVE: {
                commonUtil.setActive(iconNode.getChildByName('Leave'), true)
                this.setGame1ST(this._userIndex)
                break
            }
            default: {
                this.setOnlyChildVisiable(this.diceLayerNode, 'diceOpDialog')
                break
            }
        }
    }

    /**
     * @zh 设置游戏第一名视图 @en set game no1
     */
    public setGame1ST(userIndex: number) {
        if (userIndex !== this._userIndex) return
        const node = this.diceLayerNode.getChildByName('1st')
        const { x } = commonUtil.convertToWorldSpaceARToZero(this.node)
        node.setPosition(
            v3(
                x > 500 ? 70 : -70,
                -10,
                0
            ))
        this.playPlayerStatus(PlayerStatus.NO1)
    }

    /**
     * @zh 显示摇骰子箭头 @en display ani arrow when player is playing
     */
    public toggleArrowVisiable(isVisiable?: boolean) {
        commonUtil.setActive(this._arrowNode, !!isVisiable && globalData.isMyRound)
    }

    /**
     * @zh 设置骰子列表视图 @en spread dice group
     */
    public setDiceGroupView(diceNums?: number[]) {
        const hasDice = !!diceNums?.length
        commonUtil.setActive(this._diceGroupNode, hasDice)
        commonUtil.setActive(this.vipSprite.node, !hasDice)
        commonUtil.setActive(this.playerLabel.node, !hasDice)
        if (!!!diceNums?.length) {
            this.setOpDiceView(0)
        }
        this._diceGroupNode?.children?.forEach((node: Node, index: number) => {
            const diceNum = diceNums?.[index] || 0
            node.getComponent(DiceManager).setDiceView(diceNum)
            commonUtil.setActive(node, Number(diceNum) >= 1)
        })
    }

    /**
     * @zh 播放骰子结果 @en display dice info
     */
    public async playDiceResult(diceNum: number, diceNumArray?: number[]) {
        await this._diceBtnNode?.getComponent(DiceManager)?.playDiceResult(diceNum)
        this.setDiceGroupView(diceNumArray || [])
        this.toggleArrowVisiable()
        return Promise.resolve()
    }

    /**
     * @zh 切换对话框展示 @en toggle visiable dice with dialog
     */
    public toggleDialogVisiable(isVisiable?: boolean) {
        if (globalData.myUserId === this._userId) {
            return !isVisiable && this.setOpDiceView(0)
        }
        commonUtil.setActive(this.diceDialogNode, isVisiable)
    }

    public forceCloseVisiable(){
        commonUtil.setActive(this._arrowNode, false);
    }

    /**
     * @zh 点击破产按钮 @en click bankruptcy btn
     */
    public onClickBankruptcyTips() {
        if (globalData.myUserId === this._userId) {
            UIMgr.instance.showDialog("prefab/dialog/bankruptcyTips");
        }
    }

    /**
     * @zh 玩家操作倒计时 @en countdown pointing player operation
     * @param countTime 时间
     * @param isExpand 放大游戏特效
     */
    public playPlayerOpCountTime(countTime: number, isExpand: boolean = false) {
        let lastTime = 0
        let totalTime = 0 //已经倒计时了多久
        let countdownEffectFlag = {
            threeFlag: false,
            twoFlag: false,
            oneFlag: false
        }
        if (countTime > 0) {
            isExpand && ActionManager.playAnimation(this._avatorNode, 'enlarge', 1)
        }
        ActionManager?.playCountdown(
            this._avatorNode?.getComponent(ProcessCoverManager)?.progressComp,
            countTime,
            (dt) => {
                if (!lastTime) lastTime = dt
                totalTime += (dt - lastTime)
                lastTime = dt
                if (countTime - totalTime <= 3000 && !countdownEffectFlag.threeFlag) {
                    countdownEffectFlag.threeFlag = true
                    // director.emit('PLAY_EFFECT', EFFECT_NAME.countDownEffect)
                    AudioMgr.instance.play('audio/sound_outTime');
                }
                else if (countTime - totalTime <= 2000 && !countdownEffectFlag.twoFlag) {
                    countdownEffectFlag.twoFlag = true
                    AudioMgr.instance.play('audio/sound_outTime');
                } else if (countTime - totalTime <= 1000 && !countdownEffectFlag.oneFlag) {
                    countdownEffectFlag.oneFlag = true
                    AudioMgr.instance.play('audio/sound_outTime');
                }
            })
    }

    /**
     * @zh 重置按钮倒计时 @en reset countdown
     */
    public playPlayerResetCountTime(countTime: number) {
        this._resetDiceBtnNode?.getComponent(ResetDiceManager)?.playPlayerResetCountTime(countTime)
    }

    /**
     * @zh 操作骰子点击事件
     */
    public async onClickOpDice() {
        globalData.cancelHosting(this._userId)
        if (this._userStatus !== PlayerStatus.Gaming) return
        if (!(globalData.isMyRound && globalData.playerOpStatus === PlayerOpStatus.Other)) return

        if (!globalData.canTouchDice) {
            return;
        }

        if (!globalData.noticeDice) {
            return;
        }

        // 本地模式下直接触发骰子点击事件，不修改状态
        if (GameConfig.FORCE_LOCAL_MODE) {
            console.log('🎲 本地模式 - 骰子点击事件');
            director.emit('dice-clicked');
            return;
        }

        // 网络模式下的状态管理
        globalData.canTouchDice = false;
        globalData.noticeDice = false;

        gameLogMgr.instance.setTouchTime();
        let logInfo = gameLogMgr.instance.getCurIdAndTime(LogSendType.SelectDice);
        globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand: { commandKey: "rollDice", ...logInfo } })
        ViewManager.closePlayerCountdownEffect()

        await this.touchLoccalHandle();
    }

    public async touchLoccalHandle() {
        this.toggleArrowVisiable();
        await this._diceBtnNode?.getComponent(DiceManager)?.nowPlayDiceResult();
        this.setDiceGroupView(globalData.diceList || []);
    }

    /**
     * @zh 点击逃跑图标 @en click escaping icon
     */
    protected onClickExcapeIcon(e) {
        const node = e?.target
        if (node?.active) {
            const targetVec = commonUtil.convertToNodeSpaceAR(ViewManager.gameInstance.popUpLayer, commonUtil.convertToWorldSpaceARToZero(node))
            Toast.show(window.cocosI18n?.playerExitTip, 3000, v3(targetVec.x, targetVec.y + 80, targetVec.z))
        }
    }

    /**
     * @zh 设置玩家声音波纹状态 @en play mic animation
     */
    playMicWave() {
        this._micNode?.getComponent(VoiceManager)?.playMicWave()
    }

    /**
     * @zh 设置麦克风可用/禁用视图 @en set mic enable/disable view
     */
    public setMicViewStatus(status: boolean) {
        this._micNode?.getComponent(VoiceManager)?.toggleMicView(status)
    }

    /**
     * @zh 设置重置按钮视图 @en set reset btn view
     */
    public setResetDiceView(resetLabel: string) {
        this._resetDiceBtnNode?.getComponent(ResetDiceManager)?.setResetDiceView(resetLabel)
    }

    /**
     * @zh 获取重置按钮坐标信息 @en get reset btn info
     */
    public getResetButtonInfo() {
        try {
            const width = commonUtil.getWidth(this._resetDiceBtnNode)
            const height = commonUtil.getHeight(this._resetDiceBtnNode)
            const { x, y } = convertWorldCoordinate(this._resetDiceBtnNode)
            return { x: x / 2, y: y / 2, width: width * globalData.gameScaleRate / 2, height: height * globalData.gameScaleRate / 2 }
        } catch (e) {
            return { x: 0, y: 0, width: 0, height: 0 }
        }
    }

    /**
     * @zh 设置骰子皮肤 @en set dice skins included dice、 diceAnim and shineAnim
     */
    public setDiceSkins(diceTexture: Texture2D, diceAnimTexture: Texture2D, diceShineTexture: Texture2D) {
        const diceMgr = this._diceBtnNode.getComponent(DiceManager)
        this._diceGroupNode.children.forEach(node => {
            node.getComponent(DiceManager)?.setCustomDiceSkin(diceTexture)
        })
        diceMgr?.setCustomDiceSkin(diceTexture, diceAnimTexture, diceShineTexture)
    }

    /**
     * @zh 获取玩家坐标以及大小 @en 
     */
    public getPlayerCoorInfo() {
        if (!this.node.isValid) return {}
        const targetPosition = convertWorldCoordinate(this.node)
        return {
            px: targetPosition?.x,
            py: targetPosition?.y,
            pWidth: commonUtil.getWidth(this.node),
            pHeight: commonUtil.getHeight(this.node),
        }
    }
}
