import { Component, instantiate, Node, NodePool, Prefab, Tween, v3, _decorator } from "cc";
import { EventMgr } from "../framework/eventMgr";
import { EventName } from "../framework/eventName";
import { I18n } from "../framework/i18n";
import { LoadMgr } from "../framework/loadMgr";
import { UIMgr } from "../framework/uiMgr";
import { AudioMgr } from "../manager/audioMgr";
import { GameModeMgr } from "../manager/gameModeMgr";
import ViewManager from "../manager/view";
import { commonUtil } from "../utils/commonUtil";
import BalanceManager from "./balance";
import NumEffectManager from "./numEffect";
import TopArea from "./topArea";


const { ccclass } = _decorator;


@ccclass("BaseComponentManager")
export default class BaseComponentManager extends Component {

  private _isInitRender = false

  balanceNodes: Array<Node> = []

  numNodes: Array<Node> = []

  obstacleNode: Node

  _nodePool: NodePool;
  _nodePoolCoin: NodePool;

  protected async onLoad() {
    EventMgr.on(EventName.EVENT_RENDER_BASE_COMPONENT, this, this.initBaseComponent)
  }

  protected onDestroy() {
    EventMgr.off(EventName.EVENT_RENDER_BASE_COMPONENT, this, this.initBaseComponent)
  }

  public async initBaseComponent() {
    if (this._isInitRender) return
    this._isInitRender = true
    if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
      UIMgr.instance.showDialog('prefab/classicMode/classicTopArea', null, this.node, 1);
      UIMgr.instance.showDialog('prefab/anim/combo', null, this.node, 20);
      UIMgr.instance.showDialog('prefab/anim/star', null, this.node, 30);
      UIMgr.instance.showDialog('prefab/anim/exp', null, this.node, 30);
      UIMgr.instance.showDialog('prefab/anim/eatChessEffect', null, this.node);
    } else {
      this._nodePool = new NodePool("NumEffect");
      this._nodePoolCoin = new NodePool("NumEffectCoin");
      UIMgr.instance.showDialog('prefab/topArea', null, this.node, 1);
      UIMgr.instance.showDialog('prefab/anim/eatChessEffect', null, this.node);
      UIMgr.instance.showDialog('prefab/anim/coinList', null, this.node, 20);
      this.handleNumNodes()
      this.handleBalanceNodes()
    }
  }

  public destroyMasterView() {
    Tween.stopAll()
    // this.coinSlideNode?.destroy()
    ViewManager.coinListScript?.getNode()?.destroy();
    this.numNodes.forEach((node) => {
      node?.destroy()
    })
    this.balanceNodes.forEach((node) => {
      node?.destroy()
    })
    // this.numNodes = []
    this.balanceNodes = []
    // this.coinSlideNode = null
    commonUtil.setOpacity(this.node, 0)
  }

  private async handleNumNodes() {
    if (!this._nodePool) {
      this._nodePool = new NodePool("NumEffect");
    }
    if (!this._nodePoolCoin) {
      this._nodePoolCoin = new NodePool("NumEffectCoin");
    }
    // const targetPref: Prefab = await loadLoaclResource('prefab/anim/numEffect', Prefab);
    const targetPref: Node = await LoadMgr.instance.createPrefab('prefab/anim/numEffect');
    if (!targetPref || !this.numNodes) return
    for (let i = 0; i < 6; i++) {
      const targetNode: Node = instantiate(targetPref)
      this._nodePool.put(targetNode);
      // this.numNodes.push(targetNode)
      // targetNode.parent = this.node
      // if (i === 2) {
      //   targetNode.getComponent(NumEffectManager).initScrollItem()
      // }
    }

    for (let i = 0; i < 2; i++) {
      const numNode = instantiate(targetPref);
      const numMgr = numNode.getComponent(NumEffectManager)

      numMgr.initScrollItem();
      this._nodePoolCoin.put(numNode);
    }
  }

  private async handleBalanceNodes() {
    // const targetPref: Prefab = await loadLoaclResource('prefab/anim/balance', Prefab)
    const targetPref: Node = await LoadMgr.instance.createPrefab('prefab/anim/balance');
    if (!targetPref || !this.balanceNodes) return
    for (let i = 0; i < 4; i++) {
      const targetNode: Node = instantiate(targetPref)
      this.balanceNodes.push(targetNode)
      targetNode.parent = this.node
    }
  }


  /**
   * @description control balance anim Effect
   */
  public playBanlanceAnimation(banlanceInfo: Array<{ userIndex: number, balance: number }>) {
    return new Promise((resolve) => {
      banlanceInfo.forEach((info, index) => {
        const { userIndex, balance } = info
        if (!userIndex) return
        this.balanceNodes[index]
          ?.getComponent(BalanceManager)
          ?.playBalanceAnimation(userIndex, balance || 0)
      })
      resolve('')
    })
  }

  /**
   * @description coin slide Effect
   */
  public playCoinSliceAnimation(userIndex: number, userId: string, index = 0) {
    this.scheduleOnce(() => {
      if (!ViewManager.coinListScript?.getNode()?.isValid) return
      const { worldPos } = ViewManager.topAreaScript.getCoinWorldPosById(userId, index)
      const coinNum = 15
      commonUtil.setActive(ViewManager.coinListScript?.getNode(), true)
      ViewManager.coinListScript?.getNode().setPosition(commonUtil.convertToNodeSpaceAR(this.node, worldPos))
      ViewManager.coinListScript?.playSlideAnimation({
        coinNum,
        aniDuration: 0.95 / coinNum,
        userIndex,
      })
    }, .5)
  }

  /**
   * @description toggle op modal
   */
  public togggleTopModal(userId?) {
    ViewManager.topAreaScript?.getComponent(TopArea)?.openFlyCoinAnim(userId)
  }

  /**
   * @en eat chess effect @zh 吃棋子特效
   */
  public playEatChessAnimation(targetNode: Node, extraData?: Record<string, any>) {
    EventMgr.dispatchEvent(EventName.UPATE_EAT_CHESS_ANIM, targetNode);
    EventMgr.dispatchEvent(EventName.EVENT_EAT_CHESS_COMBO_ANIM, extraData?.combo || 0);
  }

  /**
    * @description 播放数字动画 play num effect 
    */
  public async playNumEffectAnimation(userId: string, multipleStr: string, eatChessNumStr: string, coinStr: string, balance, index = null) {
    const { multuple, chess, coin } = ViewManager.topAreaScript.getComponent(TopArea).getCoinWorldPosById(userId, index);
    const tarVecs = [
      commonUtil.convertToNodeSpaceAR(this.node, multuple?.position),
      commonUtil.convertToNodeSpaceAR(this.node, chess?.position),
      commonUtil.convertToNodeSpaceAR(this.node, coin?.position),
    ]
    // console.log("---------1")
    for (let index = 0; index < 3; index++) {
      if (index == 1 && (Number(eatChessNumStr) <= 0 || Number(balance) <= 0)) {
        continue;
      }
      if (index == 2 && Number(balance) <= 0) {
        continue;
      }
      let numNode = null;
      if (index == 2) {
        numNode = this._nodePoolCoin.get()
      } else {
        numNode = this._nodePool.get();
      }

      // console.log("---------2")

      // console.log("------------------+++++>>>>>", numNode);
      let numMgr = null;
      if (!numNode) {
        // console.log("------------------+++++", numNode);
        numNode = await LoadMgr.instance.createPrefab('prefab/anim/numEffect')
        // console.log("------------------", numNode);
        if (numNode) {
          numMgr = numNode.getComponent(NumEffectManager)
          if (index == 2) {
            numMgr.initScrollItem()
          }
        }
      } else {
        numMgr = numNode.getComponent(NumEffectManager)
      }

      // console.log("---------3")
      if (numNode) numNode.parent = this.node;

      switch (index) {
        case 0: {
          // console.log("---------4")
          commonUtil.setPosition(numNode, v3(tarVecs[index].x + 20 * (I18n.languageState === 2 ? -1 : 1), tarVecs[index].y + 15, 0))
          commonUtil.setAnchorY(numNode, 0)
          numMgr.playAnimation(multipleStr).then(() => {
            this._nodePool.put(numNode);
          })
          AudioMgr.instance.play('audio/sound_numScale')
          break
        }
        case 1: {
          // console.log("---------5")
          commonUtil.setPosition(numNode, v3(tarVecs[index].x + 20 * (I18n.languageState === 2 ? -1 : 1), tarVecs[index].y + 15, 0))
          commonUtil.setAnchorY(numNode, 0)
          numMgr.playAnimation(eatChessNumStr)
          AudioMgr.instance.play('audio/sound_numScale')
          break
        }
        default: {
          // console.log("---------6")
          commonUtil.setPosition(numNode, v3(tarVecs[index].x + 20 * index * (I18n.languageState === 2 ? -1 : 1), tarVecs[index].y - 5, 0))
          numMgr.playAnimation(coinStr, true)
          AudioMgr.instance.play('audio/sound_numScale')
          break
        }
      }
    }
  }
}
