export enum ludoErrorCode {
    /**失败*/
    RCODE_FAIL = 1,

    /**无效参数*/
    RCODE_INVALID_PARAM = 2,

    /**玩家未找到*/
    RCODE_PLAYER_NOT_FOUND = 100,

    /**当前回合不属于自己*/
    RCODE_PLAYER_ROUND_CAN_NOT_OP = 101,

    /**本回合已完成，不可重复操作*/
    RCODE_PLAYER_USER_ROUND_CAN_NOT_OP = 102,

    /**命令未找到*/
    RCODE_COMMAND_NOT_FOUND = 404,

    /**玩家状态已离开 */
    RCODE_PLAYER_STATUS_LEAVE = 103,

    /*************业务返回码*************/
    /**摇骰子失败*/
    RCODE_DICE_FAIL = 2001,

    /**移动失败*/
    RCODE_MOVE_FAIL = 2002,

    /**不允许重置骰子*/
    RCODE_RESET_DICE_CAN_NOT_OP = 3001,

    /**超时 不允许重置骰子*/
    RCODE_RESET_DICE_CAN_NOT_OP_FOR_TIME_OUT = 3002,

    /**  业务错误,不允许重置骰子 */
    RCODE_RESET_DICE_CAN_NOT_OP_BUSINESS_ERRORS = 100001,

    /** 参数错误,不允许重置骰子 */
    RCODE_RESET_DICE_CAN_NOT_OP_INVALID_PARAM = 100002,

    /**余额不足，不允许重置骰子*/
    RCODE_RESET_DICE_CAN_NOT_OP_FOR_NOT_SUFFICIENT_FUNDS = 100003,

    /**局不存在，不允许重置骰子*/
    RCODE_RESET_DICE_CAN_NOT_OP_ROUND_NOT_EXIST = 100004,

    /** 重置次数限制，不允许重置骰子 */
    RCODE_RESET_DICE_CAN_NOT_OP_FOR_COUNT = 100005,
}