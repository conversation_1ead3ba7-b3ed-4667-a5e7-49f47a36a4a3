<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{0,0},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{72,0},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{144,0},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{216,0},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{288,0},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>rank.png</string>
            <key>size</key>
            <string>{360,72}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:e4f85f7c36806d660413a9a28ae91114:3bfbfa0d6fd7d2ae8cc5963e11a714bf:74cc68f98beed4c8a96adb0c26f30229$</string>
            <key>textureFileName</key>
            <string>rank.png</string>
        </dict>
    </dict>
</plist>
