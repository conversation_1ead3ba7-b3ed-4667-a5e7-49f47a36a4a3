
import { _decorator, Node, Component } from "cc";
import { AnimGatherMgr } from "./animGatherMgr";
import { PanelTools } from "./panelTools";

const { ccclass, property } = _decorator;

/**
 * 基类弹窗类或者节点类
 * 都以来于此基类
 * 一般 addChild 之后  进行init 
 * <AUTHOR>
 */
@ccclass("BasePanel")
export class BasePanel extends Component {
    private adaptImgObj: object;
    public view: { [name: string]: Node } = {};
    protected loadAllObject(root: Node) {
        const childrenLen = root.children.length
        for (let i = 0; i < childrenLen; i++) {
            this.view[root.children[i].name] = root.children[i];
            this.loadAllObject(root.children[i]);
        }
    }

    /** 传递过来的参数 */
    protected myData: any;
    /** 工具类 */
    protected tools: PanelTools;

    onLoad() {
        this.loadAllObject(this.node);
        this.adaptImgObj = {};
        this.tools = new PanelTools(this.view, this.adaptImgObj);
    }

    init(data: any) {
        this.myData = data;
    }

    protected onEnable(): void {
        this.onMessageEvent();
    }

    protected onDisable(): void {
        this.offMessageEvent();
    }

    protected onDestroy(): void {
        this.tools.destroy();
        this.tools = null;
        this.view = null;
        this.adaptImgObj = null;
    }

    /** 添加事件 */
    protected onMessageEvent() { };

    /** 关闭事件 */
    protected offMessageEvent() { };

}