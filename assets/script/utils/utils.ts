import { SpriteFrame, Texture2D, Asset, resources, assetManager, Node, instantiate, Prefab, ImageAsset, Rect } from "cc";
import { I18n } from "../framework/i18n";
import { ChessType, PlayerPos, Scene } from "../type/index"
import { commonUtil } from "../utils/commonUtil";

/**
 * @zh 加载远程图片 @en load remote image
 */
export const loadRemoteImageAsset = (url: string, ext: Record<string, any>): Promise<ImageAsset> => {
  return new Promise((resolve) => {
    try {
      assetManager.loadRemote<ImageAsset>(url, { ...ext }, function (err, imageAsset) {
        if (err) {
          resolve(null)
        }
        else {
          resolve(imageAsset)
        }
      })
    } catch (e) { resolve(null) }
  })
}

/**
 * @zh 加载远程2d贴图 @en load remote texture2d
 */
export const loadRemoteTexture2D = async (url: string, ext: Record<string, any>): Promise<Texture2D> => {
  if (!url) {
    return;
  }

  const imageAsset: ImageAsset = await loadRemoteImageAsset(url, ext)
  if (!imageAsset) return null
  const texture = new Texture2D()
  texture.image = imageAsset
  return texture
}

/**
 * @zh 加载远程图片 @en 加载图片
 */
export const loadRemoteImg = async (url: string): Promise<SpriteFrame> => {
  try {
    if (!url) return null
    const texture2d: Texture2D = await loadRemoteTexture2D(url, /(.png|.jpg|.jpeg)$/g.test(url) ? {} : { ext: '.png' })
    if (!texture2d) return
    const spriteFrame = new SpriteFrame()
    spriteFrame.texture = texture2d
    return spriteFrame
  } catch (e) {
    return null
  }
}
/**
 * @description load local resource 
 * @param url 
 * @param type 
 * @returns 
 */
export const loadLoaclResource = <T extends Asset>(url: string, type: typeof Asset): Promise<T> => {
  if (type == SpriteFrame) {
    url = url + "/spriteFrame"
  }
  return new Promise((resolve) => {
    resources.load(
      url,
      type,
      (error: Error, resrouce: any) => {
        resolve(error ? null : resrouce)
      }
    )
  })
}

/**
 * @description load remote resource 
 * @param url 
 * @param type 
 */
export const loadRemoteReource = <T extends Asset>(url: string, type: typeof Asset): Promise<T> => {
  return new Promise((resolve) => {
    assetManager.loadRemote(
      url,
      (error: Error, resrouce: T) => {
        resolve(error ? null : resrouce)
      })
  })
}

/**
 * @description convert chess enum key
 */
export const convertChessKey = (type: ChessType | string) => {
  for (const key in ChessType) {
    if (+key === +type) return ChessType[key]
  }
}


export const reverseLabel = (str: string) => {
  return I18n.languageState === 2 ? (str || '').split('').reverse().toString() : str
}

/**
 * @description convert player enum key
 */
export const convertPlayerPos = (type: PlayerPos) => {
  for (const key in PlayerPos) {
    if (+key === +type) return PlayerPos[key]
  }
}


/**
 * @description node convert world coordinate
 */
export const convertWorldCoordinate = (node: Node) => {
  const { x, y } = commonUtil.convertToWorldSpaceAR(node.parent, node.getPosition());// node.parent.convertToWorldSpaceAR(node.getPosition())
  return { x, y }
}

/**
 * @zh 获取长贴图某块精灵 @en 
 */
export const getTextureFrame = (texture2D: Texture2D, offsetX: number, width: number, height: number): SpriteFrame => {
  const newFrame = new SpriteFrame()
  newFrame.texture = texture2D
  newFrame.rect = new Rect(offsetX, 0, width, height)
  return newFrame
}

/**
 * @desctiption 获取游戏
 */
export const isInScene = (sceneName: Scene) => {
  let isInScene = false
  switch (`${getUrlParam1("scene")}`.toLocaleLowerCase()) {
    case Scene.Result: {
      isInScene = sceneName === Scene.Result
      break
    }
    case Scene.Guide: {
      isInScene = sceneName === Scene.Guide
      break
    }
    case Scene.Game: {
      isInScene = sceneName === Scene.Game
    }
    default: {
      break
    }
  }
  return isInScene
}


/**
 * @description 将预制体转换为对应节点
 * @弃用
 */
export const convertPrefabToNode = async (prefabUrl: string, parentNode?: Node) => {
  const targetPref: Prefab = await loadLoaclResource(prefabUrl, Prefab)
  if (!targetPref) return
  const node = instantiate(targetPref)
  if (parentNode) node.parent = parentNode
  return node
}

/**
 * 获取href url
 * @param e 
 * @param t 
 * @returns 
 */
export const getUrlParam1 = (e, t = window.location.href) => {
  const n = {};
  try {
    const e = t.split("?")[1].split("&");
    for (const t in e) if (e.hasOwnProperty(t)) {
      const o = e[t].split("="),
        i = o[0],
        r = decodeURIComponent(o[1]);
      n[i] = r
    }
  } catch (e) { }
  return n[e]
}

/**
 * @zh 防抖 @en throttleFunc
 */
export const throttleFunc = (func: Function, timing = 2000) => {
  let timer = null
  const context = this
  return function(...args) {
    if (timer) return
    func.apply(context, args)
    timer = setTimeout(() => {
      timer = null
    }, timing)
  }
}
