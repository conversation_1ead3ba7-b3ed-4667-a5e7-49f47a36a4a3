import { color, Color, Component, instantiate, Label, Node, Sprite, SpriteAtlas, Vec3, _decorator } from 'cc';
import { largeScaleRate } from '../gameConfig';
import globalData from '../globalData';
import { SOCKET_TYPE } from '../network/constants';
import { ELayerIndex } from '../type/enum';
import { ChessType, DialogType, FacialType } from '../type/index';
import { commonUtil } from '../utils/commonUtil';
import { convertChessKey, loadLoaclResource } from '../utils/utils';
import { gameLocalServer } from './gameLocalServer';
import { gameLogMgr, LogSendType } from './gameLogMgr';
import { GameModeMgr } from './gameModeMgr';
import ViewManager from './view';

const { ccclass, property } = _decorator;

@ccclass("ChessDialogManager")
export default class ChessDialogManager extends Component {
  @property(Node)
  chessDialogNode: Node = null; // 棋子对话框

  @property(Node)
  dialogPointIconNode: Node; // 对话框小角指引小角

  // @property(Node)
  // facialNode: Node = null; // 表情资源

  @property(Node)
  selectDiceNode: Node = null; // 选择骰子资源

  @property(Node)
  selectDecorationalNode: Node = null; // 选择装饰节点

  blockAtlas: SpriteAtlas = null; // 选择骰子贴图资源


  async onLoad() {
    this.selectDiceNode.setScale(commonUtil.getEqualVec3(1 / largeScaleRate))
    this.blockAtlas = await loadLoaclResource('image/blocks', SpriteAtlas)
    const diceListNode = this.selectDiceNode.getChildByName('DiceList')
    // 由于骰子数有3个
    while (diceListNode.children.length < 3) {
      instantiate(diceListNode.children[0]).parent = diceListNode
    }
  }

  /**
   * @name 选中骰子
   */
  public onSelectChessEvent(e) {
    this.toggleDialogVisiable()
    const chessNode = e.target as Node
    const chessId = chessNode.name;
    const clickNum = chessNode.getChildByName('Label').getComponent(Label).string;
    console.log(`所属chessId:${chessId}, 点击数字:${clickNum}`)
    let subCommand = {
      commandKey: 'chessMoveSelect',
      chessId,
      diceNum: clickNum
    }
    let logInfo = gameLogMgr.instance.getCurIdAndTime(LogSendType.ChessMove);
    globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand, ...logInfo })
    // 关掉棋子波纹
    ViewManager.closeDiceWave();
    console.error("local====chessDialogMove====", GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode)
    if (!gameLocalServer.instance.isCanGameMode()) return
    const movePath = gameLocalServer.instance.getMoveInfoList(chessId, Number(clickNum));
    const eatOff = gameLocalServer.instance.getEatOffList(movePath, chessId);
    const finalChess = gameLocalServer.instance.getFinalChessesList(movePath, chessId, eatOff);
    const obsList = gameLocalServer.instance.getObsList(eatOff);
    const opIndex = gameLocalServer.instance.opIndex;
    const restDiceNumArray = gameLocalServer.instance.getResetDiceNumList(Number(clickNum));
    const useCoinInfo = gameLocalServer.instance.getUserCoinInfoList(eatOff);
    const isUpdate = eatOff.length > 0 ? false : true;
    if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
      // TODO 获取经验值，获取COMB, 获取第一名
      const userIndex1st =  gameLocalServer.instance.get1stUserIndex(opIndex, movePath)
      const exp = gameLocalServer.instance.getUserExpValue(opIndex, eatOff?.length)
      const combo = gameLocalServer.instance.getUserComboValue(opIndex, !!eatOff?.length)
      console.error("local====chessMove====", chessId, eatOff?.length, opIndex, JSON.stringify({ exp, combo, userIndex1st }))
      ViewManager.classicChessMove(chessId, movePath, eatOff, finalChess, obsList, { exp, combo, userIndex1st }, opIndex, restDiceNumArray, isUpdate);
    } else {
      ViewManager.chessMove(chessId, movePath, eatOff, finalChess, obsList, useCoinInfo, opIndex, restDiceNumArray, isUpdate);
    }
  }

  /**
   * 显示对话框
   * @param dialogName 
   * @param isVisiable 
   */
  public toggleDialogVisiable(dialogName?: DialogType, isVisiable?: boolean) {
    this.chessDialogNode?.children?.forEach((node: Node) => {
      commonUtil.setActive(node, node.name === dialogName && isVisiable)
    })
  }

  /**
   * @name 设置对话框位置
   * @param chessId 棋子Id
   * @param dialogName 显示对话框名称
   * @param configs 多余参数配置
   */
  private setDialogPosition = (diceNumLen: number) => {
    const { x } = commonUtil.convertToWorldSpaceAR(this.node.parent, this.node.position);
    const isInLeft = x <= 130
    const isInRight = x >= 620
    if (isInLeft || isInRight) {
      this.dialogPointIconNode.position = new Vec3(
        ((-isInLeft) || (+isInRight)) * (diceNumLen - 1) * 50,
        this.dialogPointIconNode.position.y,
        0
      )
      this.chessDialogNode.position = new Vec3(
        ((+isInLeft) || (-isInRight)) * (diceNumLen - 1) * 50 / largeScaleRate,
        this.chessDialogNode.position.y,
        0
      )
      return
    }
    this.dialogPointIconNode.position = new Vec3(0, 50, 0)
    this.chessDialogNode.position = new Vec3(0, 0, 0)
  }

  /**
   * 显示骰子列表
   * @param chessId 棋子下表
   * @param diceNums 骰子点数集合
   */
  public showSelectDiceContent = (chessId: string, diceNums?: number[]) => {
    if (!diceNums?.length) {
      return this.toggleDialogVisiable()
    }
    const chessType = chessId.split('-')[0]
    const diceListNode = this.selectDiceNode.getChildByName('DiceList')
    const bgNodes = diceListNode.children.map((node: Node) => node.getChildByName('Inner').getChildByName('Bg'))
    const setColors = (iconColor: Color, bgColor: Color) => {
      this.selectDecorationalNode.children.forEach((node: Node, index: number) => {
        if (index === 0) return
        commonUtil.setColor(node, iconColor);
      })
      bgNodes.forEach((node: Node) => { commonUtil.setColor(node, bgColor) })
      if (this.blockAtlas) {
        diceListNode.getComponent(Sprite).spriteFrame = this.blockAtlas.getSpriteFrame(convertChessKey(chessType))
      }
    }
    switch (convertChessKey(chessType)) {
      case 'red': {
        setColors(color(255, 135, 121), color(212, 85, 72))
        break
      }
      case 'blue': {
        setColors(color(92, 192, 249), color(48, 140, 214))
        break
      }
      case 'green': {
        setColors(color(79, 218, 126), color(34, 166, 78))
        break
      }
      default: {
        setColors(color(245, 209, 81), color(182, 135, 15))
        break
      }
    }

    diceListNode.children.forEach((node: Node, index: number) => {
      commonUtil.setActive(node, !!diceNums?.[index])
      node.name = chessId
      node.getChildByName('Label').getComponent(Label).string = `${diceNums?.[index] || 0}`
    })
    this.setDialogPosition(diceNums?.length || 0)
    setTimeout(() => { this.toggleDialogVisiable(DialogType.SelectDice, !!diceNums?.length) })
  }

  /**
   * 显示表情对话框内容
   * @param type 表情类型
   */
  public showFacialContent = (type: FacialType) => {
    this.toggleDialogVisiable()
    // 表情节点适配 后续改节点层级实现功能
    // this.facialNode.setScale(Vec3.ONE)
    // this.facialNode.children.forEach((node) => (commonUtil.setActive(node, false)))
    // switch (type) {
    //   case FacialType.Happy: {
    //     commonUtil.setActive(this.facialNode.getChildByName(FacialType.Happy), true)
    //     break
    //   }
    //   case FacialType.Sad: {
    //     commonUtil.setActive(this.facialNode.getChildByName(FacialType.Sad), true)
    //     break
    //   }
    // }

    const cbFunc = (cb: Function) => {
      this.toggleDialogVisiable(DialogType.Facials, true);
      setTimeout(() => {
        this.toggleDialogVisiable();
        if (type == FacialType.Sad) {
          commonUtil.setIndex(this.node, ELayerIndex.Home);
        }
        cb && cb();
      }, 2300)
    }
    const worldPos = this.node.getWorldPosition();
    ViewManager.faceAimScript && ViewManager.faceAimScript.playAnim(type, cbFunc, worldPos);

    // this.scheduleOnce(() => {
    //   if ((this.node.scale.x == 1 && this.node.scale.y == 1) && (this.facialNode.scale.x == 1 && this.facialNode.scale.y == 1)) {
    //     this.facialNode.setScale(commonUtil.getEqualVec3(largeScaleRate))
    //   }
    //   this.toggleDialogVisiable(DialogType.Facials, true)
    //   // 关闭表情框
    //   setTimeout(() => {
    //     this.toggleDialogVisiable();
    //     if (type == FacialType.Sad) {
    //       commonUtil.setIndex(this.node, ELayerIndex.Home);
    //     }
    //   }, 2300)
    // }, .1)
  }
}
