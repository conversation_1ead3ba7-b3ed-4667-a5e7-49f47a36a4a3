
/**
 * @content 订阅发布
 * <AUTHOR>
 */

import { _decorator } from "cc";
import { cs } from "./log";

const { ccclass, property } = _decorator;

@ccclass
export class EventMgr {
    private static _handlers: { [key: string]: any[] } = {};
    /**
     * 事件监听
     * @param {string} eventName 事件名
     * @param {any} target  目标
     * @param {Function} handler 监听函数
     * @returns 
     */
    public static on(eventName: string, target: any, handler: Function) {
        let objHandler: {} = { handler: handler, target: target };
        let handlerList: Array<any> = EventMgr._handlers[eventName];
        if (!handlerList) {
            handlerList = [];
            EventMgr._handlers[eventName] = handlerList;
        }
        let len = handlerList.length;
        for (let i = 0; i < len; i++) {
            if (!handlerList[i]) {
                handlerList[i] = objHandler;
                return i;
            }
        }
        handlerList.push(objHandler);
        return handlerList.length;
    }

    /**
     * 取消监听
     * @param {string} eventName 事件名
     * @param {any} target  目标
     * @param {Function} handler 监听函数
     * @returns 
     */
    public static off(eventName: string, target: any, handler: Function) {
        let handlerList = this._handlers[eventName];
        if (!handlerList) return;
        let len = handlerList.length;
        for (let i = 0; i < len; i++) {
            let objHandler = handlerList[i]
            if (objHandler.handler === handler && (target && target === objHandler.target)) {
                handlerList.splice(i, 1);
                break;
            }
        }
    };

    /**
     * 分发事件
     * @param {string} eventName  分发事件名
     * @param {any} args  分发事件参数 
     * @returns 
     */
    public static dispatchEvent(eventName: string, ...args: any) {
        let handlerList = this._handlers[eventName];
        if (!handlerList) {
            cs.warn("pleace eventName is right ?", eventName);
            return;
        }
        let len = handlerList.length;
        for (let i = 0; i < len; i++) {
            let curHandler = handlerList[i];
            if (curHandler) {
                curHandler.handler.apply(curHandler.target, args);
            }
        }
    }

    /**
     * 销毁全部事件监听
     */
    public static dispatchEventAll() {
        this._handlers = {};
        cs.log("dispatchEventAll compile!");
    }

}

