{"ver": "1.0.25", "importer": "image", "imported": true, "uuid": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c", "files": [".json", ".png"], "subMetas": {"reset": {"ver": "1.0.6", "uuid": "718084be-5404-42ea-bda7-57462cd9221e", "importer": "sprite-frame", "rawTextureUuid": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 11, "trimY": 11, "width": 66, "height": 66, "rawWidth": 88, "rawHeight": 88, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {"mipfilter": "none"}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c@6c48a", "displayName": "reset", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c", "visible": false}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c@f9941", "displayName": "reset", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 11, "trimY": 11, "width": 66, "height": 66, "rawWidth": 88, "rawHeight": 88, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-33, -33, 0, 33, -33, 0, -33, 33, 0, 33, 33, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [11, 77, 77, 77, 11, 11, 77, 11], "nuv": [0.125, 0.125, 0.875, 0.125, 0.125, 0.875, 0.875, 0.875], "minPos": [-33, -33, 0], "maxPos": [33, 33, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c@6c48a", "atlasUuid": ""}, "ver": "1.0.11", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": true, "hasAlpha": true, "redirect": "c4ad44c9-9fd9-4538-97e8-44a9b5be6d5c@f9941"}}