import gameConfig from "../gameConfig"
import globalData from "../globalData"

export default class wsClient {
  private _socket: WebSocket | null = null
  private _connected: boolean = false
  private _timer = null

  public onopen: (() => void) | null = null
  public onmessage: ((message: MessageEvent) => void) | null = null
  public onclose: ((e) => void) | null = null
  public onerror: ((e) => void) | null = null

  constructor() {
    this.init()
  }

  private async init() {
    // 检查是否禁用网络连接
    if (gameConfig.DISABLE_NETWORK) {
      console.log('🚫 网络连接已禁用 - 本地游戏模式');
      this._connected = false;
      return;
    }

    console.log('build new wsClient')
    this._socket = new WebSocket(`${gameConfig.WEBSOCKET[gameConfig.ENV]}/ws/game`, [globalData.token])
    this.onmessageFunc();
    this.onopenFunc();
    this.onerrorFunc();
    this.oncloseFunc();
    this.clearReconnectTimer()
  }

  private onmessageFunc() {
    if (!this._socket) return
    this._socket.onmessage = (event) => {
      this._connected = true
      this.onmessage && this.onmessage(event)
    }
  }

  private onopenFunc() {
    if (!this._socket) return
    this._socket.onopen = () => {
      this._connected = true
      this.onopen && this.onopen()
    }
  }

  private onerrorFunc() {
    if (!this._socket) return
    this._socket.onerror = (e) => {
      console.log("onerror", JSON.stringify(e))
      this._socket = null
      this._connected = false
      this.onerror && this.onerror(e)
      // this.reconnect()
    }
  }

  private oncloseFunc() {
    if (!this._socket) return
    this._socket.onclose = (e) => {
      console.log("onclose", JSON.stringify(e), e.code)
      this._connected = false
      this._socket = null
      // if (e.code !== 1000) {
      // this.reconnect()
      // } else {
      this.onclose && this.onclose(e)
      // }
    }
  }

  public reconnect() {
    // this.init.call(this);
    if (this._timer) return
    this._timer = setTimeout(() => this.init.call(this), 3000)
  }

  private clearReconnectTimer() {
    if (this._timer) {
      clearTimeout(this._timer)
    }
    this._timer = null
  }

  public postMessage(message: any) {
    // 本地模式下不发送网络消息
    if (gameConfig.DISABLE_NETWORK) {
      console.log('🚫 本地模式 - 跳过网络消息发送:', message);
      return false;
    }

    if (!this._connected) return false;
    if (typeof message !== 'string' && !(message instanceof ArrayBuffer) && !ArrayBuffer.isView(message)) {
      message = JSON.stringify(message)
    }
    this?._socket?.send(message)
    return true
  }

  public close() {
    if (this._connected) this._socket?.close()
    this.clearReconnectTimer()
  }

  public get connected(): Boolean {
    return this._connected
  }
}
