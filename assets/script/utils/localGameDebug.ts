import globalData from '../globalData';
import { LocalGameMgr } from '../manager/localGameMgr';
import { director } from 'cc';

/**
 * 本地游戏调试工具
 */
export class LocalGameDebug {
    
    /**
     * 打印当前游戏状态
     */
    public static printGameState(): void {
        console.log('=== 🎮 本地游戏状态 ===');
        console.log('当前操作用户:', globalData.currentOpUserId);
        console.log('我的用户ID:', globalData.myUserId);
        console.log('是我的回合:', globalData.isMyRound);
        console.log('可以摇骰子:', globalData.canTouchDice);
        console.log('骰子提示:', globalData.noticeDice);
        console.log('玩家操作状态:', globalData.playerOpStatus);
        console.log('当前玩家索引:', globalData.currentPlayerIndex);
        console.log('游戏状态:', globalData.gameInfo.gameStatus);
        console.log('玩家信息:', globalData.gameInfo.playerInfo);
        console.log('可移动棋子:', globalData.canMoveChessMap);
        console.log('========================');
    }
    
    /**
     * 强制设置玩家可以摇骰子
     */
    public static forceEnableDice(): void {
        console.log('🔧 强制启用骰子');
        globalData.canTouchDice = true;
        globalData.noticeDice = true;
        globalData.myUserId = globalData.currentOpUserId;
        this.printGameState();
    }
    
    /**
     * 手动触发骰子点击
     */
    public static triggerDiceClick(): void {
        console.log('🎲 手动触发骰子点击');
        director.emit('dice-clicked');
    }
    
    /**
     * 手动触发棋子移动完成
     */
    public static triggerChessMoveFinished(): void {
        console.log('♟️ 手动触发棋子移动完成');
        director.emit('chess-move-finished');
    }
    
    /**
     * 重启本地游戏
     */
    public static restartLocalGame(): void {
        console.log('🔄 重启本地游戏');
        const localGameMgr = LocalGameMgr.instance;
        if (localGameMgr) {
            localGameMgr.restartLocalGame();
        } else {
            console.error('❌ 本地游戏管理器未找到');
        }
    }
    
    /**
     * 跳过当前回合
     */
    public static skipCurrentTurn(): void {
        console.log('⏭️ 跳过当前回合');
        const localGameMgr = LocalGameMgr.instance;
        if (localGameMgr) {
            localGameMgr.nextPlayerTurn();
        } else {
            console.error('❌ 本地游戏管理器未找到');
        }
    }
    
    /**
     * 模拟摇骰子
     */
    public static simulateRollDice(diceValue: number = 0): void {
        const value = diceValue || Math.floor(Math.random() * 6) + 1;
        console.log(`🎲 模拟摇骰子: ${value}`);
        
        // 设置骰子结果
        globalData.diceResultInfo = value;
        
        // 触发骰子结果显示
        import('../manager/view').then(({ default: ViewManager }) => {
            ViewManager.notifyDiceResult(
                globalData.currentOpUserId,
                globalData.currentPlayerIndex,
                value,
                [value]
            );
        });
    }
    
    /**
     * 获取可移动棋子
     */
    public static getAvailableChesses(playerIndex: number, diceValue: number): string[] {
        const localGameMgr = LocalGameMgr.instance;
        if (localGameMgr) {
            return localGameMgr.getAvailableChessesForPlayer(playerIndex, diceValue);
        }
        return [];
    }
    
    /**
     * 监控游戏状态变化
     */
    public static startMonitoring(): void {
        console.log('📊 开始监控游戏状态');
        
        const monitor = () => {
            console.log('📊 状态监控:', {
                time: new Date().toLocaleTimeString(),
                currentPlayer: globalData.currentOpUserId,
                isMyRound: globalData.isMyRound,
                canTouchDice: globalData.canTouchDice,
                gameStatus: globalData.gameInfo.gameStatus
            });
        };
        
        // 每5秒监控一次
        setInterval(monitor, 5000);
        
        // 立即执行一次
        monitor();
    }
    
    /**
     * 测试完整游戏流程
     */
    public static testGameFlow(): void {
        console.log('🧪 测试完整游戏流程');
        
        // 1. 打印当前状态
        this.printGameState();
        
        // 2. 强制启用骰子
        this.forceEnableDice();
        
        // 3. 等待2秒后触发骰子点击
        setTimeout(() => {
            this.triggerDiceClick();
        }, 2000);
        
        // 4. 等待5秒后检查状态
        setTimeout(() => {
            console.log('🔍 5秒后状态检查:');
            this.printGameState();
        }, 5000);
    }
}

/**
 * 注册全局调试方法
 */
if (typeof window !== 'undefined') {
    // 游戏状态
    window.printGameState = () => LocalGameDebug.printGameState();
    window.forceEnableDice = () => LocalGameDebug.forceEnableDice();
    
    // 事件触发
    window.triggerDiceClick = () => LocalGameDebug.triggerDiceClick();
    window.triggerChessMoveFinished = () => LocalGameDebug.triggerChessMoveFinished();
    
    // 游戏控制
    window.restartLocalGame = () => LocalGameDebug.restartLocalGame();
    window.skipCurrentTurn = () => LocalGameDebug.skipCurrentTurn();
    window.simulateRollDice = (value?: number) => LocalGameDebug.simulateRollDice(value);
    
    // 调试工具
    window.startMonitoring = () => LocalGameDebug.startMonitoring();
    window.testGameFlow = () => LocalGameDebug.testGameFlow();
    window.getAvailableChesses = (playerIndex: number, diceValue: number) => 
        LocalGameDebug.getAvailableChesses(playerIndex, diceValue);
    
    console.log('🛠️ 本地游戏调试工具已加载');
    console.log('💡 可用命令:');
    console.log('  printGameState()      - 打印游戏状态');
    console.log('  forceEnableDice()     - 强制启用骰子');
    console.log('  triggerDiceClick()    - 触发骰子点击');
    console.log('  simulateRollDice(6)   - 模拟摇骰子');
    console.log('  testGameFlow()        - 测试完整流程');
    console.log('  startMonitoring()     - 开始状态监控');
}

declare global {
    interface Window {
        printGameState: () => void;
        forceEnableDice: () => void;
        triggerDiceClick: () => void;
        triggerChessMoveFinished: () => void;
        restartLocalGame: () => void;
        skipCurrentTurn: () => void;
        simulateRollDice: (value?: number) => void;
        startMonitoring: () => void;
        testGameFlow: () => void;
        getAvailableChesses: (playerIndex: number, diceValue: number) => string[];
    }
}
