import { EventType } from "../type/events";
import { loadRemoteImg, loadRemoteTexture2D } from "../utils/utils";
import ChessManager from "./chess";
import ViewManager from "./view";
import globalData from "../globalData";
import { Component, director, Node, Sprite, _decorator } from "cc";
import { commonUtil } from "../utils/commonUtil";
import { Utils } from "../framework/frameworkUtils";


const { ccclass, property } = _decorator;

@ccclass("SkinManager")
export default class SkinManager extends Component {

    @property(Sprite)
    gameBgSp: Sprite = null

    @property(Sprite)
    chessboardBgSp: Sprite = null

    @property(Sprite)
    chessboardBorderSp: Sprite = null

    _totalImg = 0;
    _curImg = 0;

    protected onLoad(): void {
        director.on(EventType.CHANGE_SKIN, this.changeCustomSkins, this)
    }

    protected onDestroy(): void {
        director.on(EventType.CHANGE_SKIN, this.changeCustom<PERSON>kins, this)
    }

    private changeNodeSkin(node: Node, url: string) {
        return new Promise(async (resolve) => {
            const spriteframe = await loadRemoteImg(url)
            if (spriteframe) {
                node.getComponent(Sprite).spriteFrame = spriteframe
                commonUtil.setWidth(node, commonUtil.getWidth(node))
                commonUtil.setHeight(node, commonUtil.getHeight(node))
            }
            this._curImg++;
            this.checkComplete();
            resolve('')
        })
    }

    private async completeGameLoad() {
        commonUtil.setActive(ViewManager.gameInstance.loadingLayer, false)
        director.emit("gameLoaded");
        console.log("游戏加载完成，" + new Date().getTime())
    }

    private checkComplete() {
        if (this._curImg >= this._totalImg) {
            this._curImg = 0;
            this._totalImg = 999;
            this.completeGameLoad();
        }
    }

    private async changeCustomSkins() {
        this._totalImg = 3;
        this._curImg = 0;
        if (!globalData.gameSkins?.globalConfig || (!globalData.gameSkins && globalData.gameSkins.globalConfig && globalData.gameSkins.globalConfig.length <= 0)) return this.completeGameLoad()
        console.log("开始加载棋盘皮肤，" + new Date().getTime());
        const {
            globalConfig: { chessBoardBorderSkin, chessBoardSkin, gameBgSkin },
            userConfig,
        } = globalData.gameSkins
        // 兜底加载完成
        Utils.timeOut1(3000).then(() => this.completeGameLoad())
        gameBgSkin && this.changeNodeSkin(this.gameBgSp.node, gameBgSkin)
        chessBoardSkin && this.changeNodeSkin(this.chessboardBgSp.node, chessBoardSkin)
        chessBoardBorderSkin && this.changeNodeSkin(this.chessboardBorderSp.node, chessBoardBorderSkin)
        console.log('棋盘皮肤加载完成' + new Date().getTime())
        if (!userConfig || Object.keys(userConfig).length <= 0) return this.completeGameLoad()

        console.log('开始加载棋子、骰子皮肤加载完成' + new Date().getTime())
        this._loadUserCustomSkin(userConfig)
        console.log('棋子、骰子皮肤加载完成' + new Date().getTime())

        return this.completeGameLoad()
    }

    /**
     * @zh 加载棋子皮肤 @en load chess skin
     */
    private _loadUserCustomSkin(userConfig) {
        return new Promise((resolve) => {
            const userIds = Object.keys(userConfig)
            userIds.forEach(async (userId: string, index: number) => {
                const userInfo = globalData.getUserInfoByUserId(userId)
                if (!userInfo?.userIndex) return

                const { chessSkin, diceSkin, diceAnimSkin, diceSpecialAnimSkin } = userConfig[userId]
                const chessTexture2D = chessSkin && await loadRemoteTexture2D(chessSkin, {})
                const diceTexture2D = diceSkin && await loadRemoteTexture2D(diceSkin, {})
                const diceAnimTexture2D = diceAnimSkin && await loadRemoteTexture2D(diceAnimSkin, {})
                const diceShineTexture2d = diceSpecialAnimSkin && await loadRemoteTexture2D(diceSpecialAnimSkin, {})

                const { userIndex } = userInfo
                for (let i = 0; i < 4; i++) {
                    chessTexture2D && ChessManager.chessMap?.get(`${userIndex}-${i + 1}`)?.getComponent(ChessManager)?.setChessSkin(chessTexture2D)
                }
                ViewManager.getPlayerMgr(userIndex)?.setDiceSkins(diceTexture2D, diceAnimTexture2D, diceShineTexture2d)
                if (userIds.length === index + 1) {
                    this.checkComplete();
                    return resolve(null)
                }
            })
        })
    }
}
