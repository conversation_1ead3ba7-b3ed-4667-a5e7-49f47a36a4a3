/**
 * 棋子控制器
 */
import globalData from '../globalData'
import { GAME_MODE, SOCKET_TYPE } from '../network/constants'
import { ChessInfo, ChessType } from '../type/index'
import { convertChessKey, convertPlayerPos, getTextureFrame } from '../utils/utils'
import ActionManager from './action'
import ChessDialogManager from './chessDialog'
import ViewManager from './view'
import { chessEndPositions, chessOriginalPositions, cornerCoordinateMap, finalChessIndexs, gameMaxFps, gridCoordinateMap, largeScaleRate, smallScaleRate } from '../gameConfig'
import { recordFinalResult } from '../utils/sensor'
import { Component, Node, Sprite, SpriteAtlas, Texture2D, v3, Vec2, Vec3, _decorator } from 'cc'
import { commonUtil } from '../utils/commonUtil'
import { ELayerIndex } from '../type/enum'
import { cs } from '../framework/log'
import { gameLogMgr, LogSendType } from './gameLogMgr'
import { AudioMgr } from './audioMgr'
import { gameLocalServer } from './gameLocalServer'
import GameConfig from '../gameConfig'
import { director } from 'cc'
import { GameModeMgr } from './gameModeMgr'

const { ccclass, property } = _decorator
const chessSkinXOffset = { red: 0, green: 64, yellow: 128, blue: 192 }

@ccclass("ChessManager")
export default class ChessManager extends Component {
  @property(Node)
  private chessIconNode: Node = null

  @property(SpriteAtlas)
  private chessAtlas: SpriteAtlas

  public static chessMap = new Map()

  public static chessPosIndexMap = new Map() // 记录棋子在哪个下标

  public static chessData = []

  public userInfo: any

  private static _isAudioFinished = false;
  private static _isPlayTips = {
    "A": false,
    "B": false
  };

  private static _endCallback = null;

  private static _scb;

  onload() {
    commonUtil.setColorFromHEX(this.chessIconNode, "#5CC0F9");
  }

  private get chessType() {
    return convertChessKey(this.node.name.split('-')?.[0])
  }

  public setChessSkin(chessTexture2D: Texture2D) {
    if (!chessTexture2D) return
    this.chessIconNode.getComponent(Sprite).spriteFrame = getTextureFrame(chessTexture2D, chessSkinXOffset[this.chessType], 64, 64)
  }

  public initChessView(chessInfo: Record<any, any>, userInfo: Record<any, any>) {
    const { chessId, chessIndex } = chessInfo
    this.node.name = chessId
    this.node.setPosition(ChessManager.getPosLocation(chessIndex, this.node))
    this.chessIconNode.getComponent(Sprite).spriteFrame = this.chessAtlas.getSpriteFrame(`${this.chessType}`)
    this.userInfo = userInfo
    ChessManager._isAudioFinished = false;
    ChessManager._isPlayTips = {
      "A": false,
      "B": false
    };
  }

  public onClickChessEvent(event) {
    event.cancelable = false
    globalData.cancelHosting(globalData.myUserId)
    cs.log("globalData.isMyRound ", !globalData.isMyRound, !globalData.canMoveChessMap)
    if (!globalData.isMyRound || !globalData.canMoveChessMap) return
    ViewManager.closeDiceDialog()
    let chessId: string = event.target.name;
    let canMoveChesses = Array.from(globalData.canMoveChessMap.keys());
    if (canMoveChesses.indexOf(chessId) >= 0) {
      let canMoveValueList = globalData.canMoveChessMap.get(chessId)
      if (canMoveValueList.length > 1 && ChessManager.chessPosIndexMap.get(chessId)) {
        commonUtil.setIndex(this.node, ELayerIndex.Click)
        return this.node.getComponent(ChessDialogManager).showSelectDiceContent(chessId, canMoveValueList)
      }
      let logInfo = gameLogMgr.instance.getCurIdAndTime(LogSendType.ChessMove);
      // 发送请求
      let subCommand = {
        commandKey: 'chessMoveSelect',
        chessId,
        diceNum: canMoveValueList.includes(6) ? 6 : canMoveValueList[0],
        ...logInfo
      }
      // 关掉棋子波纹
      ViewManager.closeDiceWave();
      gameLogMgr.instance.setSelectTime();

      // 本地模式下不发送网络消息，直接处理本地逻辑
      if (!GameConfig.FORCE_LOCAL_MODE) {
        globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand })
      }

      if (!gameLocalServer.instance.isCanGameMode()) return
      console.error("local====chessMove====", GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode, globalData.gameInfo.endMode, globalData.gameInfo.gameMode)
      const step = canMoveValueList.includes(6) ? 6 : canMoveValueList[0];
      const movePath = gameLocalServer.instance.getMoveInfoList(chessId, step);
      const eatOff = gameLocalServer.instance.getEatOffList(movePath, chessId);
      const finalChess = gameLocalServer.instance.getFinalChessesList(movePath, chessId, eatOff);
      const obsList = gameLocalServer.instance.getObsList(eatOff);
      const opIndex = gameLocalServer.instance.opIndex;
      const restDiceNumArray = gameLocalServer.instance.getResetDiceNumList(step);
      const useCoinInfo = gameLocalServer.instance.getUserCoinInfoList(eatOff);
      const isUpdate = eatOff.length > 0 ? false : true;
      
      if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
        const userIndex1st =  gameLocalServer.instance.get1stUserIndex(opIndex, movePath)
        const exp = gameLocalServer.instance.getUserExpValue(opIndex, eatOff?.length || 0)
        const combo = gameLocalServer.instance.getUserComboValue(opIndex, !!eatOff?.length)
        // TODO 获取经验值，获取COMB, 获取第一名
        console.error("local====chessMove====", chessId, eatOff?.length, opIndex, JSON.stringify({ exp, combo, userIndex1st }))
        ViewManager.classicChessMove(chessId, movePath, eatOff, finalChess, obsList, { exp, combo, userIndex1st }, opIndex, restDiceNumArray, isUpdate);
      } else {
        ViewManager.chessMove(chessId, movePath, eatOff, finalChess, obsList, useCoinInfo, opIndex, restDiceNumArray, isUpdate);
      }
    }
  }

  /**
   * @name 获取终点/起点位置
   * @param node 棋子节点，主要用来获取name 1-1
   * @returns
   */
  public static getOriginOrEndLocation(node: Node, isFinal?: boolean) {
    let chessType: any = node?.name?.split('-')
    const chessIndex = Number(chessType?.[1] || 0)
    chessType = Number(chessType?.[0] || 3)
    const sortIndex = ViewManager.getCurrentPlayerPosIndex(chessType)
    return (isFinal ? chessEndPositions : chessOriginalPositions)[convertPlayerPos(sortIndex)][chessIndex]
  }

  /**
   * @name 根据棋盘格子序号获取该格子的position位置
   * @param boxIndex 格子序号
   * @param node 棋子节点
   * @returns 
   */
  public static getPosLocation(boxIndex: number, node: Node): Vec3 {
    if (boxIndex === 0) {
      return ChessManager.getOriginOrEndLocation(node)
    } else if (finalChessIndexs.includes(boxIndex)) {
      let pos = ChessManager.getOriginOrEndLocation(node, true)
      return v3(pos.x, pos.y, 0)
    } else {
      const chssCoor = gridCoordinateMap.get(boxIndex)
      return this.getIndexPosition(chssCoor)
    }
  }

  public static getIndexPosition(chssCoor: Vec3 | Vec2): Vec3 {
    const gPos = commonUtil.convertToWorldSpaceAR(ViewManager.gameInstance.gridContainer, v3(chssCoor.x, chssCoor.y))
    return commonUtil.convertToNodeSpaceAR(ViewManager.gameInstance.chessGroupNode, gPos)
  }

  /**
   * @name 棋子位置矫正
   * @description fix all chess about it's position
   */
  public static fixChessPosition(chessInfo: ChessInfo) {
    const { chessId, chessIndex } = chessInfo
    const chessNode = this.chessMap.get(chessId) || {}
    this.chessPosIndexMap.set(chessId, chessIndex)
    if (!chessNode?.isValid) return
    chessNode.setScale(
      commonUtil.getEqualVec3(
        finalChessIndexs.includes(chessIndex)
          ? smallScaleRate
          : chessIndex === 0
            ? largeScaleRate
            : 1
      )
    )
    if (!this.getPosLocation(chessIndex, chessNode)) return
    chessNode.setPosition(this.getPosLocation(chessIndex, chessNode))
  }

  /**
   * 移动完成后修正棋子位置信息。
   * @param chessInfos 
   */
  public static moveFinishedFixChessPosition(chessInfos: Array<ChessInfo>) {
    chessInfos
      .filter(({ chessIndex, chessId }: ChessInfo) => {
        this.fixChessPosition1({ chessId, chessIndex })
        return ![0, ...finalChessIndexs].includes(chessIndex)
      })
  }

  public static fixChessPosition1(chessInfo: ChessInfo) {
    const { chessId, chessIndex } = chessInfo
    const chessNode = this.chessMap.get(chessId) || {}
    this.chessPosIndexMap.set(chessId, chessIndex)
    // if (!chessNode?.isValid) return
    // chessNode.setScale(
    //   commonUtil.getEqualVec3(
    //     finalChessIndexs.includes(chessIndex)
    //       ? smallScaleRate
    //       : chessIndex === 0
    //         ? largeScaleRate
    //         : 1
    //   )
    // )
    // if (!this.getPosLocation(chessIndex, chessNode)) return
    // chessNode.setPosition(this.getPosLocation(chessIndex, chessNode))
  }

  /**
   * @name 棋子缩放
   * @param chessInfos 棋子信息 格式 [['1-2', 45], ['4-2', 0, '4-1', 0, '1-2', 45]]
   */
  public static scaleMulChess(chessInfos: Array<ChessInfo>) {
    if (!Array.isArray(chessInfos)) return
    const chessPosInfo = {}
    // 根据下标排序
    chessInfos
      .filter(({ chessIndex, chessId }: ChessInfo) => {
        this.fixChessPosition({ chessId, chessIndex })
        return ![0, ...finalChessIndexs].includes(chessIndex)
      })
      .forEach(({ chessId, chessIndex }: ChessInfo) => {
        if (!chessPosInfo[chessIndex]) chessPosInfo[chessIndex] = []
        chessPosInfo[chessIndex].push(chessId)
      })
    // 位置排序
    Object.keys(chessPosInfo).forEach((chessIndex: string) => {
      // 节点分类
      const classifiedNodes = {} // 为不同颜色节点的棋子集合
      chessPosInfo[chessIndex]
        .forEach((chessId: string) => {
          const key = convertChessKey(chessId.split('-')[0])
          if (!classifiedNodes[key]) classifiedNodes[key] = []
          classifiedNodes[key].push(chessId)
        })

      Object.entries(classifiedNodes).forEach((infos: any, colIndex: number, self) => {
        const boxSize = 48
        const rowLen = infos[1].length // 行的长度
        const colLen = self.length  // 列的长度
        const scaleRate = (colLen > 1 || rowLen > 1) ? 2 / 3 : 1
        const xDistance = boxSize * scaleRate / (rowLen > 2 ? rowLen + 1 : rowLen)
        const yDistance = boxSize * scaleRate / (colLen > 2 ? colLen + 1 : colLen)
        infos[1].forEach((chessId: number, index: number) => {
          const chessNode: Node = ChessManager.chessMap.get(chessId)
          if (!chessNode?.isValid) return
          const { x, y } = ChessManager.getPosLocation(+chessIndex, chessNode)
          chessNode.setScale(commonUtil.getEqualVec3(scaleRate))
          chessNode.setPosition(v3(x + xDistance * (infos[1].length - index - 1) - (rowLen > 1 ? 8 : 0),
            y + yDistance * colIndex - (colLen > 1 ? 8 : 0), 0))
          commonUtil.setIndex(chessNode, +index)
        })
      })
    })
  }

  /**
   * 棋子位移
   * @param node 
   * @param movePath 
   * @returns 
   */
  public static async chessPosMove(
    node: Node,
    movePath: number[],
    isEatChess: boolean,
    teamName, reachDestCost, teammateStatus
  ) {
    return new Promise(async (resolve) => {
      node.setScale(Vec3.ONE)
      commonUtil.setIndex(node, ELayerIndex.Moving)
      for (let i = 0; i < movePath.length; i++) {
        clearTimeout(ChessManager._scb);
        let targetIndex = movePath[i];
        let targetPos: Vec3 = ChessManager.getPosLocation(targetIndex, node);
        let isLastMove: boolean = i === (movePath.length - 1);
        const targetCornerPos = cornerCoordinateMap.get(targetIndex)
        targetCornerPos && await ActionManager.chessMove(node, this.getIndexPosition(targetCornerPos), false, true)
        if (isEatChess && isLastMove) {
          await ActionManager.eatChessMove(node, ChessManager.getPosLocation(movePath[movePath.length - 1], node))
        } else {
          await ActionManager.chessMove(node, targetPos, isLastMove, false)
        }
        if (finalChessIndexs.indexOf(targetIndex) >= 0) {

          if (reachDestCost && Number(reachDestCost) > 0) {
            recordFinalResult({
              result_type: 'LudoFirstChessEnd',
              element_business_content: reachDestCost,
            })
          }

          const { x, y } = this.getOriginOrEndLocation(node, true)
          // 进入终点
          node.setScale(commonUtil.getEqualVec3(smallScaleRate))
          // director.emit('PLAY_EFFECT', EFFECT_NAME.arriveInEffect);
          AudioMgr.instance.play('audio/sound_chessEnter');
          ViewManager.toggleVisiableResultParticle(true, v3(x, y, 0))
          ChessManager._isAudioFinished = false;
          // ChessManager._audioSwitch = true;
          // ChessManager._isEnd = true;
          console.info("ChessManager 进入终点" + new Date().getTime())

          if (globalData?.gameInfo?.gameMode == GAME_MODE.TWO_VS_TWO) {
            console.log("teammateStatus:" + teammateStatus);
            // 队友破产或者逃跑不显示一人胜利tips  3：逃跑，4：破产
            if (teammateStatus && Number(teammateStatus) != 3 && Number(teammateStatus) != 4) {
              // 只有每个队伍的第一个玩家到终点后才会显示一人胜利tips
              if (!ChessManager._isPlayTips[teamName]) {
                await ViewManager.gameInstance.showCompleteTips();
                ChessManager._isPlayTips[teamName] = true;
              }
            }
          }
          clearTimeout(ChessManager._scb);
          setTimeout(() => {
            ChessManager._isAudioFinished = true
            if (ChessManager._endCallback == null) {
              return;
            }

            ChessManager._endCallback();
            ChessManager._endCallback = null
            ChessManager._isAudioFinished = false;
            console.info("ChessManager.setEndCallBack setTimeout" + new Date().getTime())
          }, 3000);
        }
      }
      commonUtil.setIndex(node, ELayerIndex.Normal);
      resolve('')
    })
  }

  public static setEndCallBack(cb) {
    if (ChessManager._isAudioFinished) {
      return;
    }
    ChessManager._endCallback = cb;
    let delayTime = 5000
    if (GameModeMgr.instance.isClassicMode || GameModeMgr.instance.isClassicQuickMode) {
      delayTime = 2000
    }
    ChessManager._scb = setTimeout(() => {
      ChessManager._endCallback && ChessManager._endCallback();
      ChessManager._endCallback = null
    }, delayTime);
    console.info("ChessManager.setEndCallBack setEndCallBack" + new Date().getTime())
  }

  public static isAudioFinished() {
    console.info("ChessManager.setEndCallBack isAudioFinished" + new Date().getTime())

    return ChessManager._isAudioFinished;
  }
}