import { director } from "cc"
import globalData from "../globalData"
import { EventType } from "../type/events"

/**
 * @zh 测试游戏皮肤 @en test game skin
 */
export const testGameSkin = () => {
  globalData.gameSkins = {
    globalConfig: {
      gameBgSkin: 'https://cdnimg.cqpiwan.com/sociality/2022/08/31/2960743981980127292.jpg',
      chessBoardSkin: 'https://cdnimg.cqpiwan.com/sociality/2022/08/31/2960743945472904764.png',
      chessBoardBorderSkin: 'https://cdnimg.cqpiwan.com/sociality/2022/08/31/2960743964802352700.png'
    },
    userConfig: {
      '111': {
        chessSkin: "https://cdnimg.cqpiwan.com/sociality/2022/08/31/2960744256860131388.png",
        diceAnimSkin: "https://cdnimg.cqpiwan.com/sociality/2022/08/31/2960756551201927228.png",
        diceSkin: "https://cdnimg.cqpiwan.com/sociality/2022/08/31/2960756527579606588.png",
        diceSpecialAnimSkin: 'https://cdnoffice.lizhi.fm/sociality/2022/07/28/2954467179485357628.png'
      },
    }
  }
  setTimeout(() => {
    console.error('加载')
    director.emit(EventType.CHANGE_SKIN)
}, 10000)
}
