import { _decorator, Component, Node, Vec3, v3 } from 'cc';
import { commonUtil } from '../utils/commonUtil';
const { ccclass, property } = _decorator;

@ccclass('nodeAdpter')
export class nodeAdpter extends Component {

    @property
    standerPos: Vec3 = v3(0, 0, 0);

    @property
    longPos: Vec3 = v3(0, 0, 0);

    private _isStandardScreen = true;

    onLoad() {
        this._isStandardScreen = commonUtil.getVisibleHeight() / commonUtil.getVisibleWidth() <= 1400 / 750;
    }

    onEnable() {
        this.node.setPosition(this._isStandardScreen ? this.standerPos : this.longPos);
    }
}

