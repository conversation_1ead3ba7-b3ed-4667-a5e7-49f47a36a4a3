import { director, instantiate, <PERSON><PERSON>As<PERSON>, Node, ParticleSystem2D, Tween, Vec3, _decorator } from 'cc';
import Ludo from '../scene/ludo';
import ChessManager from './chess'
import { allChesses, largeScaleRate } from '../gameConfig'
import { ChessType, FacialType, PlayerStatus, ChessInfo } from '../type/index';
import globalData from '../globalData';
import { EventType } from '../type/events';
import ActionManager from './action';
import ChessDialogManager from './chessDialog';
import { sensorResult } from '../utils/sensor';
import ProcessCoverManager from './processCover';
import TopArea from '../prefabs/topArea';
import TopArea_Classic from '../prefabs/classicMode/topArea_classic';
import { Utils } from '../framework/frameworkUtils';
import { END_MODE, GAME_MODE, SOCKET_TYPE } from '../network/constants';
import { I18n } from '../framework/i18n';
import { commonUtil } from '../utils/commonUtil';
import { playerBackup } from '../prefabs/playerBackup';
import { getAppInfo, getGameExtraConfig, getUserInfo } from '../seal/bridge';
import ObstacleManager from '../prefabs/obstacleBox';
import CoinListManager from '../prefabs/coinList';
import { loadLoaclResource } from '../utils/utils';
import { IGetSystemInfoRes } from '../seal/types';
import { ELayerIndex } from '../type/enum';
import { AudioMgr } from './audioMgr';
import { EventMgr } from '../framework/eventMgr';
import GameConfig from '../gameConfig';
import { EventName } from '../framework/eventName';
import { gameLocalServer } from './gameLocalServer';
import { faceAnimNode } from '../prefabs/faceAnimNode';

const { ccclass } = _decorator;

@ccclass("ViewManager")
export default class ViewManager {
  // 游戏上下文
  public static gameInstance: Ludo

  // 基础组件管理器
  public static get baseComponentManager() {
    return this.gameInstance.baseComponentManager
  }

  // 头像位置下标集合  //1红 2绿 3黄 4蓝
  public static playerSortPosIndexs = [1, 2, 3, 4]

  // 玩家集合节点
  public static playerNodeMap = new Map()


  public static topAreaScript: TopArea = null;

  // topArea的script 
  public static topArea_classicScript: TopArea_Classic = null;

  // coinList  Script
  public static coinListScript: CoinListManager = null;

  public static gameOverDialogNode: Node

  public static faceAimScript: faceAnimNode = null;

  public static getPlayerMgr(userIndex: ChessType | number): playerBackup | null {
    try {
      const playerMgr = this.playerNodeMap?.get(userIndex)?.getComponent(playerBackup)
      if (playerMgr?.node?.isValid) return playerMgr
    } catch (e) { console.log(e) }
    return null
  }

  public static getCurrentPlayerPosIndex(userIndex) {
    return this.playerSortPosIndexs.findIndex(i => i === userIndex) + 1
  }

  /**
   * @name 初始化棋盘视图
   * @param boxAngle 盒子旋转角度
   * @param boardAngle 棋盘旋转角度
   */
  private static async initChessboardView(boardAngle: number) {
    // fix: 无效节点多次创建
    this.gameInstance.chessGroupNode.children.forEach((node: Node) => {
      node.destroy()
    })
    this.gameInstance.userGroupNode.children.forEach((node: Node) => {
      node.children.forEach((n: Node) => {
        n.destroy()
      })
    })
    ChessManager.chessMap = new Map()
    this.gameInstance.chessBoardNode.angle = -boardAngle // 整体棋盘渲染到操作用户角度
  }


  /**
   * @name 初始化玩家视图
   * @param playerList 玩家列表
   * @param chessInfos 棋子位置信息
   * @returns 
   */
  private static async initAllPlayerView(playerList: any[], chessInfos: ChessInfo[]) {
    if (!Array.isArray(playerList)) return
    console.log("初始化玩家视图，" + new Date().getTime())
    let userInfoMap = new Map()
    this.playerNodeMap = new Map()
    playerList.forEach((userInfo: any) => {
      userInfoMap.set(`${userInfo.userIndex}`, userInfo)
      const node = instantiate(this.gameInstance.playerPrefab)
      this.playerNodeMap.set(userInfo.userIndex, node)
      node.getComponent(playerBackup)?.initPlayerView(userInfo)
      node.parent = this.gameInstance.userGroupNode
    })

    console.log('init Chess', JSON.stringify(chessInfos))
    chessInfos.forEach((data: any, index: number) => {
      const chessNode = instantiate(this.gameInstance.chessPrefab)
      chessNode.parent = this.gameInstance.chessGroupNode
      chessNode?.getComponent(ChessManager)?.initChessView(data, userInfoMap.get(`${+data?.chessId?.split('-')?.[0]}`))
      ChessManager.chessMap.set(data.chessId, chessNode)
      if ((index + 1) === chessInfos.length) {
        ChessManager.scaleMulChess(chessInfos)
        userInfoMap = null
      }
    })
    console.log("玩家视图加载完毕，" + new Date().getTime());
    await Utils.timeOut1(200)
    this.updateAllPlayerStatus()
    // console.info("开始加载顶部得分面板，" + new Date().getTime());

    EventMgr.dispatchEvent(EventName.UPDATE_TOP_AREA_DATA)

    ViewManager?.topAreaScript?.updateAllInfo()
    // ViewManager?.topArea_classicScript?.updateAllInfo()
    globalData.gameSkins = await getGameExtraConfig()
    console.info("GameExtraConfig获取完毕，" + new Date().getTime())
    director.emit(EventType.CHANGE_SKIN)
  }

  /**
   * @description destroy pre nodes
   */
  public static resetView() {
    [this.gameInstance.userGroupNode, this.gameInstance.chessGroupNode]
      .forEach((node: Node) => {
        node?.children?.forEach((node) => {
          node?.destroy()
        })
      })
    Tween.stopAll()
    this.destroyGameOverView()
    this.baseComponentManager.destroyMasterView()
  }

  private static async _initSystemInfo() {
    let appInfo: IGetSystemInfoRes
    try {
      appInfo = await getAppInfo()
      // console.log(`【cocos】加载本地资源`);
      loadLoaclResource(`i18n/${appInfo.language}`, JsonAsset).then((res: JsonAsset) => { window.cocosI18n = res?.json })
      // console.log(`【cocos】本地资源加载完毕`);
      // console.log(`【cocos】开始获取UserInfo`);
      globalData.mineUserInfo = await getUserInfo()
      // console.log(`【cocos】获取到AppInfo${JSON.stringify(globalData.mineUserInfo)}`);
      // if (appInfo?.isPreload) notifyGamePreloaded(1, '');
      // console.log('isPreload', appInfo?.isPreload)
    } catch (err) {
      // notifyGamePreloaded(0, err?.message || 'error, pls try again');
    } finally {
      // !appInfo?.isPreload && this.handlePublicLoad(appInfo)
    }
  }

  /**
   * @name 初始化棋盘UI页面
   * @param userIndex 当前玩家序号(棋子类型),观众传 -1
   * @param playerList 玩家信息
   * @param chessInfo 棋子信息
   */
  public static async initUiInfo(userIndex: ChessType, playerList: any[], chessInfo: any[], endMode: END_MODE, obstacleInfo?: number[]) {
    console.info("开始获取数据：getGameExtraConfig，" + new Date().getTime())

    this._initSystemInfo();
    // 处理棋盘旋转逻辑数据
    let boardAngle = 0, boxAngle = 0
    switch (Number(userIndex)) {
      case 1: {
        boardAngle = -180
        boxAngle = -180
        this.playerSortPosIndexs = [3, 4, 1, 2]
        break
      }
      case 2: {
        boardAngle = 90
        boxAngle = -90
        this.playerSortPosIndexs = [4, 1, 2, 3]
        break
      }
      case 4: {
        boardAngle = -90
        boxAngle = 90
        this.playerSortPosIndexs = [2, 3, 4, 1]
        break
      }
      default: {
        this.playerSortPosIndexs = [1, 2, 3, 4]
        break
      }
    }
    console.info("初始化棋盘视图，" + new Date().getTime())
    this.initChessboardView(boardAngle)
    console.info("棋盘视图初始化完毕，" + new Date().getTime())
    this.initAllPlayerView(playerList, chessInfo)
    obstacleInfo?.length && this.toggleObstacleVisiable(obstacleInfo)
    setTimeout(() => {
      EventMgr.dispatchEvent(EventName.EVENT_RENDER_BASE_COMPONENT)
      director.emit(EventType.CHANGE_VIEW)
    }, 1000)
  }

  /**
   * @name 棋子移动
   * @param chessId  移动的棋子id(格式为“用户坐标-棋子序号”) 如 "1-1"
   * @param movePath 棋子移动的路径 如 [40,41,42,43,44]
   * @param eatOff 吃掉棋子的id：例如棋子“2-3”刚好在地图44的位置
   * @param finalChesses 棋子最后的位置
   * @param obstacleInfo 障碍物
   * @param userCoinInfo 吃棋用户信息
   * @param opIndex opIndex
   * @param restDiceNumArray  摇骰子数据
   * @param roundEnd 回合结束
   */
  public static async chessMove(
    chessId: string,
    movePath: number[],
    eatOff: string[],
    finalChesses: Array<ChessInfo>,
    obstacleInfo: number[],
    userCoinInfo: Array<any>,
    opIndex: any = null, restDiceNumArray: any = null, roundEnd: boolean = null, teamName = "", reachDestCost = 0, teammateStatus = 3,
  ) {
    globalData.isPlaying = true;
    const moveNode = ChessManager.chessMap.get(chessId)
    if (!moveNode) {
      globalData.isPlaying = false;
      return
    }
    this.closeDiceWave()
    this.closeDiceDialog()
    this.closePlayerCountdownEffect()
    this.closePlayerResetCountdownEffect()
    await ChessManager.chessPosMove(moveNode, movePath, !!eatOff?.length, teamName, reachDestCost, teammateStatus)
    this.toggleObstacleVisiable(obstacleInfo)

    if (eatOff?.length) {
      this.baseComponentManager.playEatChessAnimation(moveNode)
      AudioMgr.instance.play('audio/sound_eat')
      await Utils.timeOut1(500)
      eatOff.forEach((beEatChessId: string, index: number, self: string[]) => {
        const beEatChess = ChessManager.chessMap.get(beEatChessId)
        if (!beEatChess?.isValid) return
        sensorResult({
          result_type: 'LudoEatChess',
          element_business_id: globalData.gameInfo?.playerInfo?.find(p => +p.userIndex === +beEatChessId.split('-')?.[0])?.userId
        })
        AudioMgr.instance.play('audio/sound_eatBack')
        ActionManager.backBirthPlace(beEatChess, moveNode, (self?.length - 1) === index)
      })
      await Utils.timeOut1(1000)
      await this.chessMoveResultEffect(userCoinInfo)
    }
    globalData.isPlaying = false;
    if (!globalData.isPlaying && !eatOff?.length) {
      // console.log("--------scaleMulChess")
      ChessManager.scaleMulChess(finalChesses)
    }
    this.chessMoveFinish(finalChesses, opIndex, restDiceNumArray, roundEnd, movePath);
    return
  }

    /**
   * @name 棋子移动
   * @param chessId  移动的棋子id(格式为“用户坐标-棋子序号”) 如 "1-1"
   * @param movePath 棋子移动的路径 如 [40,41,42,43,44]
   * @param eatOff 吃掉棋子的id：例如棋子“2-3”刚好在地图44的位置
   * @param finalChesses 棋子最后的位置
   * @param obstacleInfo 障碍物
   * @param userCoinInfo 吃棋用户信息
   * @param opIndex opIndex
   * @param restDiceNumArray  摇骰子数据
   * @param roundEnd 回合结束
   */
    public static async classicChessMove(
      chessId: string,
      movePath: number[],
      eatOff: string[],
      finalChesses: Array<ChessInfo>,
      obstacleInfo: number[],
      extraData: Record<any, any> = { exp: 0, combo: 0, userIndex1st: 0 },
      opIndex: any = null, restDiceNumArray: any = null, roundEnd: boolean = null, teamName = "", reachDestCost = 0, teammateStatus = 3,
    ) {
      globalData.isPlaying = true;
      const moveNode = ChessManager.chessMap.get(chessId)
      if (!moveNode) {
        globalData.isPlaying = false;
        return
      }
      this.closeDiceWave()
      this.closeDiceDialog()
      this.closePlayerCountdownEffect()
      this.closePlayerResetCountdownEffect()
      await ChessManager.chessPosMove(moveNode, movePath, !!eatOff?.length, teamName, reachDestCost, teammateStatus)
      this.toggleObstacleVisiable(obstacleInfo)
  
      if (eatOff?.length) {
        console.error("====chessMove====", chessId, eatOff?.length, opIndex, JSON.stringify(extraData))
        this.baseComponentManager.playEatChessAnimation(moveNode, extraData)
        AudioMgr.instance.play('audio/sound_eat')
        await Utils.timeOut1(500)
        eatOff.forEach((beEatChessId: string, index: number, self: string[]) => {
          const beEatChess = ChessManager.chessMap.get(beEatChessId)
          if (!beEatChess) return
          sensorResult({
            result_type: 'LudoEatChess',
            element_business_id: globalData.gameInfo?.playerInfo?.find(p => +p.userIndex === +beEatChessId.split('-')?.[0])?.userId
          })
          AudioMgr.instance.play('audio/sound_eatBack')
          ActionManager.backBirthPlace(beEatChess, moveNode, (self?.length - 1) === index)
        })
        await Utils.timeOut1(1000)
        this.classicChessMoveResultEffect(moveNode, opIndex, extraData)
        await Utils.timeOut1(300)
      }
      globalData.isPlaying = false;
      if (!globalData.isPlaying && !eatOff?.length) {
        // console.log("--------scaleMulChess")
        ChessManager.scaleMulChess(finalChesses)
      }
      EventMgr.dispatchEvent(EventName.EVENT_PLAYER_RENDER_1ST, extraData?.userIndex1st)
      this.chessMoveFinish(finalChesses, opIndex, restDiceNumArray, roundEnd, movePath);
    }

  /**
   * 棋子移动完成通知
   * @param finalChesses 
   * @param opIndex 
   * @param restDiceNumArray 
   */
  public static chessMoveFinish(finalChesses: any, opIndex: any, restDiceNumArray: any, roundEnd: boolean, movePath: Array<number>) {
    if (finalChesses == null && opIndex == null && roundEnd == null) return;
    if (roundEnd) {
      // if (!globalData.isPlaying) {
      //   console.log("--------scaleMulChess")
      //   ChessManager.scaleMulChess(finalChesses)
      // }
    } else {
      ChessManager.moveFinishedFixChessPosition(finalChesses);
      // if (!globalData.isPlaying) {
      //   console.log("--------scaleMulChess1")
      //   ChessManager.scaleMulChess(finalChesses)
      // }
    }

    // 本地模式下触发棋子移动完成事件
    if (GameConfig.FORCE_LOCAL_MODE) {
      console.log('♟️ 本地模式 - 棋子移动完成');
      director.emit('chess-move-finished');
    }
    ViewManager.notifyPlayerDiceList(opIndex, restDiceNumArray)
    globalData.gameInfo.chessInfo = finalChesses
    // 只有当本次走棋是自己的回合才会发送finishMove
    if (globalData.isMyRound) {
      globalData.socketSend(SOCKET_TYPE.OPERATION, { subCommand: { commandKey: 'finishMove' } })
    }
  }

  /**
   * 返回初始点
   */
  public static async rediscoverPhotography(backId: string) {
    if (!backId) return;
    ActionManager.backBirthPlace(ChessManager.chessMap.get(backId))
  }

  /**
   * @description move result effect
   */
  public static async chessMoveResultEffect(userCoinInfo: any[]) {
    console.log("--------------------sss", JSON.stringify(userCoinInfo));
    if (userCoinInfo.length) {
      if (userCoinInfo && userCoinInfo.length > 0) globalData.gameInfo.userCoinInfo = userCoinInfo
      const targetData = userCoinInfo.filter(i => i.balance !== 0)
      this.updateAllPlayerStatus()
      this.playPlayerBalanceEffect(targetData)
      await Utils.timeOut1(2000)
      EventMgr.dispatchEvent(EventName.EVENT_TOP_ITEM_UPDATE);
      let winnerData = userCoinInfo.filter((data: any) => data.balance > 0)
      let flyUserId = winnerData[0]?.userId;
      if (globalData?.gameInfo?.gameMode == GAME_MODE.TWO_VS_TWO) {
        // cs.log("win:" + JSON.stringify(winnerData));
        const temp = userCoinInfo.filter((data: any) => (data?.teamName == winnerData[0]?.teamName && data.userId != winnerData[0]?.userId));
        // cs.log("temp:" + JSON.stringify(temp));
        if (temp.length > 0 && winnerData) {
          winnerData = winnerData.concat(temp)
        }
        winnerData = winnerData.sort((a, b) => {
          return a.coin >= b.coin ? -1 : 1;
        })
        // cs.log("newWin:" + JSON.stringify(winnerData));
      }
      let isMy = false
      let flyIndex = 0;
      let userId = [];
      if (winnerData.length > 0) {
        const loserData = targetData.find((data: any) => data.balance < 0)
        if (loserData) {
          for (let i = 0; i < winnerData.length; i++) {
            !isMy && (isMy = winnerData[i].userId == String(globalData.myUserId));
            const index = 0;//isMy ? Math.max(0, i - 1) : i;
            if (flyUserId == winnerData[i].userId) {
              const result = ViewManager.topAreaScript.setLabActiveView(winnerData[i].userId
                , !(winnerData[i].multiple > 0), !(winnerData[i].eatChessNum > 0), !(winnerData[i].balance > 0), null, index)
              this.baseComponentManager?.playNumEffectAnimation(
                winnerData[i].userId,
                I18n.languageState === 2 ? `${winnerData[i].multiple}×` : `×${winnerData[i].multiple}`,
                winnerData[i].eatChessNum <= 0 ? "0" : (I18n.languageState === 2 ? `${winnerData[i].eatChessNum}+` : `+${winnerData[i].eatChessNum}`),
                `${winnerData[i]?.coin}`,
                `${winnerData[i]?.balance}`
                , index
              )
              setTimeout(() => ViewManager.topAreaScript.setLabActiveView(winnerData[i].userId, true, true, true, result), 2600)
              AudioMgr.instance.play('audio/sound_flyCoin');
            }
            userId.push(winnerData[i].userId);
            // (flyUserId == winnerData[i].userId) && (flyIndex = isMy ? Math.max(0, i - 1) : i);
          }
          this.baseComponentManager.togggleTopModal(userId)
          // console.log("------- >>>>>111", JSON.stringify(loserData));
          // console.log("------- >>>>>", loserData?.userIndex, flyUserId, flyIndex);
          this.baseComponentManager?.playCoinSliceAnimation(loserData?.userIndex, flyUserId, flyIndex)
          await Utils.timeOut1(3500)
        }
        // const winnerData = targetData.find((data: any) => data.balance > 0)
        // if (winnerData) {
        //   const loserData = targetData.find((data: any) => data.balance < 0)
        //   if (loserData) {
        //     // TODO: code perf
        //     const result = ViewManager.topAreaScript.setLabActive(winnerData.userId, false)
        //     this.baseComponentManager?.playNumEffectAnimation(
        //       winnerData.userId,
        //       I18n.languageState === 2 ? `${winnerData.multiple}×` : `×${winnerData.multiple}`,
        //       I18n.languageState === 2 ? `${winnerData.eatChessNum}+` : `+${winnerData.eatChessNum}`,
        //       `${winnerData?.coin}`
        //     )
        //     // TODO: code perf
        //     setTimeout(() => ViewManager.topAreaScript.setLabActive(winnerData.userId, true, result), 3250)
        //     SoundMgr.instance.play('audio/sound_flyCoin');
        //     this.baseComponentManager.togggleTopModal(winnerData.userId)
        //     //TODO:
        //     this.baseComponentManager.togggleTopModal(winnerData.userId)
        //     // this.baseComponentManager?.playCoinSliceAnimation(loserData?.userIndex, winnerData.userId)
        //     await Utils.timeOut1(3500)
        //   }
      }
    }
    return
  }

  /**
   * @zh 基础模式吃棋子效果 @en
   * 
   */
  public static classicChessMoveResultEffect(chessNode: Node, winIndex: number, extraData: Record<string, any>) {
    const avatorNode = ViewManager.getPlayerMgr(winIndex)?._avatorNode
    if (!avatorNode) return
    const chessCoor = commonUtil.convertToNodeSpaceAR(this.baseComponentManager?.node, commonUtil.convertToWorldSpaceARToZero(chessNode))
    const playerCoor = commonUtil.convertToNodeSpaceAR(this.baseComponentManager?.node, commonUtil.convertToWorldSpaceARToZero(avatorNode))
    if (extraData?.exp >= 0) {
      EventMgr.dispatchEvent(
        EventName.EVENT_STAR_FLY_ANI,
        chessCoor,
        playerCoor,
        () => EventMgr.dispatchEvent(EventName.EVENT_EXP_FLY_ANI, playerCoor, extraData?.exp || 0)
      )
    }
  }

  /**
   * @zh 播放收支特效 @en play balance animation
   */
  public static playPlayerBalanceEffect(userCoinInfo: any[]) {
    if (!userCoinInfo?.length) return
    this.baseComponentManager?.playBanlanceAnimation(userCoinInfo.filter(i => i.balance !== 0))
  }

  /**
   * @name 通知玩家操作骰子
   * @param opUserId 操作用户Id
   * @param opIndex 棋子类型
   * @param countDown 倒计时 /ms
   */
  public static notifyPlayerOpDice(opUserId?: string, opIndex?: number, countDown?: number, diceNumArray?: number[]) {
    this.playerSortPosIndexs.forEach((userIndex: ChessType) => {
      const playerManager = this.getPlayerMgr(userIndex)
      if (!playerManager) return

      if (userIndex === opIndex) {
        playerManager?.setDiceGroupView(diceNumArray)
        playerManager?.toggleDialogVisiable(true)
        playerManager?.toggleArrowVisiable(true)
        playerManager?.setOpDiceView(diceNumArray?.length > 0 ? diceNumArray[diceNumArray.length - 1] : 0)
        playerManager?.playPlayerOpCountTime(countDown, true)
      } else {
        playerManager?.setDiceGroupView([])
        playerManager?.toggleDialogVisiable()
        playerManager?.playPlayerOpCountTime(0)
        playerManager?.toggleArrowVisiable(false)
        playerManager?.setOpDiceView(0)
      }
    })
  }

  /**
   * 检查是否是自己回合
   */
  public static checkIsSelfRound() {
    if (!globalData.isMyRound) {
      const playerManager = this.getPlayerMgr(gameLocalServer.instance.selfOpIdx);
      // console.log("------------", gameLocalServer.instance.selfOpIdx, playerManager);
      playerManager?.forceCloseVisiable();
    }
  }

  /**
   * when turn to somebody select dice, show his countdown effect
   * @param opIndex 
   * @param countDown 
   */
  public static showPlayerCountdownEffect(opIndex?: number, countDown?: number) {
    this.getPlayerMgr(opIndex)?.playPlayerOpCountTime(countDown)
  }

  /**
   * @name 关闭玩家倒计时效果
   * @param opIndex 
   */
  public static closePlayerCountdownEffect() {
    this.playerSortPosIndexs?.forEach((userIndex: ChessType) => {
      this.getPlayerMgr(userIndex)?.playPlayerOpCountTime(0)
    })
  }

  /**
   * @name 关闭重置倒计时效果
   * @param opIndex 
   */
  public static closePlayerResetCountdownEffect() {
    this.playerSortPosIndexs?.forEach((userIndex: ChessType) => {
      this.getPlayerMgr(userIndex)?.playPlayerResetCountTime(0)
    })
  }

  /**
   * @name 通知玩家选择骰子
   * @param canMoveChessMap 可以选择棋子id 和 使用的骰子值
   * @param isShowWave 是否显示波纹
   */
  public static notifyPlayerSelectDice(canMoveChessMap: Map<string, number[]>) {
    globalData.canMoveChessMap = canMoveChessMap;
    Array.from(canMoveChessMap.keys()).forEach((chessId: string) => {
      const chessNode = ChessManager.chessMap.get(chessId)
      if (chessNode) {
        const chessIndex = (+ChessManager.chessPosIndexMap.get(chessId))
        commonUtil.setScale(chessNode, largeScaleRate)
        commonUtil.setIndex(chessNode, (chessIndex + ELayerIndex.Selected));//chessIndex === 0 ? 0 :
        // 播放波纹行为
        commonUtil.setActive(chessNode.getChildByName('Action'), true)
      }
    })
  }

  /**
   * @name 关掉棋子波纹
   */
  public static closeDiceWave() {
    globalData.canMoveChessMap = null;
    allChesses.forEach((chessId: string) => {
      const chessNode = ChessManager.chessMap.get(chessId);
      if (!chessNode) return;
      commonUtil.setActive(chessNode.getChildByName('Action'), false)
    })
  }

  /**
   * @name 关闭选择骰子框
   */
  public static closeDiceDialog() {
    allChesses.forEach((chessId: string) => {
      ChessManager.chessMap
        ?.get(chessId)
        ?.getComponent(ChessDialogManager)
        ?.toggleDialogVisiable()
    })
  }

  /**
   * @name 显示摇骰子结果
   * @param opUserId 当前操作玩家
   * @param opIndex 操作玩家座位号
   * @param diceNum 骰子结果 传入0时，显示默认皇冠状态，有骰子点才播放动画
   * @param diceNumArray 结果集
   */
  public static notifyDiceResult(opUserId: string, opIndex: number, diceNum?: number, diceNumArray?: number[]) {
    return new Promise(async (resolve) => {
      if (opUserId && opIndex) {
        const playerMgr = this.getPlayerMgr(opIndex)
        if (playerMgr) {
          playerMgr.toggleDialogVisiable(true)
          await playerMgr.playDiceResult(diceNum, diceNumArray)
        }
      }
      resolve('')
    })
  }

  /**
   * @name 通知显示对应玩家状态
   * @param opIndexs 操作玩家标示
   * @param playerStatus 玩家状态 游戏中 托管 逃离
   */
  public static notifyPlayerStatus(opIndex: number, playerStatus: PlayerStatus) {
    this.getPlayerMgr(opIndex)?.playPlayerStatus(playerStatus)
  }

  /**
   * @name 更新所有玩家状态
   */
  public static updateAllPlayerStatus() {
    EventMgr.dispatchEvent(EventName.EVENT_PLAYER_RENDER_1ST, globalData.gameInfo?.modeData?.userIndex1st)
    globalData.gameInfo.playerInfo
      ?.forEach((player) => {
        if (player.userId === globalData.myUserId) {
          this.notifyPlayerStatus(player.userIndex, player.status)
        } else if (player.status !== PlayerStatus.Hosting) {
          this.notifyPlayerStatus(player.userIndex, player.status)
        }
      })
  }

  /**
   * @name 通知玩家重置骰子状态
   * @param opIndexs 操作玩家标示
   * @param resetLabel 重置文案
   */
  public static notifyPlayerResetBtnView(opIndex: number, resetLabel: string) {
    this.getPlayerMgr(opIndex)?.setResetDiceView(resetLabel)
  }

  /**
   * @name 通知重置骰子倒计时（记住要先调用 notifyPlayerOpDice 才有效）
   * @param opIndexs 操作玩家标示
   * @param countDown 倒计时时间
   */
  public static notifyResetDiceCountdown(opIndex: number, countDown: number) {
    this.getPlayerMgr(opIndex)?.playPlayerResetCountTime(countDown)
  }

  /**
   * @name 通知玩家骰子列表结果
   */
  public static notifyPlayerDiceList(opIndex: number, diceNums: number[]) {
    this.getPlayerMgr(opIndex)?.setDiceGroupView(diceNums || [])
  }

  /**
   * @description toggle visiable border
   */
  public static toggleAvatorBorderVisiable(userId: string | number, isVisiable: boolean) {
    this.getPlayerMgr(globalData.gameInfo?.playerInfo?.find(p => p.userId === userId)?.userIndex || 0)
      ?._avatorNode
      ?.getComponent(ProcessCoverManager)
      ?.toggleBorderVisiable(!!isVisiable)
  }

  /**
   * @name get resetNode information
   */
  public static getResetButtonInfo() {
    return this.getPlayerMgr(this.playerSortPosIndexs[2])?.getResetButtonInfo()
  }

  /**
   * @zh 初始化游戏结束弹窗视图 @en destroy over view
   */
  public static async destroyGameOverView() {
    commonUtil.setActive(this.gameOverDialogNode, false)
    this.gameOverDialogNode?.destroy()
    this.gameOverDialogNode = null
  }

  /**
   * @zh 切换显示结算特效 @en toggle visiable particle
   */
  public static toggleVisiableResultParticle(isVisiable: boolean, targetVerc?: Vec3) {
    const aniBoxNode: Node = this.gameInstance?.aniBoxNode
    if (aniBoxNode?.isValid) {
      const particleSystem2D = aniBoxNode.getComponent(ParticleSystem2D)
      if (!particleSystem2D) return
      particleSystem2D.stopSystem()
      commonUtil.setActive(aniBoxNode, isVisiable)
      if (targetVerc && isVisiable) {
        aniBoxNode.setPosition(targetVerc)
        particleSystem2D.resetSystem()
      }
    }
  }

  /**
   * @zh 显示障碍物视图 @en toggle obstacle view
   */
  public static toggleObstacleVisiable(obstacleInfo: number[]) {
    this.gameInstance?.obstacleNode
      ?.getComponent(ObstacleManager)
      ?.toggleObstacleVisiable(obstacleInfo || [])
  }
}
