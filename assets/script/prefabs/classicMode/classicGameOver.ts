import { Label, Layout, Node, Sprite, Sprite<PERSON><PERSON>e, tween, Tween, v3, Vec3, _decorator } from "cc";
import I18nLabel from "../../components/i18nLabel";
import { EventMgr } from "../../framework/eventMgr";
import { EventName } from "../../framework/eventName";
import { Utils } from "../../framework/frameworkUtils";
import { I18n } from "../../framework/i18n";
import { LoadMgr } from "../../framework/loadMgr";
import { Queue } from "../../framework/queue";
import SuperPanel from "../../framework/superPanel";
import globalData from "../../globalData";
import { AudioMgr } from "../../manager/audioMgr";
import { GameOverMgr } from "../../manager/gameOverMgr";
import ViewManager from "../../manager/view";
import { notifyLeaveGame } from "../../seal/bridge";
import { sealBridge, sealClick } from "../../seal/SealBridge";
import { commonUtil } from "../../utils/commonUtil";
import { sensorAppViewScreen, sensorClick } from "../../utils/sensor";
import ClassicsettlementItem from "./classicsettlementItem";

const { ccclass, property } = _decorator;

const OpenState = { bankruptcy: 2, lose: 1, win: 0 }
interface IStarObj { x: number, y: number, w: number, h: number }

@ccclass("ClassicGameOver")
export default class ClassicGameOver extends SuperPanel {
    private allItemArr: Array<{ node: Node, script: ClassicsettlementItem }> = [];
    private queue: Queue = null;
    private gameFrame: number = 60;
    // 0 win 1 fail 2 bankruptcy  3 新手引导
    private openState: number = 0;
    //后改成resource加载
    @property(SpriteFrame)
    ribbon: SpriteFrame = null;
    @property(SpriteFrame)
    trophy: SpriteFrame = null;
    @property(SpriteFrame)
    winRibbon: SpriteFrame = null;
    @property(SpriteFrame)
    winTrophy: SpriteFrame = null;

    private lightTween: any = null;
    private starTween: any = null;
    private starTween1: any = null;

    private _curRefreshLabelTime = 0;
    private _refreshLabelTime = 1;
    private _cd = 10;
    private _isPause = true;
    private testData = [{
        portrait: "https://img.moegirl.org.cn/common/thumb/4/4b/Nagato_Yuki2.jpg/280px-Nagato_Yuki2.jpg", // 头像
        portraitMask: "",  // 头像外框
        name: "ربحتcccccccccccccccccccc", // 名称
        //  实际扣减金额数
        "actuallyBeEatChessGainBalance": 0,
        // 吃棋-平台实际收费收益
        "actuallyChargeBalance": 0,
        // 吃棋实际获得金额数(玩家破产)
        "actuallyEatChessGainBalanceBroke": 0,
        // 吃棋实际获得金额数(玩家未破产)
        "actuallyEatChessGainBalanceNotBroke": 0,
        // 总收益
        "actuallyTotalBalance": 200,
        //与赢家实际结算金额(针对破产玩家) // 使用这个字段
        "actuallyWinnerSettleAmountBroke": 0,
        //与赢家实际结算金额(针对未破产玩家)
        "actuallyWinnerSettleAmountNotBroke": 0,
        // 被吃棋子个数
        "beEatChess": 0,
        //  实际扣减金额数
        "beEatChessGainBalance": 0,
        //被吃棋倍数 (倍数 ) 
        "beEatMultiple": 0,
        // 吃棋-平台实际收费收益
        "chargeBalance": 0,
        // 收费比例 1-100 吃棋-平台收费比例 1-100
        "chargeRate": 5,
        // 吃棋子个数
        "eatChess": 3,
        // 吃棋-平台实际收费收益
        "eatChessActuallyChargeBalance": 0,
        // 吃棋底注
        "eatChessAntes": 300,
        //吃棋破产玩家数
        "eatChessBrokeAmount": 0,
        //吃棋-平台收费收益
        "eatChessChargeBalance": 45,
        // 收费比例 1-100 吃棋-平台收费比例 1-100
        "eatChessChargeRate": 5,
        //吃棋应获得金币数
        "eatChessGainBalance": 900,
        //吃棋倍率
        "eatMultiple": 0,
        // 1局中 2结算
        "from": 2,
        //游戏破产玩家数
        "gameSettleBrokeAmount": 2,
        //是否破产  是否破产: 0-未破产, 1-局中破产, 2-局结束破产
        "isBroke": 0,
        //是否为赢家:1:输，2:赢，3:平局
        "isWinner": 2,
        //应收总收益
        "totalBalance": 3285,
        "userId": "5237977668502160428",
        //赢家吃棋数
        "winnerEatChess": 3,
        //赢家吃棋倍数
        "winnerEatMultiple": 0,
        //与赢家应结算金额
        "winnerSettleAmount": 2565,

        gameEscapeUserAmount: 1, // 游戏逃跑玩家数
        escapeBalance: 300,  //逃跑玩家需支出的金币数（理论）
        profitFromEscapeBalance: 100, // 非逃跑玩家收到的金币数（理论）
        actuallyEscapeBalance: 300,//逃跑玩家需支出的金币数(实际，考虑用户余额不足情况)
        actuallyProfitFromEscapeBalance: 100, //非逃跑玩家收到的金币数(实际，考虑用户余额不足情况)
        isEscape: 0,  //是否逃跑: 0-未逃跑, 1-已逃跑
        teamName: "A"
    },
    {
        portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", // 头像
        portraitMask: "",  // 头像外框
        name: 'مضاعف', // 名称
        "actuallyBeEatChessGainBalance": 205,
        "actuallyChargeBalance": 0,
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 0,
        "actuallyTotalBalance": -205,
        "actuallyWinnerSettleAmountBroke": 0,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChess": 1,
        "beEatChessGainBalance": 300,
        "beEatMultiple": 1,
        "chargeBalance": 0,
        "chargeRate": 0,
        "eatChess": 0,
        "eatChessActuallyChargeBalance": 0,
        "eatChessAntes": 300,
        "eatChessBrokeAmount": 0,
        "eatChessChargeBalance": 0,
        "eatChessChargeRate": 5,
        "eatChessGainBalance": 0,
        "eatMultiple": 0,
        "from": 2,
        "gameSettleBrokeAmount": 3,
        "isBroke": 1,//是否破产: 0-未破产, 1-已破产
        "isWinner": 2,
        "totalBalance": -1200,
        "userId": "5112984037157282860",
        "winnerEatChess": 3,
        "winnerEatMultiple": 1,
        "winnerSettleAmount": 900,
        gameEscapeUserAmount: 1, // 游戏逃跑玩家数
        escapeBalance: 300,  //逃跑玩家需支出的金币数（理论）
        profitFromEscapeBalance: 100, // 非逃跑玩家收到的金币数（理论）
        actuallyEscapeBalance: 300,//逃跑玩家需支出的金币数(实际，考虑用户余额不足情况)
        actuallyProfitFromEscapeBalance: 100, //非逃跑玩家收到的金币数(实际，考虑用户余额不足情况)
        isEscape: 1,  //是否逃跑: 0-未逃跑, 1-已逃跑
        teamName: "A"
    }, {
        portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", // 头像
        portraitMask: "",  // 头像外框
        name: "111123456223", // 名称
        "actuallyBeEatChessGainBalance": 241,
        "actuallyChargeBalance": 0,
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 0,
        "actuallyTotalBalance": -241,
        "actuallyWinnerSettleAmountBroke": 0,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChess": 1,
        "beEatChessGainBalance": 300,
        "beEatMultiple": 1,
        "chargeBalance": 0,
        "chargeRate": 0,
        "eatChess": 0,
        "eatChessActuallyChargeBalance": 0,
        "eatChessAntes": 300,
        "eatChessBrokeAmount": 0,
        "eatChessChargeBalance": 0,
        "eatChessChargeRate": 5,
        "eatChessGainBalance": 0,
        "eatMultiple": 0,
        "from": 2,
        "gameSettleBrokeAmount": 3,
        "isBroke": 2,
        "isWinner": 1,
        "totalBalance": -1200,
        "userId": "5113170366262411308",
        "winnerEatChess": 3,
        "winnerEatMultiple": 1,
        "winnerSettleAmount": 900,
        gameEscapeUserAmount: 1, // 游戏逃跑玩家数
        escapeBalance: 300,  //逃跑玩家需支出的金币数（理论）
        profitFromEscapeBalance: 100, // 非逃跑玩家收到的金币数（理论）
        actuallyEscapeBalance: 300,//逃跑玩家需支出的金币数(实际，考虑用户余额不足情况)
        actuallyProfitFromEscapeBalance: 100, //非逃跑玩家收到的金币数(实际，考虑用户余额不足情况)
        isEscape: 0,  //是否逃跑: 0-未逃跑, 1-已逃跑
        teamName: "B"
    }, {
        portrait: "https://cdnoffice.lizhi.fm/user/2022/03/30/2932132245741221378.jpg", // 头像
        portraitMask: "",  // 头像外框
        name: "111123456224", // 名称
        "actuallyBeEatChessGainBalance": 258,
        "actuallyChargeBalance": 0,
        "actuallyEatChessGainBalanceBroke": 0,
        "actuallyEatChessGainBalanceNotBroke": 0,
        "actuallyTotalBalance": -258,
        "actuallyWinnerSettleAmountBroke": 0,
        "actuallyWinnerSettleAmountNotBroke": 0,
        "beEatChess": 1,
        "beEatChessGainBalance": 300,
        "beEatMultiple": 1,
        "chargeBalance": 0,
        "chargeRate": 0,
        "eatChess": 0,
        "eatChessActuallyChargeBalance": 0,
        "eatChessAntes": 300,
        "eatChessBrokeAmount": 0,
        "eatChessChargeBalance": 0,
        "eatChessChargeRate": 5,
        "eatChessGainBalance": 0,
        "eatMultiple": 0,
        "from": 2,
        "gameSettleBrokeAmount": 3,
        "isBroke": 2,
        "isWinner": 1,
        "totalBalance": -1200,
        "userId": "5113286746922058796",
        "winnerEatChess": 3,
        "winnerEatMultiple": 1,
        "winnerSettleAmount": 900,
        gameEscapeUserAmount: 1, // 游戏逃跑玩家数
        escapeBalance: 300,  //逃跑玩家需支出的金币数（理论）
        profitFromEscapeBalance: 100, // 非逃跑玩家收到的金币数（理论）
        actuallyEscapeBalance: 300,//逃跑玩家需支出的金币数(实际，考虑用户余额不足情况)
        actuallyProfitFromEscapeBalance: 100, //非逃跑玩家收到的金币数(实际，考虑用户余额不足情况)
        isEscape: 1,  //是否逃跑: 0-未逃跑, 1-已逃跑
        teamName: "B"
    }
    ]

    // ？？？翻转
    protected reserve(): void {
        this.isAddBlockInputEvent = false;
        this.panelAnim.anim = {
            openIsAnim: false,
            openAnimType: -1,
            closeIsAnim: false,
            closeAnimType: -1
        }
    }

    /// TODO  待优化列表 超出屏幕部分就进行隐藏或者透明度进行调整
    init(data: any): void {
        super.init(data);
        this.openState = this.myData.type;
        this.playSound(this.openState);
        this.allItemArr = [];
        this.queue = new Queue();
        this.initUi();
        this.playAnim();
        this.refrshCd();
        sensorAppViewScreen({
            "$title": 'ludo游戏结果页'
        })
    }

    private playSound(type: any) {
        //0 破产 1 lose 2 win 
        switch (type) {
            case OpenState.bankruptcy:
                break;
            case OpenState.win:
                AudioMgr.instance.play('audio/sound_resultWin');
                break;
            case OpenState.lose:
                AudioMgr.instance.play('audio/sound_resultFail');
                break;
            default:
                break;
        }
    }

    protected onEnable(): void {
        super.onEnable();
        //TODO:
        // this.init(0);
    }

    private initUi() {
        if (this.openState == 1 || this.openState == 2) {
            this.view["ribbon"].getComponent(Sprite).spriteFrame = this.ribbon;
            this.view["trophy"].getComponent(Sprite).spriteFrame = this.trophy;
        } else {
            this.view["ribbon"].getComponent(Sprite).spriteFrame = this.winRibbon;
            this.view["trophy"].getComponent(Sprite).spriteFrame = this.winTrophy;
        }
        this.tools.visible("trophy", this.openState == 0)
        this.tools.visible("quitGameBtn", this.openState !== 3);
        this.tools.visible("nextGameBtn", this.openState !== 3);
        this.tools.visible("guideStartBtn", this.openState == 3);

        //
        this.tools.visible("btnArea", false);
        this.tools.opacity("btnArea", 0);

        this.tools.visible("ribbon", false);
        this.tools.opacity("ribbon", 0);
        // this.tools.scale("trophy", 0.3);
        commonUtil.setY(this.view["trophy"], 2);
        // this.view["trophy"].y = 2;
        this.tools.opacity("trophy", 0);
        this.tools.scale("light", 0.6);
        this.tools.opacity("light", 0);
        this.view["light"].angle = 0;
        for (let i = 1; i <= 6; i++) {
            commonUtil.setHeight(this.view["star" + i], 0);
            commonUtil.setWidth(this.view["star" + i], 0);
            commonUtil.setPosition(this.view["star" + i], v3(0, 101, 0));
            // this.view["star" + i].height = 0;
            // this.view["star" + i].width = 0;
            // this.view["star" + i].x = 0;
            // this.view["star" + i].y = 101;
        }
        this.udpateI18nLab();
    }

    protected udpateI18nLab() {
        // win   离开游戏  下一局
        // fail 离开游戏  下一局
        // 破产  离开游戏  继续围观
        // 引导和这个里无关可以单独展示
        const keyStr = this.openState == 2 ? "continueToWatch" : "nextGame2V2";
        const quitGameText = I18n.str(keyStr);
        this.tools.text("nextGameLab", quitGameText);
        this.view["quitGameLab"].getComponent(I18nLabel).updateText();
    }

    protected onDisable(): void {
        super.onDisable();
        this._isPause = true;
        this.allItemArr.forEach((value) => {
            value?.node?.destroy();
            value.script = null;
        })
        this.allItemArr = [];
        this.view["content"].removeAllChildren();
        if (this.lightTween) {
            this.lightTween?.removeSelf();
            this.lightTween = null;
        }
        if (this.starTween) {
            this.starTween?.removeSelf();
            this.starTween = null;
        }
        if (this.starTween1) {
            this.starTween1?.removeSelf();
            this.starTween1 = null;
        }
        Tween.stopAll();
    }

    refreshGameTimeLabel() {
        this.tools.text("cd", `${Math.max(0, Math.round(this._cd))}${I18n.str("second")}`);
    }

    update(dt) {
        if (this._isPause) {
            return;
        }

        this._cd -= dt;
        this._curRefreshLabelTime += dt;

        if (this._curRefreshLabelTime >= this._refreshLabelTime) {
            this._curRefreshLabelTime = 0;
            this.refreshGameTimeLabel();
        }
        if (this._cd <= 0) {
            this.finishCd();
        }
    }

    refrshCd() {
        this._isPause = false;
        this._curRefreshLabelTime = 0;
        this._cd = 10;
        this._refreshLabelTime = 1;
    }

    finishCd() {
        this._isPause = true;
        notifyLeaveGame()
        ViewManager.destroyGameOverView()
        //TODO:退出游戏。
    }

    protected onMessageEvent(): void {
        super.onMessageEvent();
        EventMgr.on(EventName.EVENT_GAMEOVER_LAYOUT, this, this.updateLayout);
    }


    protected offMessageEvent(): void {
        super.offMessageEvent();
        EventMgr.off(EventName.EVENT_GAMEOVER_LAYOUT, this, this.updateLayout);
    }

    private async createAllItem() {
        await Utils.timeOut(this, 0.33);
        this.view["content"].getComponent(Layout).enabled = false;
        commonUtil.setHeight(this.view["content"], 549);
        // this.view["content"].height = 549;

        const info = sealBridge.debug ? this.testData : GameOverMgr.instance.coinInfo ?? [];
        if (info.length <= 0) {
            this.btnAnim();
        }
        let posArr = this.openState == 2 ? [-157.5, -301.5, -463.5, -589.5] : [-72, -231, -378, -513];
        for (let i = 0; i < info.length; i++) {
            this.createItem(i, posArr[i], info[i], info.length);
        }
    }

    private async createItem(idx: number, posY: number, data: object, rankLen: number) {
        let node = await LoadMgr.instance.createPrefab("prefab/classicMode/classicSettlementItem");
        commonUtil.setOpacity(node, 0);
        commonUtil.setY(node, posY);//- 20
        // node.opacity = 0;
        // node.y = posY - 20;
        const func = async () => {
            this.view["content"].addChild(node);
            let itemState = (this.openState == 2 && idx == 0) ? true : false;
            node.getComponent(ClassicsettlementItem).init({ itemState, idx, data, openState: this.openState, rankLen });
            let tween2 = tween(node)
                .to(0.25, { position: v3(node.position.x, posY, node.position.z) })
                .call(() => {
                    tween2?.removeSelf();
                    tween2 = null;
                })
                .start();

            let tween1 = tween(commonUtil.getUIOpacity(node))
                .to(0.25, { opacity: 255 })
                .call(() => {
                    tween1?.removeSelf();
                    tween1 = null;
                })
                .start();
            // let tween = tween(node).to(0.25, { y: posY, opacity: 255 }).call(() => {
            //     tween.removeSelf();
            //     tween = null;
            // }).start();
        }
        this.queue.queue(this, func, async () => {
            this.btnAnim();
        }, 0.008)
    }

    /** 下一局游戏 */
    protected clickNextGameBtn() {
        AudioMgr.instance.play("audio/sound_btn")
        if (this.openState == 0 || this.openState == 1) {
            sensorClick({
                $title: 'ludo游戏结果页',
                $element_name: '再玩一局',
            })
            sealClick({
                notifyName: 'replayClick',
                data: { userId: globalData.myUserId, }
            }, () => {
                console.log('=replayClick callback')
            })
        } else if (this.openState == 2) {
            this.closePage();
            sensorClick({
                $title: '破产结算弹窗',
                $element_name: '继续围观',
            })
            sealClick({
                notifyName: 'observeClick',
                data: {
                    userId: globalData.myUserId,
                }
            }, () => {
                console.log('=observeClick callback')
            })
        }

        ViewManager.destroyGameOverView()
    }

    /** 离开游戏 */
    protected clickQuitGameBtn() {
        if (this.openState == 2) {
            sensorClick({
                $title: '破产结算弹窗',
                $element_name: '离开游戏',
            })
        } else {
            sensorClick({
                $title: 'ludo游戏结果页',
                $element_name: '放弃',
            })
        }
        // this.clickClosePage();
        // notifyLeaveGame()
        sealClick({
            notifyName: 'quitClick',
            data: { userId: globalData.myUserId, }
        }, () => {
            console.log('=quitClick callback')
        })

        sensorClick({
            $title: 'LudoGameOver',
            $element_name: 'ludo游戏结束',
        })



        AudioMgr.instance.play("audio/sound_btn")
        ViewManager.destroyGameOverView()
    }

    private updateLayout() {
        this.view["content"].getComponent(Layout).updateLayout();
    }

    /** 动画启动 */
    private async playAnim() {
        this.ribbonAnim();
        this.trophyAnim();
        this.createAllItem();
        this.starAnim();
    }

    private async btnAnim() {
        await Utils.timeOut(this, 0.008)

        this.tools.visible("btnArea", true);
        this.tools.opacity("btnArea", 0);
        commonUtil.setY(this.view["btnArea"], -530)
        // this.view["btnArea"].y = -530;
        let tween1 = tween(this.view["btnArea"])
            .to(0.25, { position: v3(this.view["btnArea"].position.x, -510, this.view["btnArea"].position.z) })
            .call(() => {
                tween1?.removeSelf();
                tween1 = null;
            })
            .start();

        let tween2 = tween(commonUtil.getUIOpacity(this.view["btnArea"]))
            .to(0.25, { opacity: 255 })
            .call(() => {
                tween2?.removeSelf();
                tween2 = null;
            })
            .start();
        this.view["content"].getComponent(Layout).enabled = true;
    }

    private async ribbonAnim() {
        return new Promise((resolve) => {
            const ribbonInitY = -6;
            const ribbonEndY = -36;
            const initOpacity = 0;
            commonUtil.setY(this.view["ribbon"], ribbonInitY);
            // this.view["ribbon"].y = ribbonInitY;
            this.tools.visible("ribbon", true);
            this.tools.opacity("ribbon", initOpacity);
            let ribbonTween = tween(this.view["ribbon"])
                .to(0.16, { position: v3(0, ribbonEndY, 0) })
                .call(() => {
                    resolve("")
                    ribbonTween?.removeSelf();
                    ribbonTween = null;
                })
                .start();
            let ribbonTween1 = tween(commonUtil.getUIOpacity(this.view["ribbon"]))
                .to(0.16, { opacity: 255 })
                .call(() => {
                    ribbonTween1?.removeSelf();
                    ribbonTween1 = null;
                })
                .start();
        })
    }

    private async trophyAnim() {
        await Utils.timeOut(this, 0.08);
        this.lightAnim();
        if (this.view["trophy"]) {
            let trophyTween1 = tween(this.view["trophy"])
                .to(0.25, { scale: Vec3.ONE, position: v3(this.view["trophy"].position.x, -30, this.view["trophy"].position.z) })
                .call(() => {
                    trophyTween1?.removeSelf();
                    trophyTween1 = null;
                })
                .start();
            let trophyTween2 = tween(commonUtil.getUIOpacity(this.view["trophy"]))
                .to(0.17, { opacity: 255 })
                .call(() => {
                    trophyTween2?.removeSelf();
                    trophyTween2 = null;
                })
                .start();
        }
    }

    private lightAnim() {
        if (this.openState == 1 || this.openState == 2) return;
        this.lightTween = tween(this.view["light"])
            .to(0.2, { scale: Vec3.ONE })
            .call(() => {
                tween(this.view["light"])
                    .by(10, { angle: 720 })
                    .repeatForever()
                    .start();
                this.lightTween?.removeSelf();
                this.lightTween = null;
            }).start();
    }

    private async starAnim() {
        if (this.openState == 1 || this.openState == 2) return;
        await Utils.timeOut(this, 0.33);
        const hwArr: IStarObj[] = [{ w: 36, h: 34, x: -154, y: 121 }, { w: 52, h: 50, x: -183, y: 53 },
        { w: 26, h: 24, x: -96, y: 27.6 }, { w: 30, h: 28, x: 101.5, y: 29.5 },
        { w: 38, h: 36, x: 163.5, y: 61.5 }, { w: 38, h: 36, x: 141.5, y: 117.5 }]
        hwArr.forEach((value: IStarObj, idx) => {
            this.playStarAnim("star" + (idx + 1), value);
            tween(this.view["star" + (idx + 1)]).by(4, { angle: 720 })
                .repeatForever().start();
        })
    }

    private playStarAnim(name: string, obj: IStarObj) {
        this.starTween = tween(this.view[name])
            .to(0.5, { position: v3(obj.x, obj.y, this.view[name].position.z) })
            .call(() => {
                this.starTween?.removeSelf();
                this.starTween = null;
            })
            .start();

        this.starTween1 = tween(commonUtil.getUITransform(this.view[name]))
            .to(0.5, { width: obj.w, height: obj.h })
            .call(() => {
                this.starTween1?.removeSelf();
                this.starTween1 = null;
            })
            .start();
        // let starTween = tween(this.view[name]).to(0.5,
        //     { x: obj.x, y: obj.y, width: obj.w, height: obj.h }).call(() => {
        //         starTween.removeSelf();
        //         starTween = null;
        //     }).start();
    }


}
