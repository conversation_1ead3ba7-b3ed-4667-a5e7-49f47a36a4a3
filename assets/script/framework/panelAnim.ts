import { _decorator, sys, Node } from "cc";
import { AnimGatherMgr } from "./animGatherMgr";
import { IPanelAnim } from "./enum";
/**
 * superPanel 动画展示类
 * 使用方式：在脚本类中 继承superPanel
 * reserve 中设置 this.panelAnim.anim 对应参数
 * <AUTHOR>
 */
export class PanelAnim {
    /*** 默认动画   0 缩放 1 左右开合 2上下开合  3 上下移动 4 左右移动  10 缩放针对于弹框  11 向上 */
    public anim: IPanelAnim = {
        openIsAnim: false,
        openAnimType: -1,
        closeIsAnim: false,
        closeAnimType: -1
    };

    constructor(private org: any) { }

    /**
     * 播放动画
     * @param openType true 开界面  false 关闭界面
     * @returns 
     */
    public playPanelAnim(openType: boolean) {
        const hasAnim = this.isPlayPanelAnim(openType);
        if (!hasAnim) {
            openType ? this.openAnimFunc() : this.closeAnimFunc();
            return;
        }
        const animType = openType ? this.anim.openAnimType : this.anim.closeAnimType;
        switch (animType) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
                this.playAnimByCurType(openType, animType, this.org.node);
                break;
            // 针对于弹框类型10-20
            case 10:
            case 11:
                this.playAnimByCurType(openType, animType, this.org.view["contentArea"]);
                break;
            case 12:
                this.playAnimByCurType(openType, animType, [this.org.view["contentArea"], this.org.view["mask"]]);
                break;
            default:
                openType ? this.openAnimFunc() : this.closeAnimFunc();
                break;
        }
    }

    /**
     * 播放当前类型动画
     * @param openType 类型 false 代表关闭  true  代表代开界面
     * @param type 动画类型 和动画函数名息息相关
     */
    private playAnimByCurType(openType: boolean, type: number, node: Node | Array<Node>) {
        let key = "";
        if (openType) {
            key = "openPanelAnim_" + type
            AnimGatherMgr.instance[key](node).then(() => {
                this.openAnimFunc();
            }).catch(() => {
                this.openAnimFunc();
            });
        } else {
            key = "closePanelAnim_" + type
            AnimGatherMgr.instance[key](node).then(() => {
                this.closeAnimFunc();
            }).catch(() => {
                this.closeAnimFunc();
            });
        }
    }

    /**
     * 是否播放了动画
     * @param openType 类型 false 代表关闭  true  代表代开界面
     * @returns 返回值 true 有动画
     */
    private isPlayPanelAnim(openType: boolean) {
        if (openType && this.anim.openIsAnim) return true;
        if (!openType && this.anim.closeIsAnim) return true;
        return false;
    }

    /** 打开界面动画之后回调 */
    private openAnimFunc() {
        this.org.openAnimFunc();
    }

    /** 关闭界面动画之后回调 */
    private closeAnimFunc() {
        this.org.closeAnimFunc();
    }


}