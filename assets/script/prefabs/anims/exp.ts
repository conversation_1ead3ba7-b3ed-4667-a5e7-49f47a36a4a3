import { tween, v3, Vec3, _decorator } from 'cc';
import { BasePanel } from '../../framework/basePanel';
import { EventMgr } from '../../framework/eventMgr';
import { EventName } from '../../framework/eventName';
import { AudioMgr } from '../../manager/audioMgr';
import { commonUtil } from '../../utils/commonUtil';
const { ccclass } = _decorator;

@ccclass('exp')
export class exp extends BasePanel {

    private baseLabelStr = 'EXP              +  '

    onMessageEvent() {
        super.onMessageEvent();
        EventMgr.on(EventName.EVENT_EXP_FLY_ANI, this, this.playExpAnimation);
    }

    offMessageEvent() {
        super.offMessageEvent();
        EventMgr.off(EventName.EVENT_EXP_FLY_ANI, this, this.playExpAnimation);
    }

    /**
     * @en 播放显示动画 @en display exp animation
     */
    public async playExpAnimation(targetCoor: Vec3, multiple: number) {
        if (multiple > 0) {
            AudioMgr.instance.play('audio/sound_match')
            commonUtil.setPosition(this.node, targetCoor)
            this.tools.text('label', `${this.baseLabelStr}${multiple||0}`)
            tween(commonUtil.getUIOpacity(this.node))
                .to(.3, { opacity: 255 })
                .to(.4, {})
                .to(.3, { opacity: 0 })
                .start()
            
            tween(this.node)
                .to(.5, { position: v3(targetCoor.x, targetCoor.y + 80, targetCoor.z) })
                .start()
        }
    }
}
