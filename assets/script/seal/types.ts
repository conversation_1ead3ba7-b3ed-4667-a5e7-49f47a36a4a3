
export interface IGetUserInfoRes {
  userId: string
  nickName: string
  gender: number //0：未知 1：男 2：女
  portrait: string //头像
  portraitMask?: string // 头像边框
}
export interface IGetRoomInfoRes {
  roomId: string // 游戏房间唯一id
  ownerId: string // 房主id
  topPartHeight: number //顶部原生组件所占高度
  middlePartHeight: number //中间webview可点击高度
  bottomPartHeight: number //底部原生组件所占高度
}
export interface ILoginRes{
  code:string, // 临时凭证
  appId:string, // 
  appKey:string // 
}
export interface IGetRoomPageInfoRes {
  topPartHeight: number //顶部原生组件所占高度
  middlePartHeight: number //中间webview可点击高度
  bottomPartHeight: number //底部原生组件所占高度
}
export interface IGetSystemInfoRes {
  appId: string
  deviceId: string //设备ID
  model: string //设备 model
  platform: string // 平台
  system: string //iphone系统版本
  version: string //APP 版本
  SealsdkVersion: string //Seal游戏平台SDK版本号
  language: string //语言
  countryCode: string // 国家码
  env: number // 0 - 线上环境 / 1 - 预发环境 / 2 - 灯塔
  isPreload: boolean // 是否使用预加载
}

export enum GameStatusType {
  SEAL_GAME_STATUS_IDLE = 'SEAL_GAME_STATUS_IDLE', // 加载游戏后,玩家列表人数未满2人时同步这个状态；或者人数满人后再变为未满2人时需要再同步这个状态。
  SEAL_GAME_STATUS_READY = 'SEAL_GAME_STATUS_READY', // 当玩家列表有2人及以上人数时，同步这个状态。
  SEAL_GAME_STATUS_PLAYING = 'SEAL_GAME_STATUS_PLAYING', // 游戏进行中状态
  SEAL_GAME_STATUS_END = 'SEAL_GAME_STATUS_END' // 游戏结束状态
}


export enum PlayerType {
  SEAL_PLAYER_STATUS_IDLE = 'SEAL_PLAYER_STATUS_IDLE',
  SEAL_PLAYER_STATUS_JOINED = 'SEAL_PLAYER_STATUS_JOINED',
  SEAL_PLAYER_STATUS_PLAYING = 'SEAL_PLAYER_STATUS_PLAYING'
}

// 游戏音效状态
export interface IGameEffectStatus {
  status: 'success' | 'failed',
  music_switch_on: boolean,
  sound_effect_switch_on: boolean
}

declare global {
  interface Window {
    lz: any
    LizhiJSBridge: any
    cocosI18n: any
  }
}
