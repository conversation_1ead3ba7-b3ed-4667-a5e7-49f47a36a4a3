<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>mic-close.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{0,40},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mic.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,72}</string>
                <key>spriteSourceSize</key>
                <string>{72,72}</string>
                <key>textureRect</key>
                <string>{{0,112},{72,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mine-mic-disable.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{40,40}</string>
                <key>spriteSourceSize</key>
                <string>{40,40}</string>
                <key>textureRect</key>
                <string>{{0,0},{40,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>mine-mic.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{40,40}</string>
                <key>spriteSourceSize</key>
                <string>{40,40}</string>
                <key>textureRect</key>
                <string>{{40,0},{40,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>micAtlas.png</string>
            <key>size</key>
            <string>{80,184}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:415d3cbcd16a508dfe1634b8e63ed818:7fe0a1b85b4d3fdfd9a4637fb6efdd5f:9e56250182e2726b446135b893d0c094$</string>
            <key>textureFileName</key>
            <string>micAtlas.png</string>
        </dict>
    </dict>
</plist>
