import { Node, tween, Vec3 } from "cc";

export class AnimMgr {
    public static async move(node: Node, startPos: Vec3, endPos: Vec3) {
        return new Promise((resolve, reject) => {
            if (!node) {
                reject("not find node !");
                return;
            }
            node.setPosition(startPos);
            tween(node).to(0.25, { position: endPos }).call(() => {
                resolve("finish !");
            }).start();
        })
    }


}